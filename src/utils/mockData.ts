import { IMmembership } from '../common/interfaces';

export const rawData = {
  getWalletSubscription: {
    accountId: '6b17d4d2-747b-4e63-852c-88f9c6a0e0e5',
    agreementId: '45d6c667-f21d-4719-81e7-2b5dfb4f2188',

    billingItems: [
      {
        billingItemId: '651dce90-2497-4a13-9503-e16cccbb375b',
        planId: 'ac7e03e1-0da2-4515-8412-7ec556f32b8d',
        name: 'Energy Promotion',
        amount: '-1.00',
        currency: 'GBP',
      },
      {
        billingItemId: '651dce90-2497-4a13-9503-e16cccbb375b',
        planId: 'ac7e03e1-0da2-4515-8412-7ec556f32b8d',
        name: 'UK default plan',
        amount: '8.00',
        currency: 'GBP',
      },
    ],
    createdAt: '2023-07-03T14:41:31.711Z',
    customerId: 'bpPayTestWallet',
    deactivationDate: 'N/A',
    id: 'e943085d-be0c-4443-b606-02f53bb03921',
    merchantId: '312862',
    nextBillingDate: '2023-08-03T00:00:00.000Z',
    paymentMethodId: 'b271a813-4d3d-41ef-a797-3938d8c1468b',
    status: 'ACTIVE',
    statusReason: 'PAID',
    tenantId: 'bpme',
    useCase: 'price-match',
    discountExpiresAt: null,
  },
};
export const dataWithNullVaules = {
  getWalletSubscription: {
    ...rawData.getWalletSubscription,
    billingItems: null,
    nextBillingDate: null,
    discountExpiresAt: null,
  },
};

const commonMockMembership: Omit<IMmembership, 'membershipStartDate'> = {
  userType: 'SUBS-WALLET',
  membershipStatus: 'ACTIVE',
  membershipRequestCancelDate: null,
  membershipEndDate: '',
  membershipBillingCycleDate: '',
};

export const mockMemberships: Array<IMmembership> = [
  {
    ...commonMockMembership,
    membershipStartDate: '2024-09-30T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-10-01T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-10-02T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-10-09T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-10-03T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-10-04T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-10-05T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-10-06T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-10-07T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-10-08T00:00:00.000Z',
  },
];

const cancelledMockMembership: Omit<IMmembership, 'membershipStartDate'> = {
  userType: 'SUBS-WALLET',
  membershipStatus: 'CANCELLED',
  membershipRequestCancelDate: null,
  membershipEndDate: '',
  membershipBillingCycleDate: '',
};

const mockPAYGMembership: Omit<IMmembership, 'membershipStartDate'> = {
  userType: 'PAYG-Wallet',
  membershipStatus: 'CANCELLED',
  membershipRequestCancelDate: null,
  membershipEndDate: '',
  membershipBillingCycleDate: '',
};

export const mockMixedMemberships: Array<IMmembership> = [
  {
    ...mockPAYGMembership,
    membershipStartDate: '2024-11-18T00:00:00.000Z',
  },
  {
    ...commonMockMembership,
    membershipStartDate: '2024-09-30T00:00:00.000Z',
  },
  {
    ...cancelledMockMembership,
    membershipStartDate: '2024-10-30T00:00:00.000Z',
  },
  {
    ...cancelledMockMembership,
    membershipStartDate: '2024-10-02T00:00:00.000Z',
  },
];
