import outageBanner from './outageBanner';

describe('outageBanner', () => {
  it('should handle a json of outages', () => {
    expect(outageBanner({})).toStrictEqual({});
  });

  it('should return outage information when outage is enabled', () => {
    const types = {
      outage:
        '{"enabled": true,"id": 1,"title": "mock-outage","detail": "mock outage detail","body": "mock outage body information"}',
    };
    expect(outageBanner(types)).toStrictEqual(JSON.parse(types.outage));
  });

  it('should return empty if outage is not enabled', () => {
    const types = {
      outage:
        '{"enabled": false,"id": 1,"title": "mock-outage","detail": "mock outage detail","body": "mock outage body information"}',
    };

    expect(outageBanner(types)).toStrictEqual({});
  });
});
