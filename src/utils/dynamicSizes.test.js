import { dynamicHeight, dynamicWidth } from './dynamicSizes';

describe('Dynamic Sizes', () => {
  it('should return dynamic width', () => {
    expect(dynamicWidth(20)).toEqual(150);
    expect(dynamicWidth(40)).toEqual(300);
    expect(dynamicWidth(20.1)).toEqual(150.75);
    expect(dynamicWidth(0)).toEqual(0);
  });

  it('should return dynamic height', () => {
    expect(dynamicHeight(20)).toEqual(266.8);
    expect(dynamicHeight(40)).toEqual(533.6);
    expect(dynamicHeight(20.1)).toEqual(268.134);
    expect(dynamicHeight(0)).toEqual(0);
  });
});
