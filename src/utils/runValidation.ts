const runValidation = ({ schema, fields }: any): Promise<boolean> => {
  const fieldKeys = Object.keys(fields);
  const valuesObject = fieldKeys.reduce<object>(
    (obj: any, field: any) => ({
      ...obj,
      [field]: fields[field].value,
    }),
    {},
  );

  const clearErrors = () =>
    fieldKeys.forEach(
      (field) => fields[field].setError && fields[field].setError(false),
    );

  return schema
    .validate(valuesObject, { abortEarly: false })
    .then(() => {
      clearErrors();
      return true;
    })
    .catch((e: any) => {
      clearErrors();
      e.inner.forEach((error: any) => {
        const errorField = fieldKeys.find((field) => field === error.path);
        if (errorField && fields[errorField].setError) {
          fields[errorField].setError(error.message);
        }
      });
      return false;
    });
};

export default runValidation;
