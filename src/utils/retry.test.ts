import { requestWithRetry } from './retry';

jest.useFakeTimers();

describe('requestWithRetry', () => {
  const mockRequestPromise = jest.fn();
  const mockVars = {};

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should resolve the request successfully on the first attempt', async () => {
    mockRequestPromise.mockResolvedValueOnce('success');

    const result = await requestWithRetry(mockRequestPromise, mockVars);

    expect(result).toBe('success');
    expect(mockRequestPromise).toHaveBeenCalledTimes(1);
  });

  it('should resolve the request successfully after retries', async () => {
    mockRequestPromise
      .mockRejectedValueOnce(new Error('error'))
      .mockRejectedValueOnce(new Error('error'))
      .mockResolvedValueOnce('success');

    const result = await requestWithRetry(mockRequestPromise, mockVars);

    expect(result).toBe('success');
    expect(mockRequestPromise).toHaveBeenCalledTimes(3);
  });

  it('should throw an error after all retries have failed', async () => {
    mockRequestPromise
      .mockRejectedValueOnce(new Error('error'))
      .mockRejectedValueOnce(new Error('error'))
      .mockRejectedValueOnce(new Error('error'));

    await expect(
      requestWithRetry(mockRequestPromise, mockVars),
    ).rejects.toThrow('error');
    expect(mockRequestPromise).toHaveBeenCalledTimes(3);
  });
});
