import { TagStatus } from '@bp/rfid-mfe/dist/common/enums';

import {
  Countries,
  MandateStatus,
  MembershipStatus,
  SubscriptionScreenNames,
  TemporaryMandateStatus,
  UserCountry,
  UserTypeEnum,
} from '../common/enums';
import {
  IGetWalletSubscriptionResponse,
  IMmembership,
  IUserInfo,
  SubscriptionDetails,
} from '../common/interfaces';
import {
  formatCurrency,
  formatDate,
  formatDateMinusOneDay,
  formatDateSuffix,
  getAnalyticsPayload,
  getCurrency,
  getCurrentDate,
  getInitialRouteName,
  getLatestMembership,
  getOrdinalDay,
  getPhoneNumber,
  hasActiveMembership,
  hasCanceledMembership,
  hasRfidTag,
  processBillingData,
  processBillingItems,
} from './helpers';
import { mockMemberships, mockMixedMemberships, rawData } from './mockData';

const DEFAULT_PLAN = 'Default Plan';
const DISCOUNT_PLAN = 'Discount';
const validDate = '2024-11-14T00:00:00.000Z';

const mockMembership: IMmembership = {
  membershipStatus: MembershipStatus.INACTIVE,
  userType: UserTypeEnum.SUBS,
  membershipRequestCancelDate: '2023-12-30',
  membershipStartDate: '2024-10-10T00:00:00.000Z',
  membershipEndDate: '',
  membershipBillingCycleDate: '2024-11-18T00:00:00.000Z',
};

const mockCancelDate = '2023-12-31';
const mockStartDate = '2024-10-14T00:00:00.000Z';

const createMockUser = (overrides: Partial<IUserInfo> = {}): IUserInfo => ({
  userId: '123',
  type: UserTypeEnum.NEW,
  gocardless: { mandateStatus: MandateStatus.INACTIVE, mandateId: 'mock' },
  balance: 0,
  country: UserCountry.UK,
  membership: [],
  tagIds: [],
  status: '',
  latestMembership: mockMembership,
  ...overrides,
});

const createMockSubscriptionDetails = (
  billingItems: Array<{
    billingItemId: string;
    planId: string;
    name: string;
    amount: string | number;
    currency: string;
  }>,
): SubscriptionDetails => ({
  id: '',
  customerId: '',
  paymentMethodId: '',
  merchantId: '',
  tenantId: '',
  useCase: '',
  createdAt: '',
  status: '',
  statusReason: '',
  cancelledOn: '',
  nextBillingDate: null,
  accountId: '',
  agreementId: '',
  deactivationDate: '',
  discountExpiresAt: null,
  billingItems,
});

const createMockWalletSubscriptionResponse = (
  subscriptionDetails: SubscriptionDetails,
): IGetWalletSubscriptionResponse => ({
  getWalletSubscription: subscriptionDetails,
});

describe('Helpers', () => {
  test('getInitialRouteName should return correct screen for different user types', () => {
    expect(
      getInitialRouteName(createMockUser({ type: UserTypeEnum.SUBS })),
    ).toBe(SubscriptionScreenNames.GoCardlessMembership);
    expect(
      getInitialRouteName(
        createMockUser({
          type: UserTypeEnum.SUBS_WALLET,
          membership: [
            {
              membershipStatus: MembershipStatus.INACTIVE,
              userType: UserTypeEnum.SUBS,
              membershipRequestCancelDate: mockCancelDate,
              membershipStartDate: mockStartDate,
              membershipEndDate: '',
              membershipBillingCycleDate: '2024-11-17T00:00:00.000Z',
            },
          ],
        }),
      ),
    ).toBe(SubscriptionScreenNames.SubscriptionPaused);
    expect(
      getInitialRouteName(
        createMockUser({
          type: UserTypeEnum.SUBS_WALLET,
          membership: [
            {
              membershipStatus: MembershipStatus.ACTIVE,
              userType: UserTypeEnum.SUBS_WALLET,
              membershipRequestCancelDate: mockCancelDate,
              membershipStartDate: mockStartDate,
              membershipEndDate: '',
              membershipBillingCycleDate: '2024-11-16T00:00:00.000Z',
            },
          ],
        }),
      ),
    ).toBe(SubscriptionScreenNames.SubscriptionEnding);
    expect(
      getInitialRouteName(
        createMockUser({
          country: UserCountry.DE,
          type: UserTypeEnum.SUBS_WALLET,
          membership: [
            {
              membershipStatus: MembershipStatus.ACTIVE,
              userType: UserTypeEnum.SUBS_WALLET,
              membershipRequestCancelDate: mockCancelDate,
              membershipStartDate: mockStartDate,
              membershipEndDate: '',
              membershipBillingCycleDate: '2024-11-15T00:00:00.000Z',
            },
          ],
        }),
      ),
    ).toBe(SubscriptionScreenNames.SubscriptionEnding);

    expect(
      getInitialRouteName(
        createMockUser({
          country: UserCountry.DE,
          type: UserTypeEnum.SUBS_WALLET,
          membership: [
            {
              membershipStatus: MembershipStatus.ACTIVE,
              userType: UserTypeEnum.SUBS_WALLET,
              membershipRequestCancelDate: null,
              membershipStartDate: mockStartDate,
              membershipEndDate: '',
              membershipBillingCycleDate: '2024-11-14T00:00:00.000Z',
            },
          ],
        }),
      ),
    ).toBe(SubscriptionScreenNames.MyComfortMembership);

    expect(
      getInitialRouteName(createMockUser({ type: UserTypeEnum.SUBS_WALLET })),
    ).toBe(SubscriptionScreenNames.MySubscription);
    expect(
      getInitialRouteName(
        createMockUser({
          gocardless: {
            mandateStatus: MandateStatus.PENDING,
            mandateId: 'mock',
          },
        }),
      ),
    ).toBe(SubscriptionScreenNames.PendingMembership);
    expect(getInitialRouteName(createMockUser())).toBe(
      SubscriptionScreenNames.JoinGoCardless,
    );
  });

  test('getAnalyticsPayload should return correct payload', () => {
    const user = createMockUser({
      balance: 100,
      gocardless: { mandateStatus: MandateStatus.ACTIVE, mandateId: 'mock' },
      // @ts-expect-error
      tagIds: [1, 2, 3],
    });
    expect(getAnalyticsPayload(user)).toEqual({
      account_balance: 100,
      gocardless_mandate_status: MandateStatus.ACTIVE,
      tag_ids: [1, 2, 3],
    });
    expect(getAnalyticsPayload(undefined)).toEqual({
      account_balance: 0,
      gocardless_mandate_status: MandateStatus.INACTIVE,
      tag_ids: [],
    });
  });

  test('getPhoneNumber should return correct number for different countries', () => {
    expect(getPhoneNumber(createMockUser({ country: UserCountry.DE }))).toBe(
      '+49 800 1353511',
    );
    expect(getPhoneNumber(createMockUser({ country: UserCountry.NL }))).toBe(
      '+31 85 002 22 86',
    );
    expect(getPhoneNumber(createMockUser({ country: UserCountry.ES }))).toBe(
      '+34 900 948 088',
    );
    expect(getPhoneNumber(createMockUser({ country: UserCountry.UK }))).toBe(
      '+44 ************',
    );
    expect(
      getPhoneNumber(createMockUser({ country: 'UNKNOWN' as UserCountry })),
    ).toBe('');
  });

  test('formatDate should format date correctly', () => {
    expect(formatDate('2023-05-15')).toBe('15/05/2023');
  });

  test('formatDateSuffix should add correct suffix to date', () => {
    expect(formatDateSuffix('2023-05-01')).toBe('1st');
    expect(formatDateSuffix('2023-05-02')).toBe('2nd');
    expect(formatDateSuffix('2023-05-03')).toBe('3rd');
    expect(formatDateSuffix('2023-05-04')).toBe('4th');
    expect(formatDateSuffix('2023-05-11')).toBe('11th');
    expect(formatDateSuffix('2023-05-21')).toBe('21st');
  });

  test('getCurrency should return correct currency for different countries', () => {
    expect(getCurrency(Countries.UK)).toBe('GBP');
    expect(getCurrency(Countries.DE)).toBe('EUR');
    expect(getCurrency(undefined)).toBe('EUR');
  });

  test('processBillingData should process billing data correctly', () => {
    const mockData = createMockWalletSubscriptionResponse(
      createMockSubscriptionDetails([
        {
          billingItemId: '1',
          planId: 'plan1',
          name: DEFAULT_PLAN,
          amount: '10.99',
          currency: 'EUR',
        },
        {
          billingItemId: '2',
          planId: 'plan2',
          name: DISCOUNT_PLAN,
          amount: '2.00',
          currency: 'EUR',
        },
      ]),
    );

    const result = processBillingData(mockData);

    expect(result.subscription).toEqual({
      billingItemId: '1',
      planId: 'plan1',
      name: DEFAULT_PLAN,
      amount: 10.99,
      currency: 'EUR',
    });
    expect(result.discounts).toBe(2);
    expect(result.totalValue).toBe(8.99);
    expect(result.currency).toBe('EUR');
  });

  test('processBillingData should handle string and number amounts', () => {
    const mockData = createMockWalletSubscriptionResponse(
      createMockSubscriptionDetails([
        {
          billingItemId: '1',
          planId: 'plan1',
          name: DEFAULT_PLAN,
          amount: '10.99',
          currency: 'EUR',
        },
        {
          billingItemId: '2',
          planId: 'plan2',
          name: DISCOUNT_PLAN,
          amount: 5,
          currency: 'EUR',
        },
      ]),
    );

    const result = processBillingData(mockData);
    expect(result.totalValue).toBe(5.99);
  });

  test('processBillingData should handle invalid amount', () => {
    const consoleSpy = jest
      .spyOn(console, 'error')
      .mockImplementation(() => {});
    const mockData = createMockWalletSubscriptionResponse(
      createMockSubscriptionDetails([
        {
          billingItemId: '1',
          planId: 'plan1',
          name: 'Invalid Plan',
          amount: 'invalid' as any,
          currency: 'EUR',
        },
      ]),
    );

    processBillingData(mockData);
    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to parse amount for item: Invalid Plan',
    );
    consoleSpy.mockRestore();
  });

  test('formatCurrency should format currency correctly', () => {
    expect(formatCurrency({ amount: 10.99, currency: 'EUR' })).toBe('€10.99');
    expect(formatCurrency({ amount: 10.99, currency: 'GBP' })).toBe('£10.99');
    expect(
      formatCurrency({ amount: 10.99, currency: 'USD', fractionDigits: 0 }),
    ).toBe('US$10.99');
  });

  test('formatCurrency should throw error when currency is undefined', () => {
    expect(() => formatCurrency({ amount: 10.99 })).toThrow(
      'Currency is undefined or null',
    );
  });

  test('getCurrentDate should return date in correct format', () => {
    const dateSpy = jest.spyOn(Date.prototype, 'getDate').mockReturnValue(15);
    const monthSpy = jest.spyOn(Date.prototype, 'getMonth').mockReturnValue(4);
    const yearSpy = jest
      .spyOn(Date.prototype, 'getFullYear')
      .mockReturnValue(2023);

    expect(getCurrentDate()).toBe('15/05/2023');

    dateSpy.mockRestore();
    monthSpy.mockRestore();
    yearSpy.mockRestore();
  });

  test('getInitialRouteName should handle TemporaryMandateStatus', () => {
    const user: IUserInfo = createMockUser({
      gocardless: {
        mandateStatus: TemporaryMandateStatus.TEMPORARY_PENDING,
        mandateId: 'mock',
      },
    });
    expect(getInitialRouteName(user)).toBe(
      SubscriptionScreenNames.PendingMembership,
    );
  });

  describe('getLatestMembership', () => {
    it('should return latest membership', () => {
      const response = getLatestMembership(mockMemberships);

      expect(response).toStrictEqual({
        membershipBillingCycleDate: '',
        membershipEndDate: '',
        membershipRequestCancelDate: null,
        membershipStartDate: '2024-10-09T00:00:00.000Z',
        membershipStatus: MembershipStatus.ACTIVE,
        userType: UserTypeEnum.SUBS_WALLET,
      });
    });

    it('should return latest active membership', () => {
      const response = getLatestMembership(mockMixedMemberships);

      expect(response).toStrictEqual({
        membershipBillingCycleDate: '',
        membershipEndDate: '',
        membershipRequestCancelDate: null,
        membershipStartDate: '2024-09-30T00:00:00.000Z',
        membershipStatus: MembershipStatus.ACTIVE,
        userType: UserTypeEnum.SUBS_WALLET,
      });
    });
  });

  describe('hasActiveMembership', () => {
    it('should return true if latest membership status is ACTIVE', () => {
      const response = hasActiveMembership(mockMixedMemberships);

      expect(response).toStrictEqual(true);
    });
    it('should return false if latest membership status is not ACTIVE', () => {
      const response = hasActiveMembership([
        {
          userType: UserTypeEnum.SUBS_WALLET,
          membershipStatus: MembershipStatus.CANCELLED,
          membershipStartDate: '2024-09-30T00:00:00.000Z',
          membershipRequestCancelDate: null,
          membershipEndDate: '',
          membershipBillingCycleDate: '2024-10-30T00:00:00.000Z',
        },
      ]);

      expect(response).toStrictEqual(false);
    });
  });

  describe('hasCanceledMembership', () => {
    it('should return true if latest membership was canceled', () => {
      const response = hasCanceledMembership([
        {
          userType: UserTypeEnum.SUBS_WALLET,
          membershipStatus: MembershipStatus.ACTIVE,
          membershipStartDate: '2024-09-31T00:00:00.000Z',
          membershipRequestCancelDate: '2024-10-30T00:00:00.000Z',
          membershipEndDate: '',
          membershipBillingCycleDate: '2024-11-30T00:00:00.000Z',
        },
      ]);

      expect(response).toStrictEqual(true);
    });

    it('should return false if latest membership was not canceled', () => {
      const response = hasCanceledMembership(mockMemberships);

      expect(response).toStrictEqual(false);
    });
  });

  describe('getOrdinalDay', () => {
    test('getOrdinalDay should return the correct day ', () => {
      expect(getOrdinalDay(validDate)).toBe('14th');
      expect(getOrdinalDay(null)).toBe('Invalid date');
      expect(getOrdinalDay('not a date')).toBe('Invalid date');
    });
  });

  describe('processBillingItems', () => {
    test('processBillingItems should return the correct amount ', () => {
      const result = processBillingItems(
        rawData.getWalletSubscription.billingItems,
      );
      expect(result.amount).toBe(8);
      expect(result.discount).toBe(-1);
      expect(result.totalAmount).toBe(7);
    });
  });

  describe('formatDateMinusOneDay', () => {
    it('should return the previous day in dd/mm/yyyy format', () => {
      const input = '2024-04-15T00:00:00.000Z';
      const result = formatDateMinusOneDay(input);
      expect(result).toBe('14/04/2024');
    });

    it('should handle month changes correctly', () => {
      const input = '2024-05-01T00:00:00.000Z';
      const result = formatDateMinusOneDay(input);
      expect(result).toBe('30/04/2024');
    });

    it('should handle year changes correctly', () => {
      const input = '2025-01-01T00:00:00.000Z';
      const result = formatDateMinusOneDay(input);
      expect(result).toBe('31/12/2024');
    });

    it('should correctly format single-digit days and months with leading zeros', () => {
      const input = '2025-03-01T00:00:00.000Z'; // Should return 28/02/2025 (not a leap year)
      const result = formatDateMinusOneDay(input);
      expect(result).toBe('28/02/2025');
    });
  });

  describe('hasRfidTag', () => {
    const RFID_TAG_NOTE = 'physical-rfid';
    it('should return true if user has an RFID tag', () => {
      const user = createMockUser({
        tagIds: [
          {
            tagId: '1',
            tagStatus: TagStatus.ACTIVE,
            tagNotes: RFID_TAG_NOTE,
          },
        ],
      });
      expect(hasRfidTag(user)).toBe(true);
    });

    it('should return false if user does not have an RFID tag', () => {
      const user = createMockUser({ tagIds: [] });
      expect(hasRfidTag(user)).toBe(false);
    });

    it('should return false if user has an RFID tag with an invalid status', () => {
      const user = createMockUser({
        tagIds: [
          {
            tagId: '1',
            tagStatus: 'INVALID' as TagStatus,
            tagNotes: RFID_TAG_NOTE,
          },
        ],
      });
      expect(hasRfidTag(user)).toBe(false);
    });

    it('should return true if user has an RFID tag with an valid status', () => {
      const user = createMockUser({
        tagIds: [
          {
            tagId: '1',
            tagStatus: TagStatus.CUSTOMER_REQUESTED,
            tagNotes: 'physical-rfid',
          },
        ],
      });
      expect(hasRfidTag(user)).toBe(true);
    });
  });
});
