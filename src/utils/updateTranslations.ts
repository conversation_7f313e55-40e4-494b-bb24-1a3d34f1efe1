import { TRANSLATIONS } from '../translations';

export type ITranslation = typeof TRANSLATIONS.en_GB;

export type IReplaceStrings = {
  [key: string]: string;
};

export const updateTranslations = (
  selectedTranslation: ITranslation | IReplaceStrings,
  replaceStrings: Partial<IReplaceStrings>,
) => {
  let jsonString = JSON.stringify(selectedTranslation, null, 2);

  Object.entries(replaceStrings).forEach(([key, value]) => {
    if (value) {
      const searchValue = `{{${key}}}`;
      const replaceValue = value;
      jsonString = jsonString.split(searchValue).join(replaceValue);
    }
  });

  return JSON.parse(jsonString);
};
