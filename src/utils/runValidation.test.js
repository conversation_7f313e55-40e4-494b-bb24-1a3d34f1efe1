import * as yup from 'yup';

import runValidation from './runValidation';

const testIsRequiredError = 'Test is required.';
const testMaxLengthError = 'Test can be at most 10 characters.';
const testMatchesError = 'Test can only include numbers and letters.';

const testSchema = () => {
  yup.setLocale({});
  return yup.object().shape({
    test: yup
      .string()
      // Alphanumeric only
      .matches('^[a-zA-Z0-9_]*$', {
        message: testMatchesError,
      })
      .max(10, testMaxLengthError)
      .trim()
      .required(testIsRequiredError),
  });
};

describe('runValidation function', () => {
  it('should validate a valid entry', async () => {
    const testValue = 'Test';
    const setTestError = jest.fn();
    const testValidation = {
      schema: testSchema(),
      fields: {
        test: {
          value: testValue,
          setError: setTestError,
        },
      },
    };
    const isValid = await runValidation(testValidation);
    expect(isValid).toEqual(true);
    expect(setTestError).toBeCalledWith(false);
  });
  it('should give required error on empty test field', async () => {
    const testValue = '';
    const setTestError = jest.fn();
    const testValidation = {
      schema: testSchema(),
      fields: {
        test: {
          value: testValue,
          setError: setTestError,
        },
      },
    };
    const isValid = await runValidation(testValidation);
    expect(isValid).toEqual(false);
    expect(setTestError).toBeCalledWith(testIsRequiredError);
  });
  it('should be invalid but ignore setError field if it does not exist', async () => {
    const testValue = '';
    const setTestError = jest.fn();
    const testValidation = {
      schema: testSchema(),
      fields: {
        test: {
          value: testValue,
        },
      },
    };
    const isValid = await runValidation(testValidation);
    expect(isValid).toEqual(false);
    expect(setTestError).not.toBeCalled();
  });
});
