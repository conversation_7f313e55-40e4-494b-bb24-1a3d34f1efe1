import { SupportedLocale } from '@bp/pulse-shared-types/lib/enums/SupportedLocale';
import { SupportedLocales } from '@bp/pulse-shared-types/lib/types/SupportedLocales';
import { TagStatus } from '@bp/rfid-mfe/dist/common/enums';
import { orderBy } from 'lodash';
import moment from 'moment';

import { EventPayloadType } from '../analytics/types';
import {
  Countries,
  Currency,
  MandateStatus,
  MembershipStatus,
  SubscriptionScreenNames,
  TemporaryMandateStatus,
  UserCountry,
  UserTypeEnum,
} from '../common/enums';
import {
  IGetWalletSubscriptionResponse,
  IMmembership,
  IUserInfo,
  Subscription_BillingItems,
  Tags,
} from '../common/interfaces';

export const filteredMemberships = (memberships: Array<IMmembership>) =>
  memberships.filter(
    (membership) => membership.userType === UserTypeEnum.SUBS_WALLET,
  );

export const hasCanceledMembership = (memberships: Array<IMmembership>) => {
  const latestMembership = getLatestMembership(
    filteredMemberships(memberships),
  );
  return latestMembership?.membershipRequestCancelDate !== null;
};
export const hasActiveMembership = (memberships: Array<IMmembership>) => {
  const latestMembership = getLatestMembership(
    filteredMemberships(memberships),
  );
  return latestMembership?.membershipStatus === MembershipStatus.ACTIVE;
};

export const hasInactiveMembership = (
  memberships: Array<IMmembership> | undefined,
) => {
  return memberships?.some(
    (membership) =>
      membership.userType === UserTypeEnum.SUBS_WALLET &&
      membership.membershipStatus === MembershipStatus.INACTIVE,
  );
};

export const getInitialRouteName = (user: IUserInfo) => {
  const hasCancelDate = hasCanceledMembership(user.membership || []);
  const hasActiveStatus = hasActiveMembership(user.membership || []);

  if (
    user.type === UserTypeEnum.SUBS_WALLET &&
    hasCancelDate &&
    hasActiveStatus
  ) {
    return SubscriptionScreenNames.SubscriptionEnding;
  }

  if (
    user.type === UserTypeEnum.SUBS_WALLET &&
    user.membership?.some(
      (membership) => membership.membershipStatus === MembershipStatus.INACTIVE,
    )
  ) {
    return SubscriptionScreenNames.SubscriptionPaused;
  }

  if (user.country === UserCountry.DE) {
    if (user.type === UserTypeEnum.SUBS_WALLET && hasActiveStatus) {
      return SubscriptionScreenNames.MyComfortMembership;
    }
    return SubscriptionScreenNames.MembershipOnboard;
  }

  if (user.type === UserTypeEnum.SUBS) {
    return SubscriptionScreenNames.GoCardlessMembership;
  }

  if (user.type === UserTypeEnum.SUBS_WALLET) {
    return SubscriptionScreenNames.MySubscription;
  }

  if (
    user.gocardless?.mandateStatus === MandateStatus.PENDING ||
    user.gocardless?.mandateStatus === TemporaryMandateStatus.TEMPORARY_PENDING
  ) {
    return SubscriptionScreenNames.PendingMembership;
  }

  return SubscriptionScreenNames.JoinGoCardless;
};

export const getAnalyticsPayload = (
  userInfo: IUserInfo | undefined,
): EventPayloadType => ({
  account_balance: userInfo?.balance || 0,
  gocardless_mandate_status:
    userInfo?.gocardless?.mandateStatus || MandateStatus.INACTIVE,
  tag_ids: userInfo?.tagIds || [],
});

export const getAnalyticsPayloadForCancel = (
  userInfo: IUserInfo | undefined,
  offerCode: string,
): EventPayloadType => ({
  account_balance: userInfo?.balance || 0,
  gocardless_mandate_status:
    userInfo?.gocardless?.mandateStatus || MandateStatus.INACTIVE,
  tag_ids: userInfo?.tagIds || [],
  offer_code: offerCode,
});

export const getAnalyticsPayloadPaygWallet = (
  userInfo: IUserInfo | undefined,
  resubscribed: boolean | undefined,
): {
  tag_ids: Array<Tags>;
  first_time_subscribing: boolean;
} => ({
  tag_ids: userInfo?.tagIds || [],
  first_time_subscribing: !resubscribed,
});

export const getAnalyticsPayloadPaygWalletClick = (
  userInfo: IUserInfo | undefined,
  resubscribed: boolean | undefined,
  paymentMedthods: boolean,
): {
  tag_ids: Array<Tags>;
  first_time_subscribing: boolean;
  payment_methods: boolean;
} => ({
  tag_ids: userInfo?.tagIds || [],
  first_time_subscribing: !resubscribed,
  payment_methods: paymentMedthods,
});

export const getPhoneNumber = (user: IUserInfo) => {
  if (user.country === UserCountry.DE) {
    return '+49 800 1353511';
  }
  if (user.country === UserCountry.NL) {
    return '+31 85 002 22 86';
  }
  if (user.country === UserCountry.ES) {
    return '+34 900 948 088';
  }
  if (user.country === UserCountry.UK) {
    return '+44 ************';
  } else {
    return '';
  }
};

export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

export const formatDateMinusOneDay = (dateString: string) => {
  const date = new Date(dateString);
  date.setDate(date.getDate() - 1);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

const getSuffix = (date: string) => {
  const suffixes = {
    ['1']: 'st',
    ['21']: 'st',
    ['31']: 'st',
    ['2']: 'nd',
    ['22']: 'nd',
    ['3']: 'rd',
    ['23']: 'rd',
  };
  return suffixes[date as keyof typeof suffixes] || 'th';
};

export const getDateWithSuffix = (date: string, locale?: string) => {
  const dateObject = new Date(date);
  const day = dateObject.getDate().toString();
  return locale === SupportedLocale.DE_DE ? day : day + getSuffix(day);
};

export const processBillingData = (
  data: IGetWalletSubscriptionResponse,
): {
  subscription: Subscription_BillingItems | null;
  discounts: number;
  totalValue: number;
  currency: string;
} => {
  const subscriptionDetails = data.getWalletSubscription;
  const billingItems = subscriptionDetails
    ? subscriptionDetails.billingItems
    : null;

  let subscription: Subscription_BillingItems | null = null;
  let totalDiscount: number = 0;
  let totalValue: number = 0;
  let currency: string = '';

  billingItems &&
    billingItems.forEach((item: Subscription_BillingItems) => {
      let amount: number = 0;
      if (typeof item.amount === 'string') {
        const parsedAmount = parseFloat(item.amount);
        if (!isNaN(parsedAmount)) {
          amount = parsedAmount;
        } else {
          console.error(`Failed to parse amount for item: ${item.name}`);
        }
      } else if (typeof item.amount === 'number') {
        amount = item.amount;
      } else {
        console.error(`Invalid amount for item: ${item.name}`);
      }

      if (item.name.toLowerCase().includes('default')) {
        subscription = {
          billingItemId: item.billingItemId,
          planId: item.planId,
          name: item.name,
          amount: amount,
          currency: item.currency,
        };
        totalValue += amount;
      } else {
        totalDiscount += amount;
      }
      if (!currency && item.currency) {
        currency = item.currency;
      }
    });

  totalValue -= totalDiscount;

  const discounts = totalDiscount;
  return { subscription, discounts, totalValue, currency };
};

export const formatCurrency = ({
  amount,
  currency,
  fractionDigits = 2,
  locale = SupportedLocale.EN_GB,
}: {
  amount: number;
  currency?: string;
  fractionDigits?: number;
  locale?: SupportedLocales;
}) => {
  if (!currency) {
    throw new Error('Currency is undefined or null');
  }

  const formatter = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: fractionDigits,
  });
  const formattedAmount = formatter.format(amount);

  // Prepend the currency symbol to the formatted amount
  return `${formattedAmount}`;
};
export const formatCurrencyWithSymbolAtEnd = (
  amount = 0,
  numberFormat = 'de-DE',
  currency = 'EUR',
  fractionDigits = 2,
): string => {
  const currencyFormatter = new Intl.NumberFormat(numberFormat, {
    style: 'currency',
    currency,
    minimumFractionDigits: fractionDigits,
  });
  // Format the amount
  const formattedAmount = currencyFormatter.format(amount);

  // Remove space between amount and currency symbol
  const result = formattedAmount.replace(/\s/g, '');

  return `${result}`;
};

export const formatDateSuffix = (dateString: string) => {
  // Parse the date string to get the day
  const date = new Date(dateString);
  const day = date.getDate();

  // Determine the suffix based on the day
  let suffix: string;
  if (day % 10 === 1 && day !== 11) {
    suffix = 'st';
  } else if (day % 10 === 2 && day !== 12) {
    suffix = 'nd';
  } else if (day % 10 === 3 && day !== 13) {
    suffix = 'rd';
  } else {
    suffix = 'th';
  }

  // Format the date with the suffix
  return `${day}${suffix}`;
};
export const getCurrency = (country?: string) => {
  if (country === Countries.UK) {
    return Currency.GBP;
  } else {
    return Currency.EUR;
  }
};

export const getAccessibilityLabelForCurrency = (
  amount: number,
  currency?: string,
): string => {
  const wholeAmount = Math.floor(amount);
  const fractionAmount = Math.round((amount - wholeAmount) * 100);

  let wholeUnit = '';
  let fractionUnit = '';

  switch (currency) {
    case Currency.GBP:
      wholeUnit = wholeAmount === 1 ? 'pound' : 'pounds';
      fractionUnit = fractionAmount === 1 ? 'penny' : 'pence';
      break;
    case Currency.EUR:
      wholeUnit = wholeAmount === 1 ? 'euro' : 'euros';
      fractionUnit = fractionAmount === 1 ? 'cent' : 'cents';
      break;
    default:
      return 'Unsupported currency';
  }

  if (fractionAmount === 0) {
    return `${wholeAmount} ${wholeUnit}`;
  } else if (wholeAmount === 0) {
    return `${fractionAmount} ${fractionUnit}`;
  } else {
    return `${wholeAmount} ${wholeUnit} and ${fractionAmount} ${fractionUnit}`;
  }
};

export const isValidDate = (dateString: string | undefined | null) => {
  if (!dateString) {
    return false;
  }
  const date = moment(dateString);
  return date.isValid();
};

export const getOrdinalDay = (dateString: string | undefined | null) => {
  if (!dateString || !isValidDate(dateString)) {
    return 'Invalid date';
  }
  const day = new Date(dateString).getUTCDate();

  const suffix = (n: number) => {
    if (n > 3 && n < 21) {
      return 'th';
    }
    switch (n % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  };

  return `${day}${suffix(day)}`;
};
export const getCurrentDate = () => {
  const newDate = new Date();
  const day = String(newDate.getDate()).padStart(2, '0');
  const month = String(newDate.getMonth() + 1).padStart(2, '0');
  const year = newDate.getFullYear();
  return `${day}/${month}/${year}`;
};

export const getLatestMembership = (memberships: Array<IMmembership>) =>
  orderBy(
    memberships,
    [
      ({ membershipStatus }) => (membershipStatus === 'ACTIVE' ? 1 : 0), // Active memberships first
      ({ membershipStartDate }) => membershipStartDate,
    ],
    ['desc', 'desc'],
  )[0];

export const processBillingItems = (
  billingItems: Subscription_BillingItems[] | null | undefined,
) => {
  const ukItem = billingItems?.find((item) =>
    item.name.toLowerCase().includes('default'),
  );
  const offerItem =
    billingItems?.length === 2
      ? billingItems.find(
          (item) => !item.name.toLowerCase().includes('default'),
        )
      : null;

  const amount = ukItem ? parseFloat(ukItem.amount.toString()) : 0;
  const discount = offerItem ? parseFloat(offerItem.amount.toString()) : 0;

  return { amount, discount, totalAmount: amount + discount };
};

export const getCountrySpecificText = (
  t: any,
  keyPath?: string,
  userCountry?: string,
): string => {
  if (!t || !keyPath) {
    return '';
  }

  const countryKeyPath = `${keyPath}_${userCountry}`;

  const getValue = (translate: any, path: string) =>
    path.split('.').reduce((acc, part) => acc && acc[part], translate);

  return getValue(t, countryKeyPath) || getValue(t, keyPath) || '';
};

export const POLLING_INTERVAL = 4000;

const isValidRfidTag = (tag: Tags): boolean => {
  const RFID_TAG_NOTE = 'physical-rfid';
  if (
    tag.tagStatus === TagStatus.TERMINATED ||
    tag.tagStatus === TagStatus.PENDING_TERMINATION ||
    tag.tagStatus === TagStatus.SUPPLIER_FAILED ||
    tag.tagStatus === TagStatus.SUPPLIER_REJECTED
  ) {
    return false;
  }
  return (
    tag.tagNotes?.toLowerCase() === RFID_TAG_NOTE &&
    Object.values(TagStatus).includes(tag.tagStatus as TagStatus)
  );
};

export const hasRfidTag = (userInfo: IUserInfo | undefined) => {
  try {
    if (!userInfo || !userInfo.tagIds) {
      return false;
    }
    return userInfo.tagIds.some((tagId) => isValidRfidTag(tagId));
  } catch (error) {
    console.error('Error in hasRfidTag', error);
    return false;
  }
};
