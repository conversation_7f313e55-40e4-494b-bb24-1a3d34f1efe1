import {
  createNavigationContainerRef,
  NavigationContainer as NavContainer,
} from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import React, { useMemo } from 'react';
import { Pressable, Text } from 'react-native';

import { useHostNavigation } from '../providers/HostNavigationProvider';
import { TRANSLATIONS } from '../translations';

export const mockNavigation = createNavigationContainerRef();
const MockStack = createStackNavigator();

const mockTranslations = TRANSLATIONS.en_GB;

export const TestScreen = ({ navigation }: any) => {
  const { navigate, canGoBack } = useHostNavigation();

  const currentScreen = useMemo(
    () => navigation?.getCurrentRoute(),
    [navigation],
  );
  const otherScreen = useMemo(
    () => (currentScreen === 'Screen1' ? 'Screen2' : 'Screen1'),
    [currentScreen],
  );

  return (
    <>
      {canGoBack?.() ? (
        <Pressable onPress={() => navigate?.(currentScreen)}>
          <Text testID="goBack">
            {mockTranslations.gocardless.text.GO_BACK}
          </Text>
        </Pressable>
      ) : null}
      <Pressable
        onPress={() => navigate?.(otherScreen)}
        testID="navigateScreen"
      >
        <Text>{mockTranslations.gocardless.text.NAVIGATE}</Text>
      </Pressable>
    </>
  );
};

const ScreenComponent = (navigation: any) =>
  useMemo(() => () => <TestScreen navigation={navigation} />, [navigation]);
export const MockScreens = ({ navigation }: any) => {
  return (
    <MockStack.Navigator>
      <MockStack.Screen
        component={ScreenComponent(navigation)}
        name="Screen1"
      />
      <MockStack.Screen
        component={ScreenComponent(navigation)}
        name="Screen2"
      />
    </MockStack.Navigator>
  );
};

export const MockNavigationContainer = ({ navigation, children }: any) => (
  <NavContainer ref={navigation}>{children}</NavContainer>
);
