import {
  IReplaceStrings,
  updateTranslations,
} from '../utils/updateTranslations';
import mockTranslation from './mockTranslation.json';

describe('updateTranslations', () => {
  it('should replace placeholders with values from replaceStrings', () => {
    const replaceStrings: IReplaceStrings = {
      brand: 'Aral',
    };

    const updatedTranslationSet = updateTranslations(
      mockTranslation,
      replaceStrings,
    );
    expect(updatedTranslationSet).toEqual({
      key1: 'Aral',
      key2: 'value2',
      key3: 'Aral',
    });
  });
});
