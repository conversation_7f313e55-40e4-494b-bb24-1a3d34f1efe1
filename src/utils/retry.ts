/**
 * Make a request and retry it x number of times
 *
 * @param variables The request variables
 * @param retries Optionally specify the number of retries
 * @param retriesCount The recursive current retry
 *
 * @returns The response of the request or an error
 *
 */
export async function requestWithRetry<
  F extends (variables: any, scope?: string) => any,
>(
  requestPromise: F,
  variables: any,
  retries = 3,
  retriesCount = 0,
): Promise<ReturnType<F>> {
  try {
    return await requestPromise({ variables: variables });
  } catch (err) {
    const updatedCount = retriesCount + 1;
    if (updatedCount >= retries) {
      console.log(`All ${retries} retries for ${requestPromise} failed`);
      throw err;
    }
    return requestWithRetry(requestPromise, variables, retries, updatedCount);
  }
}
