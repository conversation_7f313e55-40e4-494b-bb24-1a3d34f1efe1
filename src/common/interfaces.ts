import { ApolloQueryResult } from '@apollo/client';
import { ServerParseError } from '@apollo/client/link/http';
import { ServerError } from '@apollo/client/link/utils';
import { IThreeDS } from '@bp/bppay-wallet-feature';
import { SFConsentUpdate, SFUserConsent } from '@bp/pulse-auth-sdk';
import { SupportedLocales } from '@bp/pulse-shared-types/lib/types/SupportedLocales';
import { SupportedCountries } from '@bp/rfid-mfe';
import { NavigationContainerRef } from '@react-navigation/native';
import { GraphQLError } from 'graphql';
import React from 'react';
import { TextInputProps } from 'react-native';
import { DefaultTheme } from 'styled-components/native';

import { SubsAnalyticsEventType } from '../analytics';
import { ITranslation } from '../translations/schema';
import {
  BpcmCancelSubscriptionResponseCode,
  BpcmGeneralResponseCode,
  Currency,
  HttpResponseCode,
  MandateStatus,
  OfferInvalidReason,
  OfferType,
  TemporaryMandateStatus,
  UserCountry,
  Validity,
} from './enums';

export interface GqlError {
  type: 'graphql';
  details: ReadonlyArray<GraphQLError>;
}

export interface NetworkError {
  type: 'network';
  details: Error | ServerError | ServerParseError;
}

export interface BusinessError {
  type: 'business';
  details: string;
}

export type IUser = {
  /**
   * Authenticated users unique user ID, for example Salesforce ID
   */
  userId: string | null | undefined;
  /**
   * Contry code for user
   */
  country: SupportedCountries;
};

export type PaymentMethod = React.ReactNode | null;

export interface SubscriptionProvider {
  children?: React.ReactNode;
  /**
   * Apollo GraphQL Micro-services URL that serve the Charge MFE
   */
  apiURL: string;
  /**
   * Corresponding GraphQL Micro-services URL that serve the Charge MFE
   */
  apiKey: string;
  /**
   * Locale set by user or device, used to localise strings, currencies etc
   */
  locale: SupportedLocales;
  /**
   * Brand that user will see, for example, Aral or bp
   */
  brand?: string;
  /**
   * Host applications reference navigation container
   */
  navigation: NavigationContainerRef<{}>;
  /**
   * Authenticated users unique user ID, for example, Salesforce ID
   * User object from session
   */
  user: IUser;
  /**
   * Terms and Conditions URL for eMobility Service Provider (eMSP)
   */
  emspTermsAndConditions: string;
  /**
   * Tariff Pricing URL
   */
  tariffPricing: string;
  /**
   * Handles requesting a valid JWT for authentication with GraphQL Micro-services that serve the Charge MFE
   */
  getToken: () => Promise<string | null>;
  /**
   * Handles capture of errors that may be encountered within the MFE, use to surface errors in a host application
   */
  onError?: (errorPayload: GqlError | NetworkError | BusinessError) => any;
  /**
   * Handles the desired host applications action when exiting the MFE through user interaction i.e. pressing the close icon
   */
  onExitMFE: () => void;
  /**
   * Feature Flags for Subscription MFE
   */
  featureFlags: featureFlags;
  /**
   * Allows the host application to hook into analytics events emitted from the MFE
   */
  onAnalyticsEvent: (analyticsEvent: SubsAnalyticsEventType) => void;
  /**
   * Whether there is internet access, passed from host app connectivity provider
   */
  isInternetReachable?: boolean | null;
  /**
   * If user has a negative balance and needs to have a positive balance to sign up to subscription
   */
  onPaymentRequired: () => void;

  /**
   * Handles the navigation when the user request to start charge after setting up a successful subscription
   */
  onRequestToStartCharge: () => void;

  /**
   * The exposed card component from the Wallet MFE for Wallet payment method based user sessions
   */
  cardComponent: PaymentMethod | null;

  /**
   * Handles the navigation when the user request to start charge after setting up a successful subscription
   */
  onNavigateToRFID: () => void;

  /**
   * Handles the navigation when the user presses back on SubscriptionEnding and SubscriptionEndingConfirmation screens
   */
  onNavigateToProfile: () => void;

  /**
   * Handles the navigation when the user presses on Setup Subscription button if user is ADAC
   */
  onNavigateToADACUnlink: () => void;

  /**
   * List of user consents related to terms and conditions.
   */
  consents: SFUserConsent[];

  /**
   * Updates the user's consents related to terms and conditions.
   */
  updateConsents: (updates: SFConsentUpdate[]) => Promise<void>;

  /**
   * List of required consents that the user must agree to in order to proceed.
   */
  requiredConsents: SFConsentUpdate;
}

export interface SubPreferances {
  cardPreference: string;
  addressLine: string;
  addressCity: string;
  addressCountry: string;
  addressPostcode: number;
  nextBillingDate: string;
  toBeCancelled: string;
}

export interface Gocardless {
  mandateStatus: MandateStatus | TemporaryMandateStatus;
  mandateId: string;
}

export interface Tags {
  tagId: string;
  tagStatus: string;
  tagNotes: string;
}

// Fields that were dynamically injected after GQL call was executed
interface IUserInfoExtended {
  latestMembership: IMmembership;
}

interface IUserInfoBase {
  userId: string;
  balance: number;
  status: string;
  type: string;
  country: UserCountry;
  gocardless: Gocardless;
  tagIds: Array<Tags>;
  membership: Array<IMmembership>;
  partnerType?: string;
}

export interface IUserInfo extends IUserInfoBase, IUserInfoExtended {}

export interface VoucherStatus {
  status: string | number;
  voucherStatus: string;
  voucherDesc: string;
  userPays: string;
  duration: string;
}

export interface IGetVoucherStatusResponse {
  getVoucherStatus: VoucherStatus;
}

export interface IGetVoucherStatusRequest {
  userId: string;
  voucherId: string;
}

export interface AddressDetails {
  addressLine: string;
  addressCity: string;
  addressPostcode: string;
  addressCountry: string;
}

export interface PaymentAddressDetails {
  address_line: string;
  address_city: string;
  address_postcode: string;
  address_country: string;
}

export interface IUpdateSubscriptionPrefResponse {
  updateSubscriptionPreference: any;
}

export interface IUpdateSubscriptionPrefRequest {
  userId: string;
  cardPreference: string;
  addressDetails: AddressDetails;
}

export interface IGoCardlessCompleteResponse {
  completePayment: any;
}
export interface IGoCardlessCompleteRequest {
  userId: string;
  flowId: string;
}

interface IRequestRFID {
  status: string | number;
  data?: any;
}

export interface IRequestRFIDResponse {
  requestRFID: IRequestRFID;
}

export interface IRequestRFIDRequest {
  userId: string;
  cardPreference: string;
  addressDetails: PaymentAddressDetails;
}

export type featureFlags = {
  block_subscription_upgrade_during_migration: boolean;
  enableInvoicesList: boolean;
  enableOfferDetails: boolean;
  enableRFID: boolean;
  extraTariffDiscountPercentage: number;
  introOfferCredit: number;
  introOfferCreditDuration: number;
  introPercentageDiscount: number;
  subsChargingRatesPromo: number;
  disableIntroOffer: boolean;
  savingRate: number;
  'subsChargeRateAC<22kW': number;
  'subsChargeRateDC<50kW': number;
  'subsChargeRateDC>50kW': number;
  subscriptionAmount: number;
  pollingTimeout: number;
  subsDrivingDistancekm: string;
  subsChargekWh: string;
  enableThreeDsInCreateSubscription: boolean;
};

export interface IGenericBpcmResponsePayload {
  code: BpcmGeneralResponseCode;
  error: boolean;
  eventDetails: string;
  eventTime: string;
  message: string;
  salesforceId: string;
}

export interface IGenericBpcmResponse {
  status: HttpResponseCode;
  payload: IGenericBpcmResponsePayload;
}

export interface ICancelSubscriptionRequest {
  userId: string;
}

export interface ICancelSubscriptionResponsePayload
  extends Omit<IGenericBpcmResponsePayload, 'code'> {
  code: BpcmGeneralResponseCode | BpcmCancelSubscriptionResponseCode;
}

export interface ICancelSubscriptionResponse
  extends Omit<IGenericBpcmResponse, 'payload'> {
  payload: ICancelSubscriptionResponsePayload;
}

export interface ICancelSubscriptionMutationResponse {
  cancelSubscription: ICancelSubscriptionResponse;
}

export interface ICancelWalletSubscriptionMutationResponse {
  cancelWalletSubscription: ICancelWalletSubscriptionResponse;
}

export interface ICancelWalletSubscriptionResponse {
  status: string;
  membershipStatus: string;
  statusReason: string;
  endOfCurrentBillingCycle: string;
  message: string;
  cancelledOn: string;
  error: string;
}

export type IGetWalletSubscriptionRequest = {
  userId?: string;
};
export interface Subscription_BillingItems {
  billingItemId: string;
  planId: string;
  name: string;
  amount: string | number;
  currency: string;
}
export interface Discount {
  amount: string | number;
  currency: string;
}
export interface SubscriptionDetails {
  id: string;
  customerId: string;
  paymentMethodId: string;
  merchantId: string;
  tenantId: string;
  useCase: string;
  billingItems: Subscription_BillingItems[] | null;
  createdAt: string;
  status: string;
  statusReason: string;
  nextBillingDate: string | null;
  cancelledOn: string | null;
  accountId: string;
  agreementId: string;
  deactivationDate: string;
  discountExpiresAt: string | null;
}

export type IGetWalletSubscriptionResponse = {
  getWalletSubscription: SubscriptionDetails;
};
export interface IMandate {
  mandateId: string;
  mandateStatus: string;
}
export interface IMmembership {
  userType: string;
  membershipStatus: string;
  membershipRequestCancelDate: string | null;
  membershipStartDate: string;
  membershipEndDate: string;
  membershipBillingCycleDate: string;
}

export interface ICancelSubscriptionContent {
  theme: Partial<DefaultTheme>;
  userType?: string;
  userCountry?: UserCountry;
}

export interface ICancelSubscriptionButtons {
  theme: Partial<DefaultTheme>;
  nextBillingdate?: string;
  setModalVisible: (value: boolean) => void;
  navigate: (screenName: string, params?: object, resetStack?: boolean) => void;
  cancelMembership: () => void;
  isInternetReachable?: boolean | null;
  userType?: string;
  loading?: boolean;
}

export interface IMySubscriptionData {
  theme: Partial<DefaultTheme>;
  translations: ITranslation;
  subsChargingRatesPromo: number;
  userCountry?: string;
  percentageDiscount?: string;
  activeUntil?: string;
  locale?: SupportedLocales;
}
export interface IRowWithData {
  id: string;
  title: string;
  icon: any;
  subTitle: string;
}

export interface ISubscriptionDetails {
  isMySubscription?: boolean;
  hasActiveDiscount?: boolean;
  hasActiveCreditOffer?: boolean;
  subsDiscount?: number;
  creditAmount?: number;
  subscription?: number;
  totalAmount?: number;
  currency?: Currency;
  userCountry?: UserCountry;
}

export interface ICardDescription {
  title: string;
  description?: string;
}

export interface ICancelWidget {
  title: string;
  disabled: boolean;
  onCancel: () => void;
  theme: Partial<DefaultTheme>;
}

interface IRetryWalletSubsPayment {
  nextBillingDate?: string;
  cancelledOn?: string;
  accountId?: string;
  useCase?: string;
  status?: string;
  statusReason?: string;
  error?: string;
}

export interface IRetryWalletSubsPaymentResponse {
  retryWalletSubsPayment: IRetryWalletSubsPayment;
}

export interface IRetryWalletSubsPaymentRequest {
  userId: string;
  membershipId: string;
  country?: string;
}

interface ICreateWalletSubscription {
  status: string;
  message: string;
}

export interface ICreateWalletSubscriptionResponse {
  createWalletSubscription: ICreateWalletSubscription;
}

export interface ICreateWalletSubscriptionRequest {
  userId: string;
  paymentMethodId: string;
  offerCode?: string;
  country?: string;
  threeDS?: IThreeDS;
}

interface IValidateOffer {
  isValid?: boolean | null;
  reason?: OfferInvalidReason | null;
  error?: string | null;
  offer?: {
    offerCode: string;
    offerType: OfferType;
    creditAmount?: number;
    subsDiscount?: number;
    subsDuration: number;
    offerName: string;
    offerPublicDescription: string;
  };
}

export interface IValidateOfferResponse {
  validateOffer: IValidateOffer;
}

export interface IValidateOfferRequest {
  offerCode: string;
}

interface IApplyOffer {
  error: string;
}

export interface IApplyOfferResponse {
  applyOffer: IApplyOffer;
}

export interface IApplyOfferRequest {
  offerCode: string;
  userId: string;
}

interface IGetSubsPlanQueryVariables {
  userId: string;
}

export interface ISubscriptionContextProvider {
  loading: boolean;
  subscription: number;
  totalAmount: number;
  subsPlanError: boolean;
  currency: Currency;
  subsDiscount: number;
  setSubsDiscount: (discount: number) => void;
  offerType: string;
  setOfferType: (offerType: string) => void;
  getSubsPlan: (
    variables?: IGetSubsPlanQueryVariables,
  ) => Promise<ApolloQueryResult<any> | null>;
  offerCode: string;
  setOfferCode: (offerCode: string) => void;
  valid: Validity;
  setValid: (valid: Validity) => void;
  validatedOfferCode: string;
  errorMessage: string;
  setErrorMessage: (errorMessage: string) => void;
  creditAmount: number;
  checkHandler: () => Promise<void>;
  onOfferCodeChange: TextInputProps['onChangeText'];
  offerCodeLoading: boolean;
  isSubscriptionPaused: boolean;
  setIsSubscriptionPaused: (paused: boolean) => void;
  isPausedModalVisible: boolean;
  setIsPausedModalVisible: (visible: boolean) => void;
  offerName: string;
  offerDescription: string;
  offerDuration: number;
}
export interface BillingData {
  subscription: Subscription_BillingItems | null;
  discounts: number;
  totalValue: number;
  currency: string;
}
export interface WalletSubscriptionDataContextValue {
  walletSubscriptionData: IGetWalletSubscriptionResponse | null;
  billingData: BillingData | null;
  errorData: any;
  refetchWalletSubscription: Function;
}

export interface MembershipSectionProps {
  Icon: any;
  header: string;
  content: string;
  alignTop?: boolean;
  resubscribed: boolean | undefined;
  freeTrailHeader: string;
  welcomeMsg: string;
  disableIntroOffer: boolean;
}
export interface MembershipDetailProps {
  left?: string;
  right: string | React.ReactNode;
}
