export enum SubscriptionScreenNames {
  Dashboard = 'Dashboard',
  JoinGoCardless = 'Join Gocardless Screen',
  MembershipOnboard = 'Comfort Membership',
  RFIDForm = 'RFID Form Screen',
  ShippingAddress = 'Shipping Address Screen',
  AccountDashboard = 'Account Dashboard',
  VoucherScreen = 'Voucher Screen',
  WelcomeScreen = 'Welcome Screen',
  CancelMembership = 'Cancel Membership Screen',
  CancelSubscriptionConfirmation = 'Cancel Subscription Confirmation',
  GoCardlessForm = 'GoCardless Form',
  GoCardlessMembership = 'Your Membership Screen',
  PendingMembership = 'Pending Membership Screen',
  ExpiredOffers = 'Expired Offers Screen',
  Tabs = 'Tabs',
  NoSignalError = 'No Signal Error',
  SubscriptionLanding = 'Subscription',
  SubscriptionSuccess = 'Subscription Success Screen',
  SubscriptionFailed = 'Subscription Failed Screen',
  SubscribeAndSave = 'Subscribe and Save',
  PaymentRequired = 'Payment Required',
  ConfirmSubscription = 'Confirm',
  MySubscription = 'My subscription',
  SubscriptionPaused = 'Subscription paused',
  SubscriptionEnding = 'Subscription ending',
  CancelSubscriptionFailed = 'CancelSubscriptionFailed',
  PaymentSuccess = 'PaymentSuccess',
  PaymentFailed = 'PaymentFailed',
  Loading = 'Loading',
  AdacCancelSubsConfirmation = 'AdacCancelSubsConfirmation',
  ConfirmCancelComfortMembership = 'ConfirmCancelComfortMembership',
  MyComfortMembership = 'MyComfortMembership',
  MyComfortMembershipActivation = 'MyComfortMembershipActivation',
}

export enum UserTypeEnum {
  NEW = 'NEW',
  SUBS = 'SUBS',
  PAYG = 'PAYG',
  PAYG_WALLET = 'PAYG-Wallet',
  SUBS_WALLET = 'SUBS-WALLET',
}

export enum PartnerTypeEnum {
  ADAC = 'ADAC',
}

export enum MembershipStatus {
  CANCELLED = 'CANCELLED',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}
export enum UserCountry {
  UK = 'UK',
  ES = 'ES',
  DE = 'DE',
  NL = 'NL',
  US = 'US',
}

export enum MandateStatus {
  ACTIVE = 'Active',
  PENDING = 'Pending',
  INACTIVE = 'Inactive',
  PENDING_CANCELLATION = 'Pending Cancellation',
}

export enum TemporaryMandateStatus {
  TEMPORARY_PENDING = 'Temporary Pending',
}

export enum AsyncStorageKey {
  DIRECT_DEBIT_REQUESTED = 'direct_debit_requested',
}

export enum AppRoute {
  account = 'account',
  profile = 'profile',
  registration = 'registration',
}

export enum HttpResponseCode {
  OK = 200,
  NOT_FOUND = 404,
  NOT_ACCEPTABLE = 406,
  INTERNAL_SERVER_ERROR = 500,
}

export enum BpcmGeneralResponseCode {
  OK = 'PU-0-2000',
  NOT_FOUND = 'PU-0-4004',
  INTERNAL_SERVER_ERROR = 'PU-0-5000',
}

export enum BpcmCancelSubscriptionResponseCode {
  NOT_FOUND_USER_WITH_SALESFORCE_ID = 'PU-4-4049',
  NOT_FOUND_USER_WITH_USER_ID = 'PU-4-40410',
  NOT_FOUND_POLAR_PLUS_USER = 'PU-4-40411',
  NOT_FOUND_TAG = 'PU-4-40412',
  NOT_ACCEPTABLE_CANCELLATION_ALREADY_REQUESTED = 'PU-4-4063',
  NOT_ACCEPTABLE_GOCARDLESS_MANDATE_ID_MISSING = 'PU-4-4064',
  NOT_ACCEPTABLE_MANDATE_STATUS_NOT_ACTIVE = 'PU-4-4065',
  INTERNAL_SERVER_ERROR_UNABLE_TO_CANCEL_SUBSCRIPTION = 'PU-4-5004',
}

export enum PulseDataItemIds {
  NEW_SUBS = 'smily',
  BP_PULSE_CARD = 'chargingOnGo',
  LOWEST_RATES = 'lowestRates',
  WARM_WELCOME = 'warmWelcome',
  DIAMOND = 'diamond',
  QUICKSETUP = 'quicksetup',
  OFFER_CODE = 'offercode',
  REPLACE_CARD = 'replacecard',
}

export enum OfferType {
  SUBS = 'SUBS',
  CREDIT = 'CREDIT',
  COMBO = 'COMBO',
}

export enum Validity {
  valid = 'valid',
  invalid = 'invalid',
  notSet = 'notSet',
}

export enum Countries {
  UK = 'UK',
  DE = 'DE',
  NL = 'NL',
  ES = 'ES',
}

export enum StatusReason {
  PENDING_CANCELLATION = 'PENDING_CANCELLATION',
  CANCELLED = 'CANCELLED',
}

export enum OfferInvalidReason {
  COUNTRY_MISMATCH = 'COUNTRY_MISMATCH',
  EXCEEDED_OFFER_LIMIT = 'EXCEEDED_OFFER_LIMIT',
  EXPIRED_OFFER = 'EXPIRED_OFFER',
  OFFER_NOT_FOUND = 'OFFER_NOT_FOUND',
  REDEEMED_OFFER = 'REDEEMED_OFFER',
}

export enum Currency {
  GBP = 'GBP',
  EUR = 'EUR',
  USD = 'USD',
}
