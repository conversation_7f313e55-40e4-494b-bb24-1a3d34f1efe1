import { SFConsent } from '@bp/pulse-auth-sdk/dist/types';
import { SupportedLocale } from '@bp/pulse-shared-types/lib/enums/SupportedLocale';
import { SupportedCountries } from '@bp/rfid-mfe';
import { render } from '@testing-library/react-native';
import React from 'react';

import SubscriptionProvider from './SubscriptionProvider';
import { mockNavigation } from './utils/MockNavigationContainer';

const MockProvider = ({ children }: any) => <div>{children}</div>;

jest.mock('./providers/GraphQLProvider', () => ({
  __esModule: true,
  default: () => <MockProvider />,
}));

jest.mock('./providers/HostNavigationProvider', () => ({
  __esModule: true,
  HostNavigationProvider: () => <MockProvider />,
}));

jest.mock('./providers/UserInfoProvider', () => ({
  __esModule: true,
  default: () => <MockProvider />,
}));

jest.mock('./providers/SubsPreferencesProvider', () => ({
  __esModule: true,
  default: () => <MockProvider />,
}));

jest.mock('./providers/AddressProvider', () => ({
  __esModule: true,
  AddressContextProvider: () => <MockProvider />,
}));

jest.mock('./providers/Settings', () => ({
  __esModule: true,
  SettingsContextProvider: () => <MockProvider />,
}));

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => {
    return null;
  },
}));
const mockCountry: SupportedCountries = 'UK';
const mockUseSettingsObj = {
  user: { userId: '123A', country: mockCountry },
  getToken: () =>
    new Promise<string>(() => {
      return '';
    }),
  locale: SupportedLocale.EN_GB,
  cardComponent: '',
  navigation: mockNavigation,
  apiURL: '',
  apiKey: '',
  featureFlags: {
    enableInvoicesList: true,
    enableOfferDetails: true,
    enableRFID: false,
    introOfferCredit: 0,
    introOfferCreditDuration: 0,
    introPercentageDiscount: 20,
    subsChargingRatesPromo: 0.44,
    disableIntroOffer: false,
    savingRate: 55,
    subscriptionAmount: 7.49,
    pollingTimeout: 36000,
    subsDrivingDistancekm: '215',
    subsChargekWh: '47',
    block_subscription_upgrade_during_migration: false,
    extraTariffDiscountPercentage: 10,
    'subsChargeRateAC<22kW': 0.42,
    'subsChargeRateDC<50kW': 0.46,
    'subsChargeRateDC>50kW': 0.54,
    enableThreeDsInCreateSubscription: false,
  },
  onAnalyticsEvent: () => {},
  onNavigateToRFID: () => {},
  onNavigateToProfile: () => {},
  onNavigateToADACUnlink: () => {},
  onPaymentRequired: () => {},
  onExitMFE: () => null,
  onRequestToStartCharge: () => null,
  emspTermsAndConditions: 'http://example.com',
  tariffPricing: 'http://example.com',
  consents: [],
  updateConsents: () => {
    return new Promise<void>((resolve) => {
      resolve();
    });
  },
  requiredConsents: {
    accepted: false,
    consentType: SFConsent.EV_TERMS_AND_CONDITIONS_DE,
  },
};

describe('Subscription Provider Test', () => {
  it('Can correctly render settings', () => {
    const { toJSON } = render(<SubscriptionProvider {...mockUseSettingsObj} />);
    expect(toJSON()).toMatchSnapshot();
  });
});
