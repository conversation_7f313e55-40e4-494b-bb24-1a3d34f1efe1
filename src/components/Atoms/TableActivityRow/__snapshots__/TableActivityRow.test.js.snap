// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TableActivityRow component renders with params 1`] = `
<View
  accessibilityHint="TableActivityRow"
  accessibilityLabel="TableActivityRow"
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={false}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignContent": "center",
      "borderBottomWidth": 1,
      "borderColor": "#D8D8D8",
      "borderTopWidth": 0,
      "flexDirection": "column",
      "height": 55,
      "justifyContent": "space-between",
      "marginLeft": 16,
      "marginRight": 16,
      "opacity": 1,
      "paddingLeft": 0,
      "paddingRight": 0,
    }
  }
  testID="TableActivityRow"
>
  <View
    height="55px"
    style={
      {
        "alignItems": "stretch",
        "flexDirection": "row",
        "height": 55,
      }
    }
  >
    <View
      height="55px"
      style={
        {
          "flexDirection": "column",
          "flexGrow": 1,
          "flexShrink": 1,
          "height": 55,
          "justifyContent": "center",
          "width": "100%",
        }
      }
    >
      <View
        style={
          {
            "flexDirection": "row",
            "justifyContent": "space-between",
          }
        }
      >
        <Text
          bold={false}
          size="16px"
          style={
            {
              "fontSize": 16,
              "fontWeight": "400",
              "textAlign": "left",
            }
          }
          textBreakStrategy="simple"
          weight="400"
        />
        <Text
          bold={false}
          rightTextColor="#000096"
          size="16px"
          style={
            {
              "color": "#000096",
              "fontSize": 16,
              "fontWeight": "400",
              "textAlign": "right",
            }
          }
          textBreakStrategy="simple"
          type="activity"
          weight="400"
        />
      </View>
      <View
        style={
          {
            "flexDirection": "row",
            "justifyContent": "space-between",
          }
        }
      >
        <Text
          style={
            {
              "color": "#616365",
              "fontSize": 12,
              "textAlign": "left",
            }
          }
        >
          subtext
        </Text>
        <Text
          ellipsizeMode="tail"
          numberOfLines={1}
          style={
            {
              "color": "#616365",
              "flexShrink": 1,
              "fontSize": 12,
              "overflow": "hidden",
              "paddingLeft": 20,
              "textAlign": "right",
            }
          }
        >
          subtext
        </Text>
      </View>
    </View>
    <View
      height="55px"
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "height": 55,
          "justifyContent": "center",
        }
      }
    />
  </View>
</View>
`;
