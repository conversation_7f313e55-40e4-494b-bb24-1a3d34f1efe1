import React from 'react';
import { useTheme } from 'styled-components/native';

import { DownArrow } from '../Icons';
import * as S from './TableActivityRow.styles';

interface IProps {
  height?: string;
  leftText?: string;
  leftSubtext?: string;
  rightText?: string;
  rightSubtext?: string;
  onPress?: any;
  borderWidth?: string;
  topBorderWidth?: string;
  visibleButton?: boolean;
  weight?: string;
  size?: string;
  type?: string;
  rightTextColor?: any;
  bold?: boolean;
}

const TableActivityRow = (props: IProps) => {
  const theme = useTheme();
  const {
    onPress,
    leftText,
    leftSubtext,
    rightSubtext,
    rightText,
    height = '55px',
    borderWidth = '1px',
    visibleButton,
    topBorderWidth = '0px',
    weight = '400',
    size = '16px',
    type = 'activity',
    rightTextColor = theme.subscriptionMfe.text.color.tertiary,
    bold = false,
  } = props;

  return (
    <S.OuterContainer
      testID={leftText || 'TableActivityRow'}
      accessibilityLabel={leftText || 'TableActivityRow'}
      accessibilityHint={leftText || 'TableActivityRow'}
      onPress={onPress}
      height={height}
      borderWidth={borderWidth}
      activeOpacity={onPress ? 0 : 1}
      topBorderWidth={topBorderWidth}
      type={type}
    >
      <S.InnerContainer height={height}>
        <S.LeftContainer height={height}>
          <S.TextContainer>
            <S.LeftTablerowText
              weight={weight}
              size={size}
              textBreakStrategy="simple"
              bold={bold}
            >
              {leftText}
            </S.LeftTablerowText>
            <S.RightTablerowText
              textBreakStrategy="simple"
              weight={weight}
              size={size}
              type={type}
              rightTextColor={rightTextColor}
              bold={bold}
            >
              {rightText}
            </S.RightTablerowText>
          </S.TextContainer>
          {(leftSubtext || rightSubtext) && (
            <S.TextContainer>
              <S.LeftTablerowSubText>{leftSubtext}</S.LeftTablerowSubText>
              <S.RightTablerowSubText numberOfLines={1} ellipsizeMode="tail">
                {rightSubtext}
              </S.RightTablerowSubText>
            </S.TextContainer>
          )}
        </S.LeftContainer>
        <S.IconContainer height={height}>
          {visibleButton && (
            <S.IconWrapper>
              <DownArrow
                color={theme.subscriptionMfe.text.color.strongSecondary}
              />
            </S.IconWrapper>
          )}
        </S.IconContainer>
      </S.InnerContainer>
    </S.OuterContainer>
  );
};

export default TableActivityRow;
