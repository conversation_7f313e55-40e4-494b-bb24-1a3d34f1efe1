import { styled } from 'styled-components/native';

export const OuterContainer = styled.TouchableOpacity<{
  height: string;
  borderWidth: string;
  topBorderWidth: string;
  type?: string;
}>`
  justify-content: space-between;
  height: ${({ height }) => height};
  border-bottom-width: ${({ borderWidth }) => borderWidth};
  border-top-width: ${({ topBorderWidth }) => topBorderWidth};
  border-color: ${(props: any) => props.theme.subscriptionMfe.table.border};
  margin-left: ${(props: any) => (props.type === 'voucher' ? '0px' : '16px')};
  margin-right: ${(props: any) => (props.type === 'voucher' ? '0px' : '16px')};
  padding-left: ${(props: any) => (props.type === 'voucher' ? '16px' : '0px')};
  padding-right: ${(props: any) => (props.type === 'voucher' ? '16px' : '0px')};
  flex-direction: column;
  align-content: center;
`;

export const InnerContainer = styled.View<{ height: string }>`
  flex-direction: row;
  height: ${({ height }) => height};
  align-items: stretch;
`;

export const LeftContainer = styled.View<{ height: string }>`
  justify-content: center;
  height: ${({ height }) => height};
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 1;
  width: 100%;
`;

export const TextContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
`;

export const IconContainer = styled.View<{ height: string }>`
  align-items: center;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  height: ${({ height }) => height};
`;

export const IconWrapper = styled.View`
  margin-left: 8px;
  width: 30px;
  justify-content: center;
  align-items: center;
  transform: rotate(-90deg);
`;

export const LeftTablerowText = styled.Text<{
  weight: string;
  size: string;
  bold?: boolean;
}>`
  text-align: left;
  font-size: ${({ size }) => size};
  font-weight: ${({ weight, bold }) => (bold ? '500' : weight)};
`;

export const LeftTablerowSubText = styled.Text`
  text-align: left;
  font-size: 12px;
  color: ${(props: any) =>
    props.theme.subscriptionMfe.text.color.strongSecondary};
`;

export const RightTablerowText = styled.Text<{
  weight: string;
  size: string;
  type?: string;
  rightTextColor?: any;
  bold?: boolean;
}>`
  text-align: right;
  font-size: ${({ size }) => size};
  color: ${(props: any) =>
    props.type === 'voucher'
      ? props.rightTextColor
      : props.theme.subscriptionMfe.text.color.tertiary};
  font-weight: ${({ weight, bold }) => (bold ? 'bold' : weight)};
`;

export const RightTablerowSubText = styled.Text`
  text-align: right;
  font-size: 12px;
  color: ${(props: any) =>
    props.theme.subscriptionMfe.text.color.strongSecondary};
  overflow: hidden;
  padding-left: 20px;
  flex-shrink: 1;
`;
