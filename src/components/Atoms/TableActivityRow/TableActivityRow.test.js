import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import TableActivityRow from './TableActivityRow';
describe('TableActivityRow component', () => {
  it('renders with params', () => {
    const TableActivityRowTest = render(
      <Theme>
        <TableActivityRow
          text={'text'}
          leftSubtext="subtext"
          rightSubtext="subtext"
        />
      </Theme>,
    );
    expect(TableActivityRowTest.toJSON()).toMatchSnapshot();
  });
});
