// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HeaderWithBackground rendering Should render correctly 1`] = `
<View
  accessibilityIgnoresInvertColors={true}
  style={
    {
      "justifyContent": "center",
    }
  }
>
  <Image
    resizeMode="cover"
    source={
      {
        "testUri": "../../../assets/images/bgImage.png",
      }
    }
    style={
      [
        {
          "bottom": 0,
          "left": 0,
          "position": "absolute",
          "right": 0,
          "top": 0,
        },
        {
          "height": undefined,
          "width": undefined,
        },
        undefined,
      ]
    }
  />
  <View
    style={
      {
        "alignItems": "center",
        "fontFamily": "Roboto-Medium",
        "justifyContent": "flex-start",
        "marginTop": 25,
        "paddingBottom": 0,
        "paddingLeft": 24,
        "paddingRight": 24,
        "paddingTop": 0,
        "width": "100%",
      }
    }
  >
    <Text
      style={
        {
          "color": "#FFFFFF",
          "fontSize": 38,
          "letterSpacing": 0.4,
          "lineHeight": 55,
          "maxWidth": 330,
          "textAlign": "center",
        }
      }
      type="primary"
    >
      Test
    </Text>
  </View>
</View>
`;
