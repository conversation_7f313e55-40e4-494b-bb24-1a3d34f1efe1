import { ImageBackground } from 'react-native';
import { styled } from 'styled-components/native';

export const UpperViewContainer = styled(ImageBackground)`
  justify-content: center;
`;

export const TitleContainer = styled.View`
  width: 100%;
  padding: 0px 24px;
  margin-top: 25px;
  justify-content: flex-start;
  align-items: center;
  font-family: 'Roboto-Medium';
`;

export const FreeText = styled.Text<{ type: string }>`
  color: ${({ theme }) => theme.subscriptionMfe.color.white};
  font-size: 38px;
  line-height: 55px;
  letter-spacing: 0.4px;
  text-align: center;
  max-width: ${({ type }) => (type === 'primary' ? '330px' : 'unset')};
`;

export const Text = styled.Text`
  color: ${({ theme }) => theme.subscriptionMfe.color.white};
  font-size: 26px;
  line-height: 39px;
  text-align: center;
  margin-bottom: 16px;
  font-family: 'Roboto-Light';
`;
