import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import { HeaderWithBackground } from './HeaderWithBackground';

describe('HeaderWithBackground rendering', () => {
  it('Should render correctly', () => {
    const result = render(
      <Theme>
        <HeaderWithBackground title="Test" />
      </Theme>,
    );
    expect(result.toJSON()).toMatchSnapshot();
  });
});
