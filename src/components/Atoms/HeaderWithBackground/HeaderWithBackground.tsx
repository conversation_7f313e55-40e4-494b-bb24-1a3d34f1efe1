import React from 'react';

import * as S from './HeaderWithBackground.styles';

export interface IProps {
  title: string;
  subTitle?: string;
  description?: string;
  type?: 'primary' | 'secondary';
}

export const HeaderWithBackground = ({
  title,
  subTitle,
  description,
  type = 'primary',
}: IProps) => {
  return (
    <S.UpperViewContainer
      source={require('../../../../assets/images/bgImage.png')}
      resizeMode="cover"
    >
      <S.TitleContainer>
        <S.FreeText type={type}>{title}</S.FreeText>
        {subTitle && <S.Text>{subTitle}</S.Text>}
        {description && <S.Text>{description}</S.Text>}
      </S.TitleContainer>
    </S.UpperViewContainer>
  );
};
