import React from 'react';
import { useTheme } from 'styled-components/native';

import * as S from './Tablerow.styles';

interface IProps {
  icon?: any;
  text: string | React.ReactNode;
  subtext?: string;
  onPress?: any;
  righticon?: any;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessible?: boolean;
  statustext?: string;
  testID?: string;
  hasSeperator?: boolean;
  textColor?: string;
  type?: string;
  topBorderWidth?: string;
  numberOfLines?: number;
}

const TableRow = (props: IProps) => {
  const theme = useTheme();
  const {
    icon = false,
    text,
    onPress,
    subtext = null,
    righticon,
    accessibilityLabel,
    accessibilityHint,
    accessible = true,
    statustext = '',
    testID,
    hasSeperator = true,
    textColor,
    type = 'primary',
    topBorderWidth,
    numberOfLines = 1,
  } = props;
  const listTextColour =
    textColor || theme.subscriptionMfe.gocardless.text.header;

  return (
    <S.OuterContainer
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessible={accessible}
      testID={testID}
      onPress={onPress}
      hasSeperator={hasSeperator}
      type={type}
      topBorderWidth={topBorderWidth}
      activeOpacity={accessible ? 0.2 : 1}
    >
      <S.InnerContainer type={type}>
        {icon && <S.LeftImageView type={type}>{icon}</S.LeftImageView>}
        <S.MiddleTextView>
          <S.TablerowText ellipsizeMode="tail" textColor={listTextColour}>
            {text}
          </S.TablerowText>
          {!!subtext && (
            <S.TablerowSubText
              numberOfLines={numberOfLines}
              ellipsizeMode="tail"
            >
              {subtext}
            </S.TablerowSubText>
          )}
        </S.MiddleTextView>
      </S.InnerContainer>
      {statustext !== '' && (
        <S.StatusContainer>
          <S.StatusText>{statustext}</S.StatusText>
        </S.StatusContainer>
      )}
      <S.RightImageView>{righticon}</S.RightImageView>
    </S.OuterContainer>
  );
};

export default React.memo(TableRow);
