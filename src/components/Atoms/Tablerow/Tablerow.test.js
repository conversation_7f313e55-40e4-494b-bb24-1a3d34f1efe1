import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import Tablerow from './Tablerow';
describe('Tablerow component', () => {
  it('renders with params', () => {
    const TablerowTest = render(
      <Theme>
        <Tablerow text={'text'} StatusText="status" subtext="subtext" />
      </Theme>,
    );
    expect(TablerowTest.toJSON()).toMatchSnapshot();
  });

  it('has a testID', () => {
    const { getByTestId } = render(
      <Theme>
        <Tablerow testID="TableRow testID" onPress={() => {}} text={'text'} />
      </Theme>,
    );
    expect(getByTestId('TableRow testID')).toBeTruthy();
  });
});
