// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Tablerow component renders with params 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={false}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignItems": "center",
      "borderBottomWidth": 1,
      "borderColor": "#D8D8D8",
      "borderTopWidth": 0,
      "flexDirection": "row",
      "justifyContent": "space-between",
      "opacity": 1,
      "paddingBottom": 16,
      "paddingTop": 16,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "flexDirection": "row",
        "flexShrink": 1,
        "justifyContent": "flex-start",
        "marginBottom": 0,
      }
    }
    type="primary"
  >
    <View
      style={
        {
          "flexDirection": "column",
          "flexShrink": 1,
          "justifyContent": "center",
          "paddingLeft": 28,
          "paddingRight": 8,
        }
      }
    >
      <Text
        ellipsizeMode="tail"
        style={
          {
            "alignItems": "flex-start",
            "color": "#1D1D26",
            "fontSize": 16,
            "textAlign": "left",
          }
        }
        textColor="#1D1D26"
      >
        text
      </Text>
      <Text
        ellipsizeMode="tail"
        numberOfLines={1}
        style={
          {
            "color": "#616365",
            "fontSize": 12,
            "textAlign": "left",
          }
        }
      >
        subtext
      </Text>
    </View>
  </View>
  <View
    style={
      {
        "alignItems": "center",
        "height": 30,
        "justifyContent": "center",
        "paddingBottom": 32,
        "transform": [
          {
            "rotate": "-90deg",
          },
        ],
        "width": 30,
      }
    }
  />
</View>
`;
