import { Dimensions } from 'react-native';
import { styled } from 'styled-components/native';

const phoneWidth = Dimensions.get('screen').width > 320 ? 32 : 24;
const paddingWidth = Dimensions.get('screen').width > 320 ? '16px' : '12px';
const textMarginRight = Dimensions.get('screen').width > 320 ? 5 : 10;
interface TextProps {
  textColor?: string;
}

const handlePaddingType = (type: string) => {
  switch (type) {
    case 'cancel':
      return '6px';
    case 'offer':
      return '0px';
    default:
      return paddingWidth;
  }
};

export const OuterContainer = styled.TouchableOpacity<{
  hasSeperator?: boolean;
  type?: string;
  topBorderWidth?: string;
}>`
  padding-top: ${(props: any) => handlePaddingType(props.type)};
  padding-bottom: ${(props: any) =>
    props.type === 'cancel' ? '6px' : paddingWidth};
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  border-bottom-width: ${(props: any) => (props.hasSeperator ? '1px' : '0')};
  border-color: ${(props: any) => props.theme.subscriptionMfe.table.border};
  border-top-width: ${(props: any) =>
    props.type === 'voucher' ? props.topBorderWidth : '0px'};
`;

export const LeftImageView = styled.View<{ type?: string }>`
  width: 20px;
  justify-content: center;
  align-items: center;
  padding-left: ${phoneWidth}px;
  padding-top: ${(props: any) => (props.type === 'cancel' ? '6px' : '0px')};
`;

export const RightImageView = styled.View`
  padding-bottom: 32px;
  height: 30px;
  width: 30px;
  justify-content: center;
  align-items: center;
  transform: rotate(-90deg);
`;

export const MiddleTextView = styled.View`
  flex-direction: column;
  justify-content: center;
  padding-left: 28px;
  padding-right: 8px;
  flex-shrink: 1;
`;

interface TablerowTextProps {
  textColor: string;
}

export const TablerowText = styled.Text<TablerowTextProps>`
  text-align: left;
  font-size: 16px;
  align-items: flex-start;
  color: ${({ textColor }: TextProps) => textColor};
`;

export const TablerowSubText = styled.Text`
  text-align: left;
  font-size: 12px;
  color: #616365;
`;

export const InnerContainer = styled.View<{ type?: string }>`
  justify-content: flex-start;
  align-items: ${(props: any) =>
    props.type === 'cancel' ? 'flex-start' : 'center'};
  flex-direction: row;
  flex-shrink: 1;
  margin-bottom: ${(props: any) => (props.type === 'cancel' ? '16px' : '0px')};
`;

export const StatusContainer = styled.View`
  height: 24px;
  border-radius: 13.5px;
  background-color: ${(props: any) =>
    props.theme.subscriptionMfe.voucher.color.status};
  padding-left: 4px;
  padding-right: 4px;
  margin-right: ${textMarginRight}px;
`;

export const StatusText = styled.Text`
  height: 24px;
  color: ${(props: any) => props.theme.subscriptionMfe.color.primary};
  font-size: 14px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
  padding: 0 4px;
`;
