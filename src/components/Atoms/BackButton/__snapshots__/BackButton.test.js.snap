// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`BackButton component renders without crashing 1`] = `
<View
  style={
    {
      "paddingBottom": 16,
      "paddingLeft": 16,
      "paddingRight": 16,
      "paddingTop": 16,
    }
  }
>
  <RNSVGSvgView
    align="xMidYMid"
    bbHeight="24"
    bbWidth="24"
    focusable={false}
    height="24"
    meetOrSlice={0}
    minX={0}
    minY={0}
    style={
      [
        {
          "backgroundColor": "transparent",
          "borderWidth": 0,
        },
        {
          "flex": 0,
          "height": 24,
          "width": 24,
        },
      ]
    }
    testID="backIcon"
    vbHeight={24}
    vbWidth={24}
    width="24"
  >
    <RNSVGGroup
      fill={
        {
          "payload": 4278190080,
          "type": 0,
        }
      }
    >
      <RNSVGGroup
        fill={null}
        fillRule={0}
        propList={
          [
            "fill",
            "fillRule",
          ]
        }
      >
        <RNSVGPath
          d="M0 0h24v24H0z"
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
        />
        <RNSVGPath
          d="M15.354 5.646a.501.501 0 01.057.638l-.057.07L9.707 12l5.647 5.646a.501.501 0 01.057.638l-.057.07a.501.501 0 01-.638.057l-.07-.057-6-6a.501.501 0 01-.057-.638l.057-.07 6-6a.502.502 0 01.708 0z"
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          propList={
            [
              "fill",
            ]
          }
        />
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGSvgView>
</View>
`;
