import React from 'react';
import { View } from 'react-native';

import { dynamicHeight, dynamicWidth } from '../../../utils/dynamicSizes';

interface IProps {
  vSpace?: number;
  hSpace?: number;
}

const SpacerDynamic = (props: IProps) => {
  const { vSpace = 0, hSpace = 0 } = props;
  return (
    <View
      style={{ height: dynamicHeight(vSpace), width: dynamicWidth(hSpace) }}
    />
  );
};

export default React.memo(SpacerDynamic);
