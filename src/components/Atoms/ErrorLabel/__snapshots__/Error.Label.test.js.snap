// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ErrorLabel component renders default 1`] = `
<View
  justify="flex-start"
  style={
    {
      "flexDirection": "row",
      "justifyContent": "flex-start",
      "paddingTop": 7,
    }
  }
>
  <Text
    status="error"
    style={
      {
        "color": "#CF0001",
        "fontSize": 11,
        "letterSpacing": 0.7,
        "lineHeight": 16,
      }
    }
  >
    Error Label Text
  </Text>
</View>
`;
