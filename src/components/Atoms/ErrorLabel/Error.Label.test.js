import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import ErrorLabel from './ErrorLabel';

const mockTranslations = TRANSLATIONS.en_GB;

describe('ErrorLabel component', () => {
  it('renders default', () => {
    const ErrorLabelTest = render(
      <Theme>
        <ErrorLabel>{mockTranslations.testing.ERROR_LABEL_TEXT}</ErrorLabel>
      </Theme>,
    );
    expect(ErrorLabelTest.toJSON()).toMatchSnapshot();
  });
});
