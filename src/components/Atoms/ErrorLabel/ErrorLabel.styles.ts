import { styled } from 'styled-components/native';

export const ViewContainer = styled.View<{ justify: string }>`
  flex-direction: row;
  padding-top: 7px;
  justify-content: ${({ justify }) => justify};
`;

export const ErrorLabel = styled.Text<{
  status: 'success' | 'warning' | 'error';
}>`
  font-size: 11px;
  line-height: 16px;
  letter-spacing: 0.7px;
  color: ${({ theme, status }) => theme.subscriptionMfe.color.state[status]};
`;
