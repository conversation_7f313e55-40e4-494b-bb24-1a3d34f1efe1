// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HeaderCloseButton component renders correctly 1`] = `
<View
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": undefined,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "opacity": 1,
      "padding": 16,
    }
  }
>
  <RNSVGSvgView
    align="xMidYMid"
    bbHeight="16px"
    bbWidth="16px"
    focusable={false}
    height="16px"
    meetOrSlice={0}
    minX={0}
    minY={0}
    style={
      [
        {
          "backgroundColor": "transparent",
          "borderWidth": 0,
        },
        {
          "flex": 0,
          "height": 16,
          "width": 16,
        },
      ]
    }
    vbHeight={16}
    vbWidth={16}
    width="16px"
  >
    <RNSVGGroup
      fill={
        {
          "payload": 4278190080,
          "type": 0,
        }
      }
    >
      <RNSVGGroup
        fill={null}
        name="illustarations-and-Icons"
        propList={
          [
            "fill",
            "stroke",
          ]
        }
        stroke={null}
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              -592,
              -2998,
            ]
          }
          name="Icons"
        >
          <RNSVGGroup
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            matrix={
              [
                1,
                0,
                0,
                1,
                588,
                2994,
              ]
            }
            name="atom-/-icon-/-line-/-custom-/-close"
          >
            <RNSVGPath
              d="M5.2843055,4.58859116 L5.35355339,4.64644661 L12,11.293 L18.6464466,4.64644661 L18.7156945,4.58859116 C18.9105626,4.45359511 19.179987,4.47288026 19.3535534,4.64644661 C19.5488155,4.84170876 19.5488155,5.15829124 19.3535534,5.35355339 L19.3535534,5.35355339 L12.707,12 L19.3535534,18.6464466 C19.5488155,18.8417088 19.5488155,19.1582912 19.3535534,19.3535534 C19.179987,19.5271197 18.9105626,19.5464049 18.7156945,19.4114088 L18.6464466,19.3535534 L12,12.707 L5.35355339,19.3535534 L5.2843055,19.4114088 C5.08943736,19.5464049 4.82001296,19.5271197 4.64644661,19.3535534 C4.45118446,19.1582912 4.45118446,18.8417088 4.64644661,18.6464466 L4.64644661,18.6464466 L11.293,12 L4.64644661,5.35355339 C4.45118446,5.15829124 4.45118446,4.84170876 4.64644661,4.64644661 C4.82001296,4.47288026 5.08943736,4.45359511 5.2843055,4.58859116 Z"
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
              name="Combined-Shape"
              propList={
                [
                  "fill",
                ]
              }
            />
          </RNSVGGroup>
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGSvgView>
</View>
`;
