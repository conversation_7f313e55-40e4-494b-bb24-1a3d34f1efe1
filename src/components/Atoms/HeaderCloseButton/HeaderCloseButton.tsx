import { TouchableOpacity } from '@bp/ui-components/mobile/core';
import React from 'react';

import { CloseIcon } from '../Icons';

interface HeaderCloseButtonProps {
  onPress: () => void;
  color?: string;
}

const HeaderCloseButton: React.FC<HeaderCloseButtonProps> = ({
  onPress,
  color,
}) => {
  return (
    <TouchableOpacity style={{ padding: 16 }} onPress={onPress}>
      <CloseIcon color={color ?? '#000'} />
    </TouchableOpacity>
  );
};

export default HeaderCloseButton;
