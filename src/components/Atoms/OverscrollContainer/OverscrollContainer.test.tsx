/* eslint-disable @calm/react-intl/missing-formatted-message */
import { act, fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Text, View } from 'react-native';

import OverscrollContainer from './OverscrollContainer';

jest.mock('./OverscrollContainer.styles', () => ({
  ScrollContainer: 'ScrollContainer',
  ScrollContainerStyle: {},
}));

describe('OverscrollContainer', () => {
  it('should render children correctly', () => {
    const { getByText } = render(
      <OverscrollContainer>
        <Text>Test</Text>
      </OverscrollContainer>,
    );
    expect(getByText('Test')).toBeTruthy();
  });

  it('should call willButtonFloat with correct value', () => {
    const mockWillButtonFloat = jest.fn();
    const { getByTestId } = render(
      <OverscrollContainer willButtonFloat={mockWillButtonFloat}>
        <View testID="content">
          <Text>Test</Text>
        </View>
      </OverscrollContainer>,
    );

    const scrollContainer = getByTestId('scroll-container');

    act(() => {
      fireEvent(scrollContainer, 'layout', {
        nativeEvent: { layout: { height: 100 } },
      });
    });

    act(() => {
      fireEvent(scrollContainer, 'contentSizeChange', null, 150);
    });

    expect(mockWillButtonFloat).toHaveBeenCalledWith(true);

    act(() => {
      fireEvent(scrollContainer, 'contentSizeChange', null, 50);
    });

    expect(mockWillButtonFloat).toHaveBeenCalledWith(false);
  });

  it('should disable scrolling when scrollEnable is false', () => {
    const { getByTestId } = render(
      <OverscrollContainer scrollEnable={false}>
        <Text>Test</Text>
      </OverscrollContainer>,
    );

    const scrollContainer = getByTestId('scroll-container');
    expect(scrollContainer.props.scrollEnabled).toBe(false);
  });
});
