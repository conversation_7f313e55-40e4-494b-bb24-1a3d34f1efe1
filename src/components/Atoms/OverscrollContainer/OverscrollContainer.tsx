import React, { PropsWithChildren, useEffect, useState } from 'react';

import * as S from './OverscrollContainer.styles';

interface Iprops {
  willButtonFloat?: (value: boolean) => void;
  scrollEnable?: boolean;
}

const OverscrollContainer = ({
  willButtonFloat = () => null,
  scrollEnable = true,
  children,
}: PropsWithChildren<Iprops>) => {
  const [scrollContainerHeight, setScrollContainerHeight] = useState(0);
  const [contentContainerHeight, setContentContainerHeight] = useState(0);

  useEffect(() => {
    willButtonFloat(contentContainerHeight > scrollContainerHeight);
  }, [scrollContainerHeight, contentContainerHeight, willButtonFloat]);

  return (
    <S.ScrollContainer
      testID="scroll-container"
      scrollEnabled={scrollEnable}
      onContentSizeChange={(
        _: any,
        contentHeight: React.SetStateAction<number>,
      ) => {
        setContentContainerHeight(contentHeight);
      }}
      onLayout={({ nativeEvent }: any) => {
        setScrollContainerHeight(nativeEvent.layout.height);
      }}
      contentContainerStyle={S.ScrollContainerStyle}
      alwaysBounceVertical={false}
      nestedScrollEnabled
    >
      {children}
    </S.ScrollContainer>
  );
};
export default OverscrollContainer;
