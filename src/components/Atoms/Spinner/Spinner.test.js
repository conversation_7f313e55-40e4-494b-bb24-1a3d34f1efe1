import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import Spinner from './Spinner';

describe('Spinner component', () => {
  it('should render', () => {
    const toggle = render(
      <Theme>
        <Spinner />
      </Theme>,
    );
    expect(toggle.toJSON()).toMatchSnapshot();
  });
  it('should render correctly', () => {
    const toggle = render(
      <Theme>
        <Spinner active={true} />
      </Theme>,
    );
    expect(toggle.toJSON()).toMatchSnapshot();
  });
});
