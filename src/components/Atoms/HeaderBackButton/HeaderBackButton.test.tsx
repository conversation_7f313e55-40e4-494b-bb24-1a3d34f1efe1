import { NavigationContainerRef, useRoute } from '@react-navigation/native';
import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import { SubscriptionScreenNames } from '../../../common/enums';
import { TRANSLATIONS } from '../../../translations';
import { mockNavigation } from '../../../utils/MockNavigationContainer';
import HeaderBackButton from './HeaderBackButton';

type ParamList = {
  [SubscriptionScreenNames.SubscriptionPaused]: { isModalVisible: boolean };
  [SubscriptionScreenNames.SubscriptionLanding]: { isModalVisible?: boolean };
};

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useRoute: jest.fn(),
  };
});

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: {
    id: '123',
    balance: '1.99',
    gocardless: { mandateStatus: 'applied' },
    tagIds: [],
  },
  onAnalyticsEvent: () => jest.fn(),
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  featureFlags: {
    enableInvoicesList: true,
    enableOfferDetails: true,
    enableRFID: true,
  },
  isInternetReachable: true,
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const navigationMock: NavigationContainerRef<ParamList> = {
  navigate: jest.fn(),
  goBack: jest.fn(),
  dispatch: jest.fn(),
  canGoBack: jest.fn(() => true),
  reset: jest.fn(),
  isFocused: jest.fn(() => true),
  getParent: jest.fn(),
  getState: jest.fn(),
  dangerouslyGetParent: jest.fn(),
  dangerouslyGetState: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  setParams: jest.fn(),
} as unknown as NavigationContainerRef<ParamList>;

describe('HeaderBackButton component', () => {
  it('renders correctly', () => {
    const { toJSON } = render(
      <HeaderBackButton navigation={navigationMock} fromPaused={true} />,
    );
    expect(toJSON()).toMatchSnapshot();
  });
  it('navigates correctly when fromPaused is true', () => {
    const { getByTestId } = render(
      <HeaderBackButton navigation={navigationMock} fromPaused={true} />,
    );

    fireEvent.press(getByTestId('backButton'));
    expect(navigationMock.goBack).toHaveBeenCalled();
  });

  it('navigates correctly when fromPaused is false', () => {
    const { getByTestId } = render(
      <HeaderBackButton
        navigation={navigationMock}
        setModalVisible={null}
        fromPaused={false}
      />,
    );

    fireEvent.press(getByTestId('backButton'));
    expect(navigationMock.navigate).toHaveBeenCalledWith(
      SubscriptionScreenNames.SubscriptionLanding,
      { isModalVisible: null },
    );
  });

  it('calls customOnPress when provided', () => {
    const customOnPressMock = jest.fn();
    const { getByTestId } = render(
      <HeaderBackButton
        navigation={navigationMock}
        customOnPress={customOnPressMock}
      />,
    );
    fireEvent.press(getByTestId('backButton'));
    expect(customOnPressMock).toHaveBeenCalled();
  });

  it('should call onBackPress callback function from params if provided', () => {
    const onBackPressMock = jest.fn();
    (useRoute as jest.Mock).mockReturnValue({
      params: {
        onBackPress: onBackPressMock,
      },
    });

    const { getByTestId } = render(
      <HeaderBackButton navigation={mockNavigation} />,
    );

    fireEvent.press(getByTestId('backButton'));
    expect(onBackPressMock).toHaveBeenCalled();
  });
});
