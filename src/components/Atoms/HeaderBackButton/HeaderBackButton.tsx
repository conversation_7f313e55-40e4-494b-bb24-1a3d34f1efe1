import { TouchableOpacity } from '@bp/ui-components/mobile/core';
import {
  NavigationContainerRef,
  RouteProp,
  useRoute,
} from '@react-navigation/native';
import React from 'react';

import { SubscriptionScreenNames } from '../../../common/enums';
import { BackIcon } from '../Icons';

interface HeaderLeftButtonProps {
  navigation: NavigationContainerRef<any>;
  setModalVisible?: boolean | null;
  color?: string | null;
  screen?: string | null;
  fromPaused?: boolean | null;
  customOnPress?: () => void | null;
  accessibilityLabel?: string;
}

const HeaderBackButton = ({
  navigation,
  setModalVisible,
  color,
  fromPaused,
  screen,
  customOnPress,
  accessibilityLabel,
}: HeaderLeftButtonProps) => {
  const { params } = useRoute<RouteProp<any>>() ?? {};
  return (
    <TouchableOpacity
      accessibilityLabel={accessibilityLabel}
      style={{ padding: 16 }}
      onPress={
        customOnPress ||
        params?.onBackPress ||
        (() => {
          const targetScreen = fromPaused
            ? SubscriptionScreenNames.SubscriptionPaused
            : SubscriptionScreenNames.SubscriptionLanding;

          if (fromPaused) {
            navigation.goBack();
          } else if (screen) {
            navigation.navigate(screen);
          } else {
            navigation.navigate(targetScreen, {
              isModalVisible: setModalVisible,
            });
          }
        })
      }
      testID="backButton"
    >
      <BackIcon testID="backIcon" color={color ?? '#000'} />
    </TouchableOpacity>
  );
};

export default HeaderBackButton;
