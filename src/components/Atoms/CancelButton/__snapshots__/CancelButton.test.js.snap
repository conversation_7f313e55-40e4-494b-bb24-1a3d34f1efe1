// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Cancel Button component renders as cancel without crashing 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": false,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={false}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignSelf": "stretch",
      "backgroundColor": "#F5CCCC",
      "borderBottomLeftRadius": 28,
      "borderBottomRightRadius": 28,
      "borderBottomWidth": 2,
      "borderColor": "#F5CCCC",
      "borderLeftWidth": 2,
      "borderRightWidth": 2,
      "borderTopLeftRadius": 28,
      "borderTopRightRadius": 28,
      "borderWidth": 2,
      "height": 55,
      "justifyContent": "center",
      "lineHeight": 55,
      "opacity": 1,
    }
  }
  testID="CancelButton"
>
  <Text
    disabled={false}
    size="normal"
    style={
      {
        "alignItems": "center",
        "color": "#CF0001",
        "display": "flex",
        "fontSize": 16,
        "justifyContent": "center",
        "letterSpacing": 0.7,
        "textAlign": "center",
        "textDecorationColor": "#CF0001",
        "textDecorationLine": "none",
        "textDecorationStyle": "solid",
      }
    }
  >
    Hello
  </Text>
</View>
`;

exports[`Cancel Button component should render small button 1`] = `
<View
  accessibilityRole="button"
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": false,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onClick={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignSelf": "stretch",
      "backgroundColor": "#F5CCCC",
      "borderBottomLeftRadius": 28,
      "borderBottomRightRadius": 28,
      "borderBottomWidth": 2,
      "borderColor": "#F5CCCC",
      "borderLeftWidth": 2,
      "borderRightWidth": 2,
      "borderTopLeftRadius": 28,
      "borderTopRightRadius": 28,
      "borderWidth": 2,
      "height": 32,
      "justifyContent": "center",
      "lineHeight": 55,
      "opacity": 1,
    }
  }
  testID="CancelButton"
>
  <Text
    disabled={false}
    size="small"
    style={
      {
        "alignItems": "center",
        "color": "#CF0001",
        "display": "flex",
        "fontSize": 13,
        "justifyContent": "center",
        "letterSpacing": 0.7,
        "textAlign": "center",
        "textDecorationColor": "#CF0001",
        "textDecorationLine": "none",
        "textDecorationStyle": "solid",
      }
    }
  />
</View>
`;
