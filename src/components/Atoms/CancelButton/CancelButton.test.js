import { theme } from '@bp/ui-components/mobile/core';
import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import { BackIcon } from '../Icons';
import CancelButton from './CancelButton';

const mockTranslations = TRANSLATIONS.en_GB;
const buttonTypes = ['cancel'];

describe('Cancel Button component', () => {
  buttonTypes.forEach((button) => {
    it(`renders as ${button} without crashing`, () => {
      const buttonRender = render(
        <Theme customTheme={theme}>
          <CancelButton type={button} disabled={button === 'disabled'}>
            {mockTranslations.testing.HELLO}
          </CancelButton>
        </Theme>,
      );
      expect(buttonRender.toJSON()).toMatchSnapshot();
    });
  });

  it('should render icons', () => {
    const onPressMock = jest.fn();
    const { queryByTestId } = render(
      <Theme customTheme={theme}>
        <CancelButton icon={<BackIcon />} onPress={onPressMock} />
      </Theme>,
    );
    expect(queryByTestId('ButtonIcon')).toBeDefined();
  });

  it('should render small button', () => {
    const onPressMock = jest.fn();
    const buttonRender = render(
      <Theme customTheme={theme}>
        <CancelButton type="cancel" size="small" onPress={onPressMock} />
      </Theme>,
    );
    expect(buttonRender.toJSON()).toMatchSnapshot();
  });

  it('should render button with spinner correctly', () => {
    const onPressMock = jest.fn();
    const { queryByTestId } = render(
      <Theme customTheme={theme}>
        <CancelButton spinner onPress={onPressMock}>
          {mockTranslations.testing.HELLO}
        </CancelButton>
      </Theme>,
    );
    expect(queryByTestId('ButtonIcon')).toBeDefined();
  });

  it('should render button with centered icon correctly', () => {
    const onPressMock = jest.fn();
    const { queryByTestId } = render(
      <Theme customTheme={theme}>
        <CancelButton icon={<BackIcon />} centeredIcon onPress={onPressMock}>
          {mockTranslations.testing.HELLO}
        </CancelButton>
      </Theme>,
    );
    expect(queryByTestId('ButtonIconCenter')).toBeDefined();
  });
});
