import React from 'react';
import { ActivityIndicator } from 'react-native';

import * as S from './CancelButton.styles';

interface IProps {
  children: string;
  onPress?: any;
  icon?: React.ReactNode;
  size?: 'normal' | 'small';
  centeredIcon?: boolean;
  disabled?: boolean;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
  ref?: any;
  color?: string;
  spinner?: boolean;
  disabledColor?: string;
}

export default (props: IProps) => {
  const {
    children,
    onPress,
    icon,
    size = 'normal',
    centeredIcon,
    disabled = false,
    accessibilityLabel,
    accessibilityHint,
    testID,
    ref,
    color,
    disabledColor,
    spinner = false,
  } = props;

  return (
    <S.Button
      color={color}
      disabledColor={disabledColor}
      size={size}
      ref={ref}
      onPress={onPress}
      testID={testID || 'CancelButton'}
      accessible
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityRole="button"
      disabled={disabled}
    >
      {icon && !centeredIcon && <S.ButtonIcon>{icon}</S.ButtonIcon>}
      {spinner && (
        <S.ButtonIcon>
          <ActivityIndicator size={'small'} color={'black'} />
        </S.ButtonIcon>
      )}
      <S.Text
        disabled={disabled}
        icon={icon && !centeredIcon ? true : undefined}
        size={size}
      >
        {icon && centeredIcon && (
          <S.ButtonIconCenter data-testId="ButtonIcon">
            {icon}
          </S.ButtonIconCenter>
        )}
        {children}
      </S.Text>
    </S.Button>
  );
};
