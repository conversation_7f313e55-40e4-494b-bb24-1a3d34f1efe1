import { styled } from 'styled-components/native';

interface IButton {
  ref?: any;
  theme?: any;
  type?: string;
  size: string;
  disabled: boolean;
  color?: string;
  disabledColor?: string;
}

interface IButtonText {
  type?: string;
  size: string;
  disabled: boolean;
  icon?: boolean;
}

const getButtonTextStyle = ({ disabled, theme }: IButton) => {
  return disabled
    ? theme.subscriptionMfe.buttons.cancel.disabledText
    : theme.subscriptionMfe.buttons.cancel.text;
};

export const Text = styled.Text<IButtonText>`
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  text-align: center;
  letter-spacing: 0.7px;
  font-size: ${({ size }) => (size === 'small' ? '13px' : '16px')};
  ${({ icon }) => icon && 'padding: 0 58px'};
  color: ${getButtonTextStyle};
  text-decoration: ${({ type }) => (type === 'timed' ? 'underline' : 'none')};
  text-decoration-color: ${getButtonTextStyle};
`;

export const Button = styled.TouchableOpacity<IButton>`
  border-radius: ${({ type }) => {
    if (type === 'timed' || type === 'fullWidth') {
      return '0';
    } else {
      return '28px';
    }
  }};
  height: ${({ size }) => (size === 'small' ? '32px' : '55px')};
  line-height: 55px;
  align-self: stretch;
  justify-content: center;
  background: ${({ theme }) => theme.subscriptionMfe.buttons.cancel.background};
  border-color: ${({ theme }) =>
    theme.subscriptionMfe.buttons.cancel.borderColor};
  border-width: ${({ type }) => (type === 'fullWidth' ? '1px' : '2px')};
  border-left-width: ${({ type }) => (type === 'fullWidth' ? '0' : '2px')};
  border-right-width: ${({ type }) => (type === 'fullWidth' ? '0' : '2px')};
  border-bottom-width: ${({ type }) => (type === 'fullWidth' ? '0' : '2px')};
`;

export const ButtonIcon = styled.View`
  position: absolute;
  display: flex;
  justify-content: center;
  left: 0;
  height: 100%;
  padding-left: 19px;
`;

export const ButtonIconCenter = styled.View`
  margin-right: 10px;
`;
