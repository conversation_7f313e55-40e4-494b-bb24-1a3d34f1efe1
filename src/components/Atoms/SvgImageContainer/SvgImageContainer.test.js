import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import SvgImageContainer from './SvgImageContainer';

describe('SvgImageContainer component', () => {
  it('should render', () => {
    const toggle = render(
      <Theme>
        <SvgImageContainer />
      </Theme>,
    );
    expect(toggle.toJSON()).toMatchSnapshot();
  });
});
