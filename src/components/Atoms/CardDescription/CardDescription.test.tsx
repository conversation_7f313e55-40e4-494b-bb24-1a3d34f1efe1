import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import { CardDescription } from './CardDescription';

describe('CardDescription rendering', () => {
  it('Should render correctly', () => {
    const result = render(
      <Theme>
        <CardDescription title="test" description="description" />
      </Theme>,
    );
    expect(result.toJSON()).toMatchSnapshot();
  });
});
