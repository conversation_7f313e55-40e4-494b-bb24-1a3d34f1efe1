// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CardDescription rendering Should render correctly 1`] = `
<View
  style={
    {
      "display": "flex",
    }
  }
>
  <Text
    needsPaddingBottom={true}
    style={
      {
        "color": "#212121",
        "fontSize": 16,
        "fontWeight": "400",
        "lineHeight": 28,
        "paddingBottom": 8,
        "paddingLeft": 0,
      }
    }
  >
    test
  </Text>
  <Text
    style={
      {
        "color": "#212121",
        "fontSize": 12,
        "fontWeight": "400",
        "lineHeight": 21,
      }
    }
  >
    description
  </Text>
</View>
`;
