import { styled } from 'styled-components/native';

interface NormalTextProps {
  needsPadding?: boolean;
  needsPaddingBottom?: boolean;
}

export const DescriptionWrapper = styled.View`
  display: flex;
`;

export const DescriptionText = styled.Text`
  font-size: 12px;
  line-height: 21px;
  font-weight: 400;
  color: ${({ theme }) => theme.subscriptionMfe.gocardless.text.banner};
`;

export const NormalText = styled.Text<NormalTextProps>`
  padding-left: ${({ needsPadding }) => (needsPadding ? '24px' : '0')};
  font-size: 16px;
  line-height: 28px;
  font-weight: 400;
  color: ${({ theme }) => theme.subscriptionMfe.gocardless.text.banner};
  padding-bottom: ${({ needsPadding, needsPaddingBottom }) =>
    needsPadding || needsPaddingBottom ? '8px' : '0'};
`;
