import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const CarSideNormal = (props: IProps) => {
  const { width = '24', height = '24', color = '#000096' } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24">
      <G fill="none" fill-rule="evenodd">
        <Path fill="none" d="M0 0h24v24H0z" />
        <Path
          d="M14.777 15.076H9.05m7.51 1.348a1.856 1.856 0 1 1 0-3.712 1.856 1.856 0 0 1 0 3.712zm-9.302 0a1.856 1.856 0 1 1 0-3.712 1.856 1.856 0 0 1 0 3.712zM5.43 14.883l-1.429-.829v-3.197l.8-1.875-.4-.937c2.08-.245 4.144-.469 5.772-.469 2.582 0 4.739 1.132 6.626 2.813 2.628.528 3.2 1.814 3.2 2.343v2.344h-1.655"
          stroke={color}
        />
      </G>
    </Svg>
  );
};

export default React.memo(CarSideNormal);
