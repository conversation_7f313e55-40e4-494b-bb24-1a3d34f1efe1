import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const PolarPomIcon = (props: IProps) => {
  const { width = '24', height = '24', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height}>
      <G stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <G transform="translate(-592.000000, -1126.000000)">
          <G transform="translate(588.000000, 1122.000000)">
            <Path
              d="M12,4 C16.418278,4 20,7.581722 20,12 C20,16.418278 16.418278,20 12,20 C7.581722,20 4,16.418278 4,12 C4,7.581722 7.581722,4 12,4 Z M12,5 C8.13400675,5 5,8.13400675 5,12 C5,15.8659932 8.13400675,19 12,19 C15.8659932,19 19,15.8659932 19,12 C19,8.13400675 15.8659932,5 12,5 Z M16,12 C16.2761424,12 16.5,12.2238576 16.5,12.5 C16.5,14.9852814 14.4852814,17 12,17 C9.51471863,17 7.5,14.9852814 7.5,12.5 C7.5,12.2238576 7.72385763,12 8,12 L8,12 Z M15.463,13 L8.536,13 L8.54580909,13.0677182 C8.80701426,14.6691343 10.1550039,15.9054397 11.8079648,15.9948211 L11.8079648,15.9948211 L12,16 C13.739697,16 15.1829394,14.7307273 15.4541909,13.0677182 L15.4541909,13.0677182 L15.463,13 Z M9,8 C9.55228475,8 10,8.44771525 10,9 C10,9.55228475 9.55228475,10 9,10 C8.44771525,10 8,9.55228475 8,9 C8,8.44771525 8.44771525,8 9,8 Z M15,8 C15.5522847,8 16,8.44771525 16,9 C16,9.55228475 15.5522847,10 15,10 C14.4477153,10 14,9.55228475 14,9 C14,8.44771525 14.4477153,8 15,8 Z"
              fill={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(PolarPomIcon);
