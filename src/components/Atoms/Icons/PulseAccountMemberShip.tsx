import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const PulseAccountMemberShip = (props: IProps) => {
  const { width = '24', height = '24', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24">
      <G stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <G transform="translate(-16.000000, -506.000000)">
          <G transform="translate(0.000000, 477.000000)">
            <G transform="translate(16.000000, 29.000000)">
              <Path
                d="M20,6 C21.1045695,6 22,6.8954305 22,8 L22,16 C22,17.1045695 21.1045695,18 20,18 L4,18 C2.8954305,18 2,17.1045695 2,16 L2,8 C2,6.8954305 2.8954305,6 4,6 L20,6 Z M20,7 L4,7 C3.********,7 3.********,7.******** 3.********,7.******** L3,8 L3,16 C3,16.5128358 3.********,16.9355072 3.********,16.9932723 L4,17 L20,17 C20.5128358,17 20.9355072,16.6139598 20.9932723,16.1166211 L21,16 L21,8 C21,7.******** 20.6139598,7.******** 20.1166211,7.******** L20,7 Z M7.********,8.******** C7.********,8.******** 7.********,8.******** 7.94836804,8.******** L7.94836804,8.******** L8.566,10.032 L9.94954045,10.232654 C10.330357,10.2879899 10.4987552,10.7265019 10.2832676,11.0204684 L10.2832676,11.0204684 L10.2266471,11.0855007 L9.226,12.061 L9.46226729,13.43802 C9.52765213,13.8192428 9.15855166,14.1159918 8.81081455,13.9970736 L8.81081455,13.9970736 L8.73679257,13.9651082 L7.5,13.315 L6.26320743,13.9651082 C5.92084799,14.1450972 5.52456457,13.8857623 5.53020583,13.5182969 L5.53020583,13.5182969 L5.53773271,13.43802 L5.773,12.061 L4.77335286,11.0855007 C4.49779139,10.8168946 4.61930536,10.363149 4.96642759,10.2519856 L4.96642759,10.2519856 L5.05045955,10.232654 L6.433,10.032 Z M18.5,12 C18.7761424,12 19,12.2238576 19,12.5 C19,12.7761424 18.7761424,13 18.5,13 L17.5,13 C17.2238576,13 17,12.7761424 17,12.5 C17,12.2238576 17.2238576,12 17.5,12 L18.5,12 Z M18.5,9 C18.7761424,9 19,9.******** 19,9.5 C19,9.******** 18.7761424,10 18.5,10 L13.5,10 C13.2238576,10 13,9.******** 13,9.5 C13,9.******** 13.2238576,9 13.5,9 L18.5,9 Z"
                fill={color}
              />
            </G>
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(PulseAccountMemberShip);
