import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const CloseIcon = (props: IProps) => {
  const { width = '16px', height = '16px', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 16 16">
      <G
        id="illustarations-and-Icons"
        stroke="none"
        stroke-width="1"
        fill="none"
        fill-rule="evenodd"
      >
        <G id="Icons" transform="translate(-592.000000, -2998.000000)">
          <G
            id="atom-/-icon-/-line-/-custom-/-close"
            transform="translate(588.000000, 2994.000000)"
          >
            <Path
              d="M5.2843055,4.58859116 L5.35355339,4.64644661 L12,11.293 L18.6464466,4.64644661 L18.7156945,4.58859116 C18.9105626,4.45359511 19.179987,4.47288026 19.3535534,4.64644661 C19.5488155,4.84170876 19.5488155,5.15829124 19.3535534,5.35355339 L19.3535534,5.35355339 L12.707,12 L19.3535534,18.6464466 C19.5488155,18.8417088 19.5488155,19.1582912 19.3535534,19.3535534 C19.179987,19.5271197 18.9105626,19.5464049 18.7156945,19.4114088 L18.6464466,19.3535534 L12,12.707 L5.35355339,19.3535534 L5.2843055,19.4114088 C5.08943736,19.5464049 4.82001296,19.5271197 4.64644661,19.3535534 C4.45118446,19.1582912 4.45118446,18.8417088 4.64644661,18.6464466 L4.64644661,18.6464466 L11.293,12 L4.64644661,5.35355339 C4.45118446,5.15829124 4.45118446,4.84170876 4.64644661,4.64644661 C4.82001296,4.47288026 5.08943736,4.45359511 5.2843055,4.58859116 Z"
              id="Combined-Shape"
              fill={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(CloseIcon);
