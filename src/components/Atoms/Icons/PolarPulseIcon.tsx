import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const PolarPulseIcon = (props: IProps) => {
  const { width = '20', height = '16', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 20 16">
      <G stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <G transform="translate(-590.000000, -3910.000000)">
          <G transform="translate(588.000000, 3906.000000)">
            <Path
              d="M20,4 C21.1045695,4 22,4.8954305 22,6 L22,14 C22,15.1045695 21.1045695,16 20,16 L18.9724574,16.0007074 C18.7234136,18.2503421 16.8160341,20 14.5,20 C12.1839659,20 10.2765864,18.2503421 10.0275426,16.0007074 L4,16 C2.8954305,16 2,15.1045695 2,14 L2,6 C2,4.8954305 2.8954305,4 4,4 L20,4 Z M11.25,14.25 C10.5596441,14.25 10,14.8096441 10,15.5 C10,16.1903559 10.5596441,16.75 11.25,16.75 C11.8972087,16.75 12.4295339,16.2581253 12.4935464,15.6278052 L12.5,15.509 L12.5054857,15.6492623 C12.5818349,16.6841222 13.4456382,17.5 14.5,17.5 C15.6045695,17.5 16.5,16.6045695 16.5,15.5 C16.5,14.3954305 15.6045695,13.5 14.5,13.5 C13.3954305,13.5 12.5,14.3954305 12.5,15.5 C12.5,14.8096441 11.9403559,14.25 11.25,14.25 Z M20,5 L4,5 C3.48716416,5 3.06449284,5.38604019 3.00672773,5.88337887 L3,6 L3,14 C3,14.5128358 3.38604019,14.9355072 3.88337887,14.9932723 L4,15 L10.0274316,15.000297 C10.2760186,12.7501788 12.1836212,11 14.5,11 C16.8163788,11 18.7239814,12.7501788 18.9725684,15.000297 L20,15 C20.5128358,15 20.9355072,14.6139598 20.9932723,14.1166211 L21,14 L21,6 C21,5.48716416 20.6139598,5.06449284 20.1166211,5.00672773 L20,5 Z M6.5,9 C7.88071187,9 9,10.1192881 9,11.5 C9,11.7761424 8.77614237,12 8.5,12 C8.22385763,12 8,11.7761424 8,11.5 C8,10.6715729 7.32842712,10 6.5,10 C5.67157288,10 5,10.6715729 5,11.5 C5,11.7761424 4.77614237,12 4.5,12 C4.22385763,12 4,11.7761424 4,11.5 C4,10.1192881 5.11928813,9 6.5,9 Z M6.5,6 C7.05228475,6 7.5,6.44771525 7.5,7 C7.5,7.55228475 7.05228475,8 6.5,8 C5.94771525,8 5.5,7.55228475 5.5,7 C5.5,6.44771525 5.94771525,6 6.5,6 Z"
              fill={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(PolarPulseIcon);
