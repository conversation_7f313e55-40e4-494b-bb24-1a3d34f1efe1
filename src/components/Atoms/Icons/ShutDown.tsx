import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
  testID?: string;
}

const ShutDown = (props: IProps) => {
  const {
    width = '24',
    height = '24',
    color = 'black',
    testID = 'rightArrow',
  } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" testID={testID}>
      <G fill="none" fill-rule="evenodd">
        <Path fill="none" d="M0 0h24v24H0z" />
        <Path
          d="M15 7.38c1.8 1.02 3 3 3 5.22 0 3.3-2.7 6-6 6s-6-2.7-6-6c0-2.22 1.2-4.14 3-5.22m3-1.98v4.2"
          stroke-linecap="square"
          stroke={color}
        />
      </G>
    </Svg>
  );
};

export default React.memo(ShutDown);
