import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const PolarRFIDIcon = (props: IProps) => {
  const { width = '20', height = '20', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 20 20">
      <G stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <G transform="translate(-590.000000, -1556.000000)">
          <G transform="translate(588.000000, 1554.000000)">
            <Path
              d="M14,6 C14.2761424,6 14.5,6.22385763 14.5,6.5 C14.5,6.74545989 14.3231248,6.94960837 14.0898756,6.99194433 L14,7 L4.5,7 C3.72030388,7 3.07955132,7.59488808 3.00686658,8.35553999 L3,8.5 L3,19.5 C3,20.2796961 3.59488808,20.9204487 4.35553999,20.9931334 L4.5,21 L15.5,21 C16.2796961,21 16.9204487,20.4051119 16.9931334,19.64446 L17,19.5 L17,10 C17,9.72385763 17.2238576,9.5 17.5,9.5 C17.7454599,9.5 17.9496084,9.67687516 17.9919443,9.91012437 L18,10 L18,19.5 C18,20.8254834 16.9684641,21.9100387 15.6643757,21.9946823 L15.5,22 L4.5,22 C3.1745166,22 2.08996133,20.9684641 2.00531768,19.6643757 L2,19.5 L2,8.5 C2,7.1745166 3.03153594,6.08996133 4.33562431,6.00531768 L4.5,6 L14,6 Z M7.5,10.5 C8.88071187,10.5 10,11.6192881 10,13 C10,14.0880385 9.30493675,15.0137363 8.33461011,15.3572936 L9.9472136,17.7763932 C10.0707082,18.0233825 9.97059605,18.323719 9.7236068,18.4472136 C9.5040608,18.5569866 9.24236387,18.4900826 9.10018522,18.3003914 L9.0527864,18.2236068 L7.0527864,15.2236068 C6.92929178,14.9766175 7.02940395,14.676281 7.2763932,14.5527864 C7.32447207,14.528747 7.3745724,14.5131805 7.42499629,14.5055211 L7.5,14.5 C8.32842712,14.5 9,13.8284271 9,13 C9,12.2203039 8.40511192,11.5795513 7.64446001,11.5068666 L7.5,11.5 L6.5,11.5 C6.25454011,11.5 6.05039163,11.6768752 6.00805567,11.9101244 L6,12 L6,18 C6,18.2761424 5.77614237,18.5 5.5,18.5 C5.25454011,18.5 5.05039163,18.3231248 5.00805567,18.0898756 L5,18 L5,12 C5,11.2203039 5.59488808,10.5795513 6.35553999,10.5068666 L6.5,10.5 L7.5,10.5 Z M15,10.5 C15.2761424,10.5 15.5,10.7238576 15.5,11 C15.5,11.2454599 15.3231248,11.4496084 15.0898756,11.4919443 L15,11.5 L13,11.5 C12.7545401,11.5 12.5503916,11.6768752 12.5080557,11.9101244 L12.5,12 L12.5,13.5 L14,13.5 C14.2761424,13.5 14.5,13.7238576 14.5,14 C14.5,14.2454599 14.3231248,14.4496084 14.0898756,14.4919443 L14,14.5 L12.5,14.5 L12.5,18 C12.5,18.2761424 12.2761424,18.5 12,18.5 C11.7545401,18.5 11.5503916,18.3231248 11.5080557,18.0898756 L11.5,18 L11.5,12 C11.5,11.2203039 12.0948881,10.5795513 12.85554,10.5068666 L13,10.5 L15,10.5 Z M20.6819805,3.31801948 C22.4393398,5.0753788 22.4393398,7.9246212 20.6819805,9.68198052 C20.4867184,9.87724266 20.1701359,9.87724266 19.9748737,9.68198052 C19.7796116,9.48671837 19.7796116,9.17013588 19.9748737,8.97487373 C21.3417088,7.60803871 21.3417088,5.39196129 19.9748737,4.02512627 C18.6080387,2.65829124 16.3919613,2.65829124 15.0251263,4.02512627 C14.8298641,4.22038841 14.5132816,4.22038841 14.3180195,4.02512627 C14.1227573,3.82986412 14.1227573,3.51328163 14.3180195,3.31801948 C16.0753788,1.56066017 18.9246212,1.56066017 20.6819805,3.31801948 Z M17.5,5 C18.3284271,5 19,5.67157288 19,6.5 C19,7.32842712 18.3284271,8 17.5,8 C16.6715729,8 16,7.32842712 16,6.5 C16,5.67157288 16.6715729,5 17.5,5 Z M17.5,6 C17.2238576,6 17,6.22385763 17,6.5 C17,6.77614237 17.2238576,7 17.5,7 C17.7761424,7 18,6.77614237 18,6.5 C18,6.22385763 17.7761424,6 17.5,6 Z"
              fill={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(PolarRFIDIcon);
