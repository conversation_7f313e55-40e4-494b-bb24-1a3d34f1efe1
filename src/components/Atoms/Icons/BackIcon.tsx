import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
  testID?: string;
}

const BackIcon = (props: IProps) => {
  const {
    width = '24',
    height = '24',
    color = 'black',
    testID = 'backIcon',
  } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" testID={testID}>
      <G fill="none" fillRule="evenodd">
        <Path d="M0 0h24v24H0z" />
        <Path
          d="M15.354 5.646a.501.501 0 01.057.638l-.057.07L9.707 12l5.647 5.646a.501.501 0 01.057.638l-.057.07a.501.501 0 01-.638.057l-.07-.057-6-6a.501.501 0 01-.057-.638l.057-.07 6-6a.502.502 0 01.708 0z"
          fill={color}
        />
      </G>
    </Svg>
  );
};

export default React.memo(BackIcon);
