import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const PolarPlusIcon = (props: IProps) => {
  const { width = '20', height = '16', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height}>
      <G
        id="illustarations-and-Icons"
        stroke="none"
        stroke-width="1"
        fill="none"
        fill-rule="evenodd"
      >
        <G id="Icons" transform="translate(-590.000000, -2614.000000)">
          <G
            id="atom-/-icon-/-line-/-custom-/-pulse-credit"
            transform="translate(588.000000, 2610.000000)"
          >
            <Path
              d="M20,4 C21.1045695,4 22,4.8954305 22,6 L22,14 C22,15.1045695 21.1045695,16 20,16 L18.9724574,16.0007074 C18.7234136,18.2503421 16.8160341,20 14.5,20 C12.1839659,20 10.2765864,18.2503421 10.0275426,16.0007074 L4,16 C2.8954305,16 2,15.1045695 2,14 L2,6 C2,4.8954305 2.8954305,4 4,4 L20,4 Z M14.5,13.5 C13.3954305,13.5 12.5,14.3954305 12.5,15.5 C12.5,16.6045695 13.3954305,17.5 14.5,17.5 C15.6045695,17.5 16.5,16.6045695 16.5,15.5 C16.5,14.3954305 15.6045695,13.5 14.5,13.5 Z M11.25,14.25 C10.5596441,14.25 10,14.8096441 10,15.5 C10,16.1903559 10.5596441,16.75 11.25,16.75 C11.9403559,16.75 12.5,16.1903559 12.5,15.5 C12.5,14.8096441 11.9403559,14.25 11.25,14.25 Z M20,5 L4,5 C3.48716416,5 3.06449284,5.38604019 3.00672773,5.88337887 L3,6 L3,14 C3,14.5128358 3.38604019,14.9355072 3.88337887,14.9932723 L4,15 L10.0274316,15.000297 C10.2760186,12.7501788 12.1836212,11 14.5,11 C16.8163788,11 18.7239814,12.7501788 18.9725684,15.000297 L20,15 C20.5128358,15 20.9355072,14.6139598 20.9932723,14.1166211 L21,14 L21,6 C21,5.48716416 20.6139598,5.06449284 20.1166211,5.00672773 L20,5 Z M6.5,10 C6.77614237,10 7,10.2238576 7,10.5 C7,10.7761424 6.77614237,11 6.5,11 L5.5,11 C5.22385763,11 5,10.7761424 5,10.5 C5,10.2238576 5.22385763,10 5.5,10 L6.5,10 Z M10.5,7 C10.7761424,7 11,7.22385763 11,7.5 C11,7.77614237 10.7761424,8 10.5,8 L5.5,8 C5.22385763,8 5,7.77614237 5,7.5 C5,7.22385763 5.22385763,7 5.5,7 L10.5,7 Z"
              id="Combined-Shape"
              fill={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(PolarPlusIcon);
