import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const DetailIcon = (props: IProps) => {
  const { width = '16', height = '20', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height}>
      <G
        id="illustarations-and-Icons"
        stroke="none"
        stroke-width="1"
        fill="none"
        fill-rule="evenodd"
      >
        <G id="Icons" transform="translate(-592.000000, -3332.000000)">
          <G
            id="atom-/-icon-/-line-/-custom-/-your-details"
            transform="translate(588.000000, 3330.000000)"
          >
            <Path
              d="M18,2 C19.1045695,2 20,2.8954305 20,4 L20,20 C20,21.1045695 19.1045695,22 18,22 L6,22 C4.8954305,22 4,21.1045695 4,20 L4,4 C4,2.8954305 4.8954305,2 6,2 L18,2 Z M18,3 L6,3 C5.48716416,3 5.06449284,3.38604019 5.00672773,3.88337887 L5,4 L5,20 C5,20.5128358 5.38604019,20.9355072 5.88337887,20.9932723 L6,21 L18,21 C18.5128358,21 18.9355072,20.6139598 18.9932723,20.1166211 L19,20 L19,4 C19,3.48716416 18.6139598,3.06449284 18.1166211,3.00672773 L18,3 Z M16.5,8 C16.7761424,8 17,8.22385763 17,8.5 C17,8.77614237 16.7761424,9 16.5,9 L7.5,9 C7.22385763,9 7,8.77614237 7,8.5 C7,8.22385763 7.22385763,8 7.5,8 L16.5,8 Z M16.5,5 C16.7761424,5 17,5.22385763 17,5.5 C17,5.77614237 16.7761424,6 16.5,6 L7.5,6 C7.22385763,6 7,5.77614237 7,5.5 C7,5.22385763 7.22385763,5 7.5,5 L16.5,5 Z"
              id="Combined-Shape"
              fill={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(DetailIcon);
