import { render } from '@testing-library/react-native';
import React from 'react';

import * as Icons from './';

const testProps = {
  color: '#000',
  width: '24px',
};

Object.values(Icons).map((Icon) => {
  describe('<Icon /> component', () => {
    test('its snapshot matches', () => {
      const icon = render(<Icon {...testProps} />);
      expect(icon.toJSON()).toMatchSnapshot();
    });
  });
});
