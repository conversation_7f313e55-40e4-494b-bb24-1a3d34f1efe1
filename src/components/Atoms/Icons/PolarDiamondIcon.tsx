import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const PolarDiamondIcon = (props: IProps) => {
  const { width = '24', height = '24', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height}>
      <G stroke-width="1" fill="none" fill-rule="evenodd">
        <G transform="translate(-592.000000, -1079.000000)">
          <G transform="translate(588.000000, 1074.000000)">
            <Path d="M9 9h6m3 0-6 8.4L6 9l2.4-3h7.2L18 9z" stroke={color} />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(PolarDiamondIcon);
