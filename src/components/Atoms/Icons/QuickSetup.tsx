import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const QuickSetup = (props: IProps) => {
  const { width = '24', height = '24', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height}>
      <G fill="none" fill-rule="evenodd">
        <G transform="translate(-592.000000, -1079.000000)">
          <G transform="translate(588.000000, 1074.000000)">
            <Path fill="none" d="M0 0h24v24H0z" />
            <Path
              d="M12 19.15a7.15 7.15 0 1 0 0-14.3 7.15 7.15 0 0 0 0 14.3zm0-10.7v4h4"
              stroke-linecap="square"
              stroke={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(QuickSetup);
