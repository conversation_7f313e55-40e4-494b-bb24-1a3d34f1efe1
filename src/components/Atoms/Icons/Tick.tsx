import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const Tick = (props: IProps) => {
  const { width = '18', height = '15', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 18 15">
      <G
        id="illustarations-and-Icons"
        stroke="none"
        stroke-width="1"
        fill="none"
        fill-rule="evenodd"
      >
        <G id="Icons" transform="translate(-591.000000, -2951.000000)">
          <G
            id="atom-/-icon-/-line-/-custom-/-tick"
            transform="translate(588.000000, 2946.000000)"
          >
            <Path
              d="M19.603688,5.69514462 C19.772055,5.47626755 20.0859783,5.43532103 20.3048554,5.60368801 C20.4994128,5.75334754 20.5533831,6.01801456 20.444725,6.22870617 L20.396312,6.30485538 L10.396312,19.3048554 C10.2327348,19.5175057 9.93610389,19.559217 9.72231328,19.4159688 L9.64644661,19.3535534 L3.64644661,13.3535534 C3.45118446,13.1582912 3.45118446,12.8417088 3.64644661,12.6464466 C3.82001296,12.4728803 4.08943736,12.4535951 4.2843055,12.5885912 L4.35355339,12.6464466 L9.95,18.243 L19.603688,5.69514462 Z"
              id="Path-21"
              fill={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(Tick);
