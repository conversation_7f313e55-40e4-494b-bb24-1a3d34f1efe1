import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const ChargingOnGoIcon = (props: IProps) => {
  const { width = '24', height = '24', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height}>
      <G stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <G transform="translate(-590.000000, -644.000000)">
          <G transform="translate(588.000000, 642.000000)">
            <G transform="translate(2.000000, 2.000000)">
              <Path
                d="M15.0658784,0.251930531 C15.3381714,-0.224582136 16.0681486,0.0220722076 15.995614,0.56608186 L15.995614,0.56608186 L15.07,7.5 L19.5018094,7.5 C19.8484189,7.5 20.080858,7.83950965 19.9779221,8.15356538 L19.9779221,8.15356538 L19.9450732,8.23133782 L13.9432638,19.7313378 C13.6863867,20.2235371 12.9385545,19.9929757 13.0034247,19.4415794 L13.0034247,19.4415794 L13.937,11.5 L9.5,11.5 C9.14562031,11.5 8.91328081,11.1466111 9.02944566,10.8300718 L9.02944566,10.8300718 L9.06587843,10.7519305 Z M14.666,2.967 L10.361,10.5 L14.5,10.5 C14.769367,10.5 14.9844253,10.7115509 14.99925,10.9705539 L14.99925,10.9705539 L14.9965753,11.0584206 L14.314,16.858 L18.676,8.5 L14.5,8.5 C14.2278448,8.5 14.0117495,8.2842647 14.0003869,8.02260574 L14.0003869,8.02260574 L14.004386,7.93391814 L14.666,2.967 Z M7.5,14 C7.77614237,14 8,14.2238576 8,14.5 C8,14.7761424 7.77614237,15 7.5,15 L4.5,15 C4.22385763,15 4,14.7761424 4,14.5 C4,14.2238576 4.22385763,14 4.5,14 L7.5,14 Z M7.5,7 C7.77614237,7 8,7.22385763 8,7.5 C8,7.77614237 7.77614237,8 7.5,8 L0.5,8 C0.223857625,8 3.38176876e-17,7.77614237 0,7.5 C-3.38176876e-17,7.22385763 0.223857625,7 0.5,7 L7.5,7 Z M11.5,3 C11.7761424,3 12,3.22385763 12,3.5 C12,3.77614237 11.7761424,4 11.5,4 L4.5,4 C4.22385763,4 4,3.77614237 4,3.5 C4,3.22385763 4.22385763,3 4.5,3 L11.5,3 Z"
                fill={color}
              />
            </G>
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(ChargingOnGoIcon);
