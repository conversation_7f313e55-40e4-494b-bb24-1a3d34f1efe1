// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Icon /> component its snapshot matches 1`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  testID="backIcon"
  vbHeight={24}
  vbWidth={24}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      fillRule={0}
      propList={
        [
          "fill",
          "fillRule",
        ]
      }
    >
      <RNSVGPath
        d="M0 0h24v24H0z"
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
      />
      <RNSVGPath
        d="M15.354 5.646a.501.501 0 01.057.638l-.057.07L9.707 12l5.647 5.646a.501.501 0 01.057.638l-.057.07a.501.501 0 01-.638.057l-.07-.057-6-6a.501.501 0 01-.057-.638l.057-.07 6-6a.502.502 0 01.708 0z"
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        propList={
          [
            "fill",
          ]
        }
      />
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 2`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  vbHeight={24}
  vbWidth={24}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
        ]
      }
    >
      <RNSVGPath
        d="M0 0h24v24H0z"
        fill={null}
        propList={
          [
            "fill",
          ]
        }
      />
      <RNSVGPath
        d="M14.777 15.076H9.05m7.51 1.348a1.856 1.856 0 1 1 0-3.712 1.856 1.856 0 0 1 0 3.712zm-9.302 0a1.856 1.856 0 1 1 0-3.712 1.856 1.856 0 0 1 0 3.712zM5.43 14.883l-1.429-.829v-3.197l.8-1.875-.4-.937c2.08-.245 4.144-.469 5.772-.469 2.582 0 4.739 1.132 6.626 2.813 2.628.528 3.2 1.814 3.2 2.343v2.344h-1.655"
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        propList={
          [
            "stroke",
          ]
        }
        stroke={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
      />
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 3`] = `
<RNSVGSvgView
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -590,
            -644,
          ]
        }
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              642,
            ]
          }
        >
          <RNSVGGroup
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            matrix={
              [
                1,
                0,
                0,
                1,
                2,
                2,
              ]
            }
          >
            <RNSVGPath
              d="M15.0658784,0.251930531 C15.3381714,-0.224582136 16.0681486,0.0220722076 15.995614,0.56608186 L15.995614,0.56608186 L15.07,7.5 L19.5018094,7.5 C19.8484189,7.5 20.080858,7.83950965 19.9779221,8.15356538 L19.9779221,8.15356538 L19.9450732,8.23133782 L13.9432638,19.7313378 C13.6863867,20.2235371 12.9385545,19.9929757 13.0034247,19.4415794 L13.0034247,19.4415794 L13.937,11.5 L9.5,11.5 C9.14562031,11.5 8.91328081,11.1466111 9.02944566,10.8300718 L9.02944566,10.8300718 L9.06587843,10.7519305 Z M14.666,2.967 L10.361,10.5 L14.5,10.5 C14.769367,10.5 14.9844253,10.7115509 14.99925,10.9705539 L14.99925,10.9705539 L14.9965753,11.0584206 L14.314,16.858 L18.676,8.5 L14.5,8.5 C14.2278448,8.5 14.0117495,8.2842647 14.0003869,8.02260574 L14.0003869,8.02260574 L14.004386,7.93391814 L14.666,2.967 Z M7.5,14 C7.77614237,14 8,14.2238576 8,14.5 C8,14.7761424 7.77614237,15 7.5,15 L4.5,15 C4.22385763,15 4,14.7761424 4,14.5 C4,14.2238576 4.22385763,14 4.5,14 L7.5,14 Z M7.5,7 C7.77614237,7 8,7.22385763 8,7.5 C8,7.77614237 7.77614237,8 7.5,8 L0.5,8 C0.223857625,8 3.38176876e-17,7.77614237 0,7.5 C-3.38176876e-17,7.22385763 0.223857625,7 0.5,7 L7.5,7 Z M11.5,3 C11.7761424,3 12,3.22385763 12,3.5 C12,3.77614237 11.7761424,4 11.5,4 L4.5,4 C4.22385763,4 4,3.77614237 4,3.5 C4,3.22385763 4.22385763,3 4.5,3 L11.5,3 Z"
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
              propList={
                [
                  "fill",
                ]
              }
            />
          </RNSVGGroup>
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 4`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="16px"
  bbWidth="24px"
  focusable={false}
  height="16px"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 16,
        "width": 24,
      },
    ]
  }
  vbHeight={16}
  vbWidth={16}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      name="illustarations-and-Icons"
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -592,
            -2998,
          ]
        }
        name="Icons"
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              2994,
            ]
          }
          name="atom-/-icon-/-line-/-custom-/-close"
        >
          <RNSVGPath
            d="M5.2843055,4.58859116 L5.35355339,4.64644661 L12,11.293 L18.6464466,4.64644661 L18.7156945,4.58859116 C18.9105626,4.45359511 19.179987,4.47288026 19.3535534,4.64644661 C19.5488155,4.84170876 19.5488155,5.15829124 19.3535534,5.35355339 L19.3535534,5.35355339 L12.707,12 L19.3535534,18.6464466 C19.5488155,18.8417088 19.5488155,19.1582912 19.3535534,19.3535534 C19.179987,19.5271197 18.9105626,19.5464049 18.7156945,19.4114088 L18.6464466,19.3535534 L12,12.707 L5.35355339,19.3535534 L5.2843055,19.4114088 C5.08943736,19.5464049 4.82001296,19.5271197 4.64644661,19.3535534 C4.45118446,19.1582912 4.45118446,18.8417088 4.64644661,18.6464466 L4.64644661,18.6464466 L11.293,12 L4.64644661,5.35355339 C4.45118446,5.15829124 4.45118446,4.84170876 4.64644661,4.64644661 C4.82001296,4.47288026 5.08943736,4.45359511 5.2843055,4.58859116 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            name="Combined-Shape"
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 5`] = `
<RNSVGSvgView
  bbHeight="20"
  bbWidth="24px"
  focusable={false}
  height="20"
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 20,
        "width": 24,
      },
    ]
  }
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      name="illustarations-and-Icons"
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -592,
            -3332,
          ]
        }
        name="Icons"
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              3330,
            ]
          }
          name="atom-/-icon-/-line-/-custom-/-your-details"
        >
          <RNSVGPath
            d="M18,2 C19.1045695,2 20,2.8954305 20,4 L20,20 C20,21.1045695 19.1045695,22 18,22 L6,22 C4.8954305,22 4,21.1045695 4,20 L4,4 C4,2.8954305 4.8954305,2 6,2 L18,2 Z M18,3 L6,3 C5.48716416,3 5.06449284,3.38604019 5.00672773,3.88337887 L5,4 L5,20 C5,20.5128358 5.38604019,20.9355072 5.88337887,20.9932723 L6,21 L18,21 C18.5128358,21 18.9355072,20.6139598 18.9932723,20.1166211 L19,20 L19,4 C19,3.48716416 18.6139598,3.06449284 18.1166211,3.00672773 L18,3 Z M16.5,8 C16.7761424,8 17,8.22385763 17,8.5 C17,8.77614237 16.7761424,9 16.5,9 L7.5,9 C7.22385763,9 7,8.77614237 7,8.5 C7,8.22385763 7.22385763,8 7.5,8 L16.5,8 Z M16.5,5 C16.7761424,5 17,5.22385763 17,5.5 C17,5.77614237 16.7761424,6 16.5,6 L7.5,6 C7.22385763,6 7,5.77614237 7,5.5 C7,5.22385763 7.22385763,5 7.5,5 L16.5,5 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            name="Combined-Shape"
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 6`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="8"
  bbWidth="24px"
  focusable={false}
  height="8"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 8,
        "width": 24,
      },
    ]
  }
  vbHeight={8}
  vbWidth={14}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      name="illustarations-and-Icons"
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -593,
            -1706,
          ]
        }
        name="Icons"
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              1698,
            ]
          }
          name="atom-/-icon-/-line-/-custom-/-dropdown"
        >
          <RNSVGPath
            d="M5.64644661,8.64644661 C5.82001296,8.47288026 6.08943736,8.45359511 6.2843055,8.58859116 L6.35355339,8.64644661 L12,14.293 L17.6464466,8.64644661 C17.820013,8.47288026 18.0894374,8.45359511 18.2843055,8.58859116 L18.3535534,8.64644661 C18.5271197,8.82001296 18.5464049,9.08943736 18.4114088,9.2843055 L18.3535534,9.35355339 L12.3535534,15.3535534 C12.179987,15.5271197 11.9105626,15.5464049 11.7156945,15.4114088 L11.6464466,15.3535534 L5.64644661,9.35355339 C5.45118446,9.15829124 5.45118446,8.84170876 5.64644661,8.64644661 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            name="Path-8"
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 7`] = `
<RNSVGSvgView
  bbHeight="32"
  bbWidth="32"
  focusable={false}
  height="32"
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 32,
        "width": 32,
      },
    ]
  }
  width="32"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGPath
      d="M16 7c-4.962 0-9 4.038-9 9s4.038 9 9 9 9-4.038 9-9-4.038-9-9-9zm1.085 3.938-.523 6.75h-1.125l-.522-6.75h2.17zM16 21.062a1.125 1.125 0 1 1 0-2.249 1.125 1.125 0 0 1 0 2.25z"
      fill={
        {
          "payload": 4291559424,
          "type": 0,
        }
      }
      propList={
        [
          "fill",
        ]
      }
    />
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 8`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  vbHeight={24}
  vbWidth={24}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
        ]
      }
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -592,
            -1079,
          ]
        }
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              1074,
            ]
          }
        >
          <RNSVGGroup
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            matrix={
              [
                1,
                0,
                0,
                1,
                2,
                2,
              ]
            }
          >
            <RNSVGPath
              d="M0 0h24v24H0z"
              fill={null}
              propList={
                [
                  "fill",
                ]
              }
            />
            <RNSVGPath
              d="M18.587 11.706 12.291 5.41A1.409 1.409 0 0 0 11.3 5H6.4A1.4 1.4 0 0 0 5 6.4v4.9c0 .389.157.738.413.99l6.3 6.3a1.394 1.394 0 0 0 1.978 0l4.9-4.9a1.4 1.4 0 0 0-.003-1.985zM8.5 9.5c-.553 0-1-.447-1-1 0-.553.447-1 1-1 .553 0 1 .447 1 1 0 .553-.447 1-1 1z"
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
              propList={
                [
                  "stroke",
                ]
              }
              stroke={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            />
          </RNSVGGroup>
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 9`] = `
<RNSVGSvgView
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
        ]
      }
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -592,
            -1079,
          ]
        }
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              1074,
            ]
          }
        >
          <RNSVGPath
            d="M9 9h6m3 0-6 8.4L6 9l2.4-3h7.2L18 9z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            propList={
              [
                "stroke",
              ]
            }
            stroke={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 10`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  meetOrSlice={0}
  minX={3}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  vbHeight={24}
  vbWidth={24}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      name="8.-📺-Subscriptions-🟠"
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -16,
            -299,
          ]
        }
        name="Subscription-page---uplift---Subs-wallet-+-RFID"
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              16,
              299,
            ]
          }
          name="atom-/-icon-/-line-/-custom-/-peace-of-mind"
        >
          <RNSVGRect
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            height="24"
            name="Rectangle"
            width="24"
            x="0"
            y="0"
          />
          <RNSVGPath
            d="M12,4 C16.418278,4 20,7.581722 20,12 C20,16.418278 16.418278,20 12,20 C7.581722,20 4,16.418278 4,12 C4,7.581722 7.581722,4 12,4 Z M12,5 C8.13400675,5 5,8.13400675 5,12 C5,15.8659932 8.13400675,19 12,19 C15.8659932,19 19,15.8659932 19,12 C19,8.13400675 15.8659932,5 12,5 Z M16,12 C16.2761424,12 16.5,12.2238576 16.5,12.5 C16.5,14.9852814 14.4852814,17 12,17 C9.51471863,17 7.5,14.9852814 7.5,12.5 C7.5,12.2238576 7.72385763,12 8,12 Z M15.463,13 L8.536,13 L8.54580909,13.0677182 C8.80701426,14.6691343 10.1550039,15.9054397 11.8079648,15.9948211 L12,16 C13.739697,16 15.1829394,14.7307273 15.4541909,13.0677182 L15.463,13 Z M9,8 C9.55228475,8 10,8.44771525 10,9 C10,9.55228475 9.55228475,10 9,10 C8.44771525,10 8,9.55228475 8,9 C8,8.44771525 8.44771525,8 9,8 Z M15,8 C15.5522847,8 16,8.44771525 16,9 C16,9.55228475 15.5522847,10 15,10 C14.4477153,10 14,9.55228475 14,9 C14,8.44771525 14.4477153,8 15,8 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            name="Combined-Shape"
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 11`] = `
<RNSVGSvgView
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -592,
            -1079,
          ]
        }
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              1074,
            ]
          }
        >
          <RNSVGPath
            d="M16,5 C17.1045695,5 18,5.8954305 18,7 L18,8 C19.1045695,8 20,8.8954305 20,10 L20,14 C20,15.1045695 19.1045695,16 18,16 L18,17 C18,18.1045695 17.1045695,19 16,19 L6,19 C4.8954305,19 4,18.1045695 4,17 L4,7 C4,5.8954305 4.8954305,5 6,5 L16,5 Z M16,6 L6,6 C5.48716416,6 5.06449284,6.38604019 5.00672773,6.88337887 L5,7 L5,17 C5,17.5128358 5.38604019,17.9355072 5.88337887,17.9932723 L6,18 L16,18 C16.5128358,18 16.9355072,17.6139598 16.9932723,17.1166211 L17,17 L17,16 L14,16 C12.8954305,16 12,15.1045695 12,14 L12,10 C12,8.8954305 12.8954305,8 14,8 L17,8 L17,7 C17,6.48716416 16.6139598,6.06449284 16.1166211,6.00672773 L16,6 Z M18,9 L14,9 C13.4871642,9 13.0644928,9.38604019 13.0067277,9.88337887 L13,10 L13,14 C13,14.5128358 13.3860402,14.9355072 13.8833789,14.9932723 L14,15 L18,15 C18.5128358,15 18.9355072,14.6139598 18.9932723,14.1166211 L19,14 L19,10 C19,9.48716416 18.6139598,9.06449284 18.1166211,9.00672773 L18,9 Z M15,11 C15.5522847,11 16,11.4477153 16,12 C16,12.5522847 15.5522847,13 15,13 C14.4477153,13 14,12.5522847 14,12 C14,11.4477153 14.4477153,11 15,11 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 12`] = `
<RNSVGSvgView
  bbHeight="16"
  bbWidth="24px"
  focusable={false}
  height="16"
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 16,
        "width": 24,
      },
    ]
  }
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      name="illustarations-and-Icons"
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -590,
            -2614,
          ]
        }
        name="Icons"
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              2610,
            ]
          }
          name="atom-/-icon-/-line-/-custom-/-pulse-credit"
        >
          <RNSVGPath
            d="M20,4 C21.1045695,4 22,4.8954305 22,6 L22,14 C22,15.1045695 21.1045695,16 20,16 L18.9724574,16.0007074 C18.7234136,18.2503421 16.8160341,20 14.5,20 C12.1839659,20 10.2765864,18.2503421 10.0275426,16.0007074 L4,16 C2.8954305,16 2,15.1045695 2,14 L2,6 C2,4.8954305 2.8954305,4 4,4 L20,4 Z M14.5,13.5 C13.3954305,13.5 12.5,14.3954305 12.5,15.5 C12.5,16.6045695 13.3954305,17.5 14.5,17.5 C15.6045695,17.5 16.5,16.6045695 16.5,15.5 C16.5,14.3954305 15.6045695,13.5 14.5,13.5 Z M11.25,14.25 C10.5596441,14.25 10,14.8096441 10,15.5 C10,16.1903559 10.5596441,16.75 11.25,16.75 C11.9403559,16.75 12.5,16.1903559 12.5,15.5 C12.5,14.8096441 11.9403559,14.25 11.25,14.25 Z M20,5 L4,5 C3.48716416,5 3.06449284,5.38604019 3.00672773,5.88337887 L3,6 L3,14 C3,14.5128358 3.38604019,14.9355072 3.88337887,14.9932723 L4,15 L10.0274316,15.000297 C10.2760186,12.7501788 12.1836212,11 14.5,11 C16.8163788,11 18.7239814,12.7501788 18.9725684,15.000297 L20,15 C20.5128358,15 20.9355072,14.6139598 20.9932723,14.1166211 L21,14 L21,6 C21,5.48716416 20.6139598,5.06449284 20.1166211,5.00672773 L20,5 Z M6.5,10 C6.77614237,10 7,10.2238576 7,10.5 C7,10.7761424 6.77614237,11 6.5,11 L5.5,11 C5.22385763,11 5,10.7761424 5,10.5 C5,10.2238576 5.22385763,10 5.5,10 L6.5,10 Z M10.5,7 C10.7761424,7 11,7.22385763 11,7.5 C11,7.77614237 10.7761424,8 10.5,8 L5.5,8 C5.22385763,8 5,7.77614237 5,7.5 C5,7.22385763 5.22385763,7 5.5,7 L10.5,7 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            name="Combined-Shape"
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 13`] = `
<RNSVGSvgView
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -592,
            -1126,
          ]
        }
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              1122,
            ]
          }
        >
          <RNSVGPath
            d="M12,4 C16.418278,4 20,7.581722 20,12 C20,16.418278 16.418278,20 12,20 C7.581722,20 4,16.418278 4,12 C4,7.581722 7.581722,4 12,4 Z M12,5 C8.13400675,5 5,8.13400675 5,12 C5,15.8659932 8.13400675,19 12,19 C15.8659932,19 19,15.8659932 19,12 C19,8.13400675 15.8659932,5 12,5 Z M16,12 C16.2761424,12 16.5,12.2238576 16.5,12.5 C16.5,14.9852814 14.4852814,17 12,17 C9.51471863,17 7.5,14.9852814 7.5,12.5 C7.5,12.2238576 7.72385763,12 8,12 L8,12 Z M15.463,13 L8.536,13 L8.54580909,13.0677182 C8.80701426,14.6691343 10.1550039,15.9054397 11.8079648,15.9948211 L11.8079648,15.9948211 L12,16 C13.739697,16 15.1829394,14.7307273 15.4541909,13.0677182 L15.4541909,13.0677182 L15.463,13 Z M9,8 C9.55228475,8 10,8.44771525 10,9 C10,9.55228475 9.55228475,10 9,10 C8.44771525,10 8,9.55228475 8,9 C8,8.44771525 8.44771525,8 9,8 Z M15,8 C15.5522847,8 16,8.44771525 16,9 C16,9.55228475 15.5522847,10 15,10 C14.4477153,10 14,9.55228475 14,9 C14,8.44771525 14.4477153,8 15,8 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 14`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="16"
  bbWidth="24px"
  focusable={false}
  height="16"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 16,
        "width": 24,
      },
    ]
  }
  vbHeight={16}
  vbWidth={20}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -590,
            -3910,
          ]
        }
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              3906,
            ]
          }
        >
          <RNSVGPath
            d="M20,4 C21.1045695,4 22,4.8954305 22,6 L22,14 C22,15.1045695 21.1045695,16 20,16 L18.9724574,16.0007074 C18.7234136,18.2503421 16.8160341,20 14.5,20 C12.1839659,20 10.2765864,18.2503421 10.0275426,16.0007074 L4,16 C2.8954305,16 2,15.1045695 2,14 L2,6 C2,4.8954305 2.8954305,4 4,4 L20,4 Z M11.25,14.25 C10.5596441,14.25 10,14.8096441 10,15.5 C10,16.1903559 10.5596441,16.75 11.25,16.75 C11.8972087,16.75 12.4295339,16.2581253 12.4935464,15.6278052 L12.5,15.509 L12.5054857,15.6492623 C12.5818349,16.6841222 13.4456382,17.5 14.5,17.5 C15.6045695,17.5 16.5,16.6045695 16.5,15.5 C16.5,14.3954305 15.6045695,13.5 14.5,13.5 C13.3954305,13.5 12.5,14.3954305 12.5,15.5 C12.5,14.8096441 11.9403559,14.25 11.25,14.25 Z M20,5 L4,5 C3.48716416,5 3.06449284,5.38604019 3.00672773,5.88337887 L3,6 L3,14 C3,14.5128358 3.38604019,14.9355072 3.88337887,14.9932723 L4,15 L10.0274316,15.000297 C10.2760186,12.7501788 12.1836212,11 14.5,11 C16.8163788,11 18.7239814,12.7501788 18.9725684,15.000297 L20,15 C20.5128358,15 20.9355072,14.6139598 20.9932723,14.1166211 L21,14 L21,6 C21,5.48716416 20.6139598,5.06449284 20.1166211,5.00672773 L20,5 Z M6.5,9 C7.88071187,9 9,10.1192881 9,11.5 C9,11.7761424 8.77614237,12 8.5,12 C8.22385763,12 8,11.7761424 8,11.5 C8,10.6715729 7.32842712,10 6.5,10 C5.67157288,10 5,10.6715729 5,11.5 C5,11.7761424 4.77614237,12 4.5,12 C4.22385763,12 4,11.7761424 4,11.5 C4,10.1192881 5.11928813,9 6.5,9 Z M6.5,6 C7.05228475,6 7.5,6.44771525 7.5,7 C7.5,7.55228475 7.05228475,8 6.5,8 C5.94771525,8 5.5,7.55228475 5.5,7 C5.5,6.44771525 5.94771525,6 6.5,6 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 15`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="20"
  bbWidth="24px"
  focusable={false}
  height="20"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 20,
        "width": 24,
      },
    ]
  }
  vbHeight={20}
  vbWidth={20}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -590,
            -1556,
          ]
        }
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              1554,
            ]
          }
        >
          <RNSVGPath
            d="M14,6 C14.2761424,6 14.5,6.22385763 14.5,6.5 C14.5,6.74545989 14.3231248,6.94960837 14.0898756,6.99194433 L14,7 L4.5,7 C3.72030388,7 3.07955132,7.59488808 3.00686658,8.35553999 L3,8.5 L3,19.5 C3,20.2796961 3.59488808,20.9204487 4.35553999,20.9931334 L4.5,21 L15.5,21 C16.2796961,21 16.9204487,20.4051119 16.9931334,19.64446 L17,19.5 L17,10 C17,9.72385763 17.2238576,9.5 17.5,9.5 C17.7454599,9.5 17.9496084,9.67687516 17.9919443,9.91012437 L18,10 L18,19.5 C18,20.8254834 16.9684641,21.9100387 15.6643757,21.9946823 L15.5,22 L4.5,22 C3.1745166,22 2.08996133,20.9684641 2.00531768,19.6643757 L2,19.5 L2,8.5 C2,7.1745166 3.03153594,6.08996133 4.33562431,6.00531768 L4.5,6 L14,6 Z M7.5,10.5 C8.88071187,10.5 10,11.6192881 10,13 C10,14.0880385 9.30493675,15.0137363 8.33461011,15.3572936 L9.9472136,17.7763932 C10.0707082,18.0233825 9.97059605,18.323719 9.7236068,18.4472136 C9.5040608,18.5569866 9.24236387,18.4900826 9.10018522,18.3003914 L9.0527864,18.2236068 L7.0527864,15.2236068 C6.92929178,14.9766175 7.02940395,14.676281 7.2763932,14.5527864 C7.32447207,14.528747 7.3745724,14.5131805 7.42499629,14.5055211 L7.5,14.5 C8.32842712,14.5 9,13.8284271 9,13 C9,12.2203039 8.40511192,11.5795513 7.64446001,11.5068666 L7.5,11.5 L6.5,11.5 C6.25454011,11.5 6.05039163,11.6768752 6.00805567,11.9101244 L6,12 L6,18 C6,18.2761424 5.77614237,18.5 5.5,18.5 C5.25454011,18.5 5.05039163,18.3231248 5.00805567,18.0898756 L5,18 L5,12 C5,11.2203039 5.59488808,10.5795513 6.35553999,10.5068666 L6.5,10.5 L7.5,10.5 Z M15,10.5 C15.2761424,10.5 15.5,10.7238576 15.5,11 C15.5,11.2454599 15.3231248,11.4496084 15.0898756,11.4919443 L15,11.5 L13,11.5 C12.7545401,11.5 12.5503916,11.6768752 12.5080557,11.9101244 L12.5,12 L12.5,13.5 L14,13.5 C14.2761424,13.5 14.5,13.7238576 14.5,14 C14.5,14.2454599 14.3231248,14.4496084 14.0898756,14.4919443 L14,14.5 L12.5,14.5 L12.5,18 C12.5,18.2761424 12.2761424,18.5 12,18.5 C11.7545401,18.5 11.5503916,18.3231248 11.5080557,18.0898756 L11.5,18 L11.5,12 C11.5,11.2203039 12.0948881,10.5795513 12.85554,10.5068666 L13,10.5 L15,10.5 Z M20.6819805,3.31801948 C22.4393398,5.0753788 22.4393398,7.9246212 20.6819805,9.68198052 C20.4867184,9.87724266 20.1701359,9.87724266 19.9748737,9.68198052 C19.7796116,9.48671837 19.7796116,9.17013588 19.9748737,8.97487373 C21.3417088,7.60803871 21.3417088,5.39196129 19.9748737,4.02512627 C18.6080387,2.65829124 16.3919613,2.65829124 15.0251263,4.02512627 C14.8298641,4.22038841 14.5132816,4.22038841 14.3180195,4.02512627 C14.1227573,3.82986412 14.1227573,3.51328163 14.3180195,3.31801948 C16.0753788,1.56066017 18.9246212,1.56066017 20.6819805,3.31801948 Z M17.5,5 C18.3284271,5 19,5.67157288 19,6.5 C19,7.32842712 18.3284271,8 17.5,8 C16.6715729,8 16,7.32842712 16,6.5 C16,5.67157288 16.6715729,5 17.5,5 Z M17.5,6 C17.2238576,6 17,6.22385763 17,6.5 C17,6.77614237 17.2238576,7 17.5,7 C17.7761424,7 18,6.77614237 18,6.5 C18,6.22385763 17.7761424,6 17.5,6 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 16`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  vbHeight={24}
  vbWidth={24}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -16,
            -506,
          ]
        }
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              0,
              477,
            ]
          }
        >
          <RNSVGGroup
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            matrix={
              [
                1,
                0,
                0,
                1,
                16,
                29,
              ]
            }
          >
            <RNSVGPath
              d="M20,6 C21.1045695,6 22,6.8954305 22,8 L22,16 C22,17.1045695 21.1045695,18 20,18 L4,18 C2.8954305,18 2,17.1045695 2,16 L2,8 C2,6.8954305 2.8954305,6 4,6 L20,6 Z M20,7 L4,7 C3.48716416,7 3.06449284,7.38604019 3.00672773,7.88337887 L3,8 L3,16 C3,16.5128358 3.38604019,16.9355072 3.88337887,16.9932723 L4,17 L20,17 C20.5128358,17 20.9355072,16.6139598 20.9932723,16.1166211 L21,16 L21,8 C21,7.48716416 20.6139598,7.06449284 20.1166211,7.00672773 L20,7 Z M7.05163196,8.77871714 C7.23503881,8.40709429 7.76496119,8.40709429 7.94836804,8.77871714 L7.94836804,8.77871714 L8.566,10.032 L9.94954045,10.232654 C10.330357,10.2879899 10.4987552,10.7265019 10.2832676,11.0204684 L10.2832676,11.0204684 L10.2266471,11.0855007 L9.226,12.061 L9.46226729,13.43802 C9.52765213,13.8192428 9.15855166,14.1159918 8.81081455,13.9970736 L8.81081455,13.9970736 L8.73679257,13.9651082 L7.5,13.315 L6.26320743,13.9651082 C5.92084799,14.1450972 5.52456457,13.8857623 5.53020583,13.5182969 L5.53020583,13.5182969 L5.53773271,13.43802 L5.773,12.061 L4.77335286,11.0855007 C4.49779139,10.8168946 4.61930536,10.363149 4.96642759,10.2519856 L4.96642759,10.2519856 L5.05045955,10.232654 L6.433,10.032 Z M18.5,12 C18.7761424,12 19,12.2238576 19,12.5 C19,12.7761424 18.7761424,13 18.5,13 L17.5,13 C17.2238576,13 17,12.7761424 17,12.5 C17,12.2238576 17.2238576,12 17.5,12 L18.5,12 Z M18.5,9 C18.7761424,9 19,9.22385763 19,9.5 C19,9.77614237 18.7761424,10 18.5,10 L13.5,10 C13.2238576,10 13,9.77614237 13,9.5 C13,9.22385763 13.2238576,9 13.5,9 L18.5,9 Z"
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
              propList={
                [
                  "fill",
                ]
              }
            />
          </RNSVGGroup>
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 17`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="16"
  bbWidth="24px"
  focusable={false}
  height="16"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 16,
        "width": 24,
      },
    ]
  }
  vbHeight={16}
  vbWidth={20}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      name="illustarations-and-Icons"
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -590,
            -2614,
          ]
        }
        name="Icons"
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              2610,
            ]
          }
          name="atom-/-icon-/-line-/-custom-/-pulse-credit"
        >
          <RNSVGRect
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            height="24"
            name="Rectangle"
            width="24"
            x="0"
            y="0"
          />
          <RNSVGPath
            d="M20,4 C21.1045695,4 22,4.8954305 22,6 L22,14 C22,15.1045695 21.1045695,16 20,16 L18.9724574,16.0007074 C18.7234136,18.2503421 16.8160341,20 14.5,20 C12.1839659,20 10.2765864,18.2503421 10.0275426,16.0007074 L4,16 C2.8954305,16 2,15.1045695 2,14 L2,6 C2,4.8954305 2.8954305,4 4,4 L20,4 Z M14.5,13.5 C13.3954305,13.5 12.5,14.3954305 12.5,15.5 C12.5,16.6045695 13.3954305,17.5 14.5,17.5 C15.6045695,17.5 16.5,16.6045695 16.5,15.5 C16.5,14.3954305 15.6045695,13.5 14.5,13.5 Z M11.25,14.25 C10.5596441,14.25 10,14.8096441 10,15.5 C10,16.1903559 10.5596441,16.75 11.25,16.75 C11.9403559,16.75 12.5,16.1903559 12.5,15.5 C12.5,14.8096441 11.9403559,14.25 11.25,14.25 Z M20,5 L4,5 C3.48716416,5 3.06449284,5.38604019 3.00672773,5.88337887 L3,6 L3,14 C3,14.5128358 3.38604019,14.9355072 3.88337887,14.9932723 L4,15 L10.0274316,15.000297 C10.2760186,12.7501788 12.1836212,11 14.5,11 C16.8163788,11 18.7239814,12.7501788 18.9725684,15.000297 L20,15 C20.5128358,15 20.9355072,14.6139598 20.9932723,14.1166211 L21,14 L21,6 C21,5.48716416 20.6139598,5.06449284 20.1166211,5.00672773 L20,5 Z M6.5,10 C6.77614237,10 7,10.2238576 7,10.5 C7,10.7761424 6.77614237,11 6.5,11 L5.5,11 C5.22385763,11 5,10.7761424 5,10.5 C5,10.2238576 5.22385763,10 5.5,10 L6.5,10 Z M10.5,7 C10.7761424,7 11,7.22385763 11,7.5 C11,7.77614237 10.7761424,8 10.5,8 L5.5,8 C5.22385763,8 5,7.77614237 5,7.5 C5,7.22385763 5.22385763,7 5.5,7 L10.5,7 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            name="Combined-Shape"
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 18`] = `
<RNSVGSvgView
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
        ]
      }
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -592,
            -1079,
          ]
        }
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              1074,
            ]
          }
        >
          <RNSVGPath
            d="M0 0h24v24H0z"
            fill={null}
            propList={
              [
                "fill",
              ]
            }
          />
          <RNSVGPath
            d="M12 19.15a7.15 7.15 0 1 0 0-14.3 7.15 7.15 0 0 0 0 14.3zm0-10.7v4h4"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            propList={
              [
                "stroke",
              ]
            }
            stroke={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 19`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  testID="rightArrow"
  vbHeight={24}
  vbWidth={24}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
        ]
      }
    >
      <RNSVGPath
        d="M0 0h24v24H0z"
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
      />
      <RNSVGPath
        d="M8.646 18.354a.5.5 0 0 1-.057-.638l.057-.07L14.293 12 8.646 6.354a.5.5 0 0 1-.057-.638l.057-.07a.5.5 0 0 1 .638-.057l.07.057 6 6a.5.5 0 0 1 .057.638l-.057.07-6 6a.5.5 0 0 1-.708 0z"
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        propList={
          [
            "fill",
          ]
        }
      />
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 20`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="24"
  bbWidth="24px"
  focusable={false}
  height="24"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 24,
        "width": 24,
      },
    ]
  }
  testID="rightArrow"
  vbHeight={24}
  vbWidth={24}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      propList={
        [
          "fill",
        ]
      }
    >
      <RNSVGPath
        d="M0 0h24v24H0z"
        fill={null}
        propList={
          [
            "fill",
          ]
        }
      />
      <RNSVGPath
        d="M15 7.38c1.8 1.02 3 3 3 5.22 0 3.3-2.7 6-6 6s-6-2.7-6-6c0-2.22 1.2-4.14 3-5.22m3-1.98v4.2"
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        propList={
          [
            "stroke",
          ]
        }
        stroke={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
      />
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;

exports[`<Icon /> component its snapshot matches 21`] = `
<RNSVGSvgView
  align="xMidYMid"
  bbHeight="15"
  bbWidth="24px"
  focusable={false}
  height="15"
  meetOrSlice={0}
  minX={0}
  minY={0}
  style={
    [
      {
        "backgroundColor": "transparent",
        "borderWidth": 0,
      },
      {
        "flex": 0,
        "height": 15,
        "width": 24,
      },
    ]
  }
  vbHeight={15}
  vbWidth={18}
  width="24px"
>
  <RNSVGGroup
    fill={
      {
        "payload": 4278190080,
        "type": 0,
      }
    }
  >
    <RNSVGGroup
      fill={null}
      name="illustarations-and-Icons"
      propList={
        [
          "fill",
          "stroke",
        ]
      }
      stroke={null}
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
        matrix={
          [
            1,
            0,
            0,
            1,
            -591,
            -2951,
          ]
        }
        name="Icons"
      >
        <RNSVGGroup
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          matrix={
            [
              1,
              0,
              0,
              1,
              588,
              2946,
            ]
          }
          name="atom-/-icon-/-line-/-custom-/-tick"
        >
          <RNSVGPath
            d="M19.603688,5.69514462 C19.772055,5.47626755 20.0859783,5.43532103 20.3048554,5.60368801 C20.4994128,5.75334754 20.5533831,6.01801456 20.444725,6.22870617 L20.396312,6.30485538 L10.396312,19.3048554 C10.2327348,19.5175057 9.93610389,19.559217 9.72231328,19.4159688 L9.64644661,19.3535534 L3.64644661,13.3535534 C3.45118446,13.1582912 3.45118446,12.8417088 3.64644661,12.6464466 C3.82001296,12.4728803 4.08943736,12.4535951 4.2843055,12.5885912 L4.35355339,12.6464466 L9.95,18.243 L19.603688,5.69514462 Z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
            name="Path-21"
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGGroup>
</RNSVGSvgView>
`;
