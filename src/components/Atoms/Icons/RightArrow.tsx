import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
  testID?: string;
}

const RightArrow = (props: IProps) => {
  const {
    width = '24',
    height = '24',
    color = 'black',
    testID = 'rightArrow',
  } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" testID={testID}>
      <G fill="none" fill-rule="evenodd">
        <Path d="M0 0h24v24H0z" />
        <Path
          d="M8.646 18.354a.5.5 0 0 1-.057-.638l.057-.07L14.293 12 8.646 6.354a.5.5 0 0 1-.057-.638l.057-.07a.5.5 0 0 1 .638-.057l.07.057 6 6a.5.5 0 0 1 .057.638l-.057.07-6 6a.5.5 0 0 1-.708 0z"
          fill={color}
        />
      </G>
    </Svg>
  );
};

export default React.memo(RightArrow);
