import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const DownArrow = (props: IProps) => {
  const { width = '14', height = '8', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 14 8">
      <G
        id="illustarations-and-Icons"
        stroke="none"
        stroke-width="1"
        fill="none"
        fill-rule="evenodd"
      >
        <G id="Icons" transform="translate(-593.000000, -1706.000000)">
          <G
            id="atom-/-icon-/-line-/-custom-/-dropdown"
            transform="translate(588.000000, 1698.000000)"
          >
            <Path
              d="M5.64644661,8.64644661 C5.82001296,8.47288026 6.08943736,8.45359511 6.2843055,8.58859116 L6.35355339,8.64644661 L12,14.293 L17.6464466,8.64644661 C17.820013,8.47288026 18.0894374,8.45359511 18.2843055,8.58859116 L18.3535534,8.64644661 C18.5271197,8.82001296 18.5464049,9.08943736 18.4114088,9.2843055 L18.3535534,9.35355339 L12.3535534,15.3535534 C12.179987,15.5271197 11.9105626,15.5464049 11.7156945,15.4114088 L11.6464466,15.3535534 L5.64644661,9.35355339 C5.45118446,9.15829124 5.45118446,8.84170876 5.64644661,8.64644661 Z"
              id="Path-8"
              fill={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(DownArrow);
