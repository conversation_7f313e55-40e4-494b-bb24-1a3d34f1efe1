import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const PolarPayAsUGoIcon = (props: IProps) => {
  const { width = '24', height = '24', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height}>
      <G stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <G transform="translate(-592.000000, -1079.000000)">
          <G transform="translate(588.000000, 1074.000000)">
            <Path
              d="M16,5 C17.1045695,5 18,5.8954305 18,7 L18,8 C19.1045695,8 20,8.8954305 20,10 L20,14 C20,15.1045695 19.1045695,16 18,16 L18,17 C18,18.1045695 17.1045695,19 16,19 L6,19 C4.8954305,19 4,18.1045695 4,17 L4,7 C4,5.8954305 4.8954305,5 6,5 L16,5 Z M16,6 L6,6 C5.48716416,6 5.06449284,6.38604019 5.00672773,6.88337887 L5,7 L5,17 C5,17.5128358 5.38604019,17.9355072 5.88337887,17.9932723 L6,18 L16,18 C16.5128358,18 16.9355072,17.6139598 16.9932723,17.1166211 L17,17 L17,16 L14,16 C12.8954305,16 12,15.1045695 12,14 L12,10 C12,8.8954305 12.8954305,8 14,8 L17,8 L17,7 C17,6.48716416 16.6139598,6.06449284 16.1166211,6.00672773 L16,6 Z M18,9 L14,9 C13.4871642,9 13.0644928,9.38604019 13.0067277,9.88337887 L13,10 L13,14 C13,14.5128358 13.3860402,14.9355072 13.8833789,14.9932723 L14,15 L18,15 C18.5128358,15 18.9355072,14.6139598 18.9932723,14.1166211 L19,14 L19,10 C19,9.48716416 18.6139598,9.06449284 18.1166211,9.00672773 L18,9 Z M15,11 C15.5522847,11 16,11.4477153 16,12 C16,12.5522847 15.5522847,13 15,13 C14.4477153,13 14,12.5522847 14,12 C14,11.4477153 14.4477153,11 15,11 Z"
              fill={color}
            />
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(PolarPayAsUGoIcon);
