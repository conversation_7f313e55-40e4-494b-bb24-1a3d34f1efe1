import React from 'react';
import { G, Path, Svg } from 'react-native-svg';

interface IProps {
  width?: string;
  height?: string;
  color?: string;
}

const OfferIcon = (props: IProps) => {
  const { width = '24', height = '24', color = 'currentColor' } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24">
      <G fill="none" fill-rule="evenodd">
        <G transform="translate(-592.000000, -1079.000000)">
          <G transform="translate(588.000000, 1074.000000)">
            <G transform="translate(2.000000, 2.000000)">
              <Path fill="none" d="M0 0h24v24H0z" />
              <Path
                d="M18.587 11.706 12.291 5.41A1.409 1.409 0 0 0 11.3 5H6.4A1.4 1.4 0 0 0 5 6.4v4.9c0 .389.157.738.413.99l6.3 6.3a1.394 1.394 0 0 0 1.978 0l4.9-4.9a1.4 1.4 0 0 0-.003-1.985zM8.5 9.5c-.553 0-1-.447-1-1 0-.553.447-1 1-1 .553 0 1 .447 1 1 0 .553-.447 1-1 1z"
                stroke={color}
              />
            </G>
          </G>
        </G>
      </G>
    </Svg>
  );
};

export default React.memo(OfferIcon);
