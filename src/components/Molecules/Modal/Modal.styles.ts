import { styled } from 'styled-components/native';

interface IBackgroundColorProp {
  backgroundColor?: string;
}

export const Modal = styled.Modal`
  flex: 1 0 0;
  width: 100%;
`;

export const ModalContainer = styled.View<IBackgroundColorProp>`
  flex: 1 0 0;
  width: 100%;
  background-color: ${(p) =>
    p.backgroundColor ? p.backgroundColor : 'rgba(0, 0, 0, 0.41)'};
  justify-content: center;
  padding: 0 20px;
`;

export const ModalContent = styled.View<any>`
  width: 100%;
  padding: ${(p) => (p.paddingEnabled ? '0 15px' : '0')};
  border-radius: 9px;
  overflow: hidden;
  background-color: ${(p) => p.theme.subscriptionMfe.color.white};
  shadow-color: ${(p) => p.theme.subscriptionMfe.color.black};
  shadow-offset: 0 0;
  shadow-opacity: 0.11;
  shadow-radius: 6px;
  elevation: 10;
`;
