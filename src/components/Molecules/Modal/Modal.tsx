import React from 'react';
import { TouchableWithoutFeedback } from 'react-native';

import * as S from './Modal.styles';

const Modal = ({
  visible = false,
  setVisibleState,
  children,
  backgroundColor,
  paddingEnabled = true,
  // Make sure to add the alpha (transparency value) at the end of the color string
  ...props
}: any) => {
  const closeModal = () => setVisibleState && setVisibleState(false);

  return (
    <S.Modal
      animationType="fade"
      transparent
      visible={visible}
      onRequestClose={closeModal}
      {...props}
    >
      <TouchableWithoutFeedback
        accessible={false}
        testID="TouchableWithoutFeedback"
        onPress={closeModal}
      >
        <S.ModalContainer backgroundColor={backgroundColor}>
          <TouchableWithoutFeedback accessible={false}>
            <S.ModalContent paddingEnabled={paddingEnabled}>
              {children}
            </S.ModalContent>
          </TouchableWithoutFeedback>
        </S.ModalContainer>
      </TouchableWithoutFeedback>
    </S.Modal>
  );
};

export default Modal;
