import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import Modal from './Modal';

const mockTranslations = TRANSLATIONS.en_GB;

describe('Modal component', () => {
  it('renders default', () => {
    const setVisibleState = jest.fn();
    const ModalTest = render(
      <Theme>
        <Modal visible={true} setVisibleState={setVisibleState}>
          {mockTranslations.testing.CHILDREN}
        </Modal>
        ,
      </Theme>,
    );
    expect(ModalTest.toJSON()).toMatchSnapshot();

    const TouchableOuterArea = ModalTest.getAllByTestId(
      'TouchableWithoutFeedback',
    )[0];
    fireEvent.press(TouchableOuterArea);
    expect(setVisibleState).toHaveBeenCalled();
  });

  it('renders visisble', () => {
    const ModalTest = render(
      <Theme>
        <Modal visible={true}>{mockTranslations.testing.CHILDREN}</Modal>,
      </Theme>,
    );
    expect(ModalTest.toJSON()).toMatchSnapshot();
  });

  it('renders invisisble', () => {
    const ModalTest = render(
      <Theme>
        <Modal>{mockTranslations.testing.CHILDREN}</Modal>,
      </Theme>,
    );
    expect(ModalTest.toJSON()).toMatchSnapshot();
  });

  it('renders with padding disabled', () => {
    const ModalTest = render(
      <Theme>
        <Modal visible={true} paddingEnabled={false}>
          {mockTranslations.testing.CHILDREN}
        </Modal>
        ,
      </Theme>,
    );
    expect(ModalTest.toJSON()).toMatchSnapshot();
  });
});
