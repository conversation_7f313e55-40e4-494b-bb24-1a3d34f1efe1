// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Modal component renders default 1`] = `
[
  <Modal
    animationType="fade"
    hardwareAccelerated={false}
    onRequestClose={[Function]}
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 0,
        "width": "100%",
      }
    }
    transparent={true}
    visible={true}
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessible={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "backgroundColor": "rgba(0, 0, 0, 0.41)",
          "flexBasis": 0,
          "flexGrow": 1,
          "flexShrink": 0,
          "justifyContent": "center",
          "paddingBottom": 0,
          "paddingLeft": 20,
          "paddingRight": 20,
          "paddingTop": 0,
          "width": "100%",
        }
      }
      testID="TouchableWithoutFeedback"
    >
      <View
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessible={false}
        focusable={false}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        paddingEnabled={true}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 9,
            "borderBottomRightRadius": 9,
            "borderTopLeftRadius": 9,
            "borderTopRightRadius": 9,
            "elevation": 10,
            "overflow": "hidden",
            "paddingBottom": 0,
            "paddingLeft": 15,
            "paddingRight": 15,
            "paddingTop": 0,
            "shadowColor": "#000000",
            "shadowOffset": {
              "height": 0,
              "width": 0,
            },
            "shadowOpacity": 0.11,
            "shadowRadius": 6,
            "width": "100%",
          }
        }
      >
        Children
      </View>
    </View>
  </Modal>,
  ",",
]
`;

exports[`Modal component renders invisisble 1`] = `
[
  <Modal
    animationType="fade"
    hardwareAccelerated={false}
    onRequestClose={[Function]}
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 0,
        "width": "100%",
      }
    }
    transparent={true}
    visible={false}
  />,
  ",",
]
`;

exports[`Modal component renders visisble 1`] = `
[
  <Modal
    animationType="fade"
    hardwareAccelerated={false}
    onRequestClose={[Function]}
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 0,
        "width": "100%",
      }
    }
    transparent={true}
    visible={true}
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessible={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "backgroundColor": "rgba(0, 0, 0, 0.41)",
          "flexBasis": 0,
          "flexGrow": 1,
          "flexShrink": 0,
          "justifyContent": "center",
          "paddingBottom": 0,
          "paddingLeft": 20,
          "paddingRight": 20,
          "paddingTop": 0,
          "width": "100%",
        }
      }
      testID="TouchableWithoutFeedback"
    >
      <View
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessible={false}
        focusable={false}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        paddingEnabled={true}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 9,
            "borderBottomRightRadius": 9,
            "borderTopLeftRadius": 9,
            "borderTopRightRadius": 9,
            "elevation": 10,
            "overflow": "hidden",
            "paddingBottom": 0,
            "paddingLeft": 15,
            "paddingRight": 15,
            "paddingTop": 0,
            "shadowColor": "#000000",
            "shadowOffset": {
              "height": 0,
              "width": 0,
            },
            "shadowOpacity": 0.11,
            "shadowRadius": 6,
            "width": "100%",
          }
        }
      >
        Children
      </View>
    </View>
  </Modal>,
  ",",
]
`;

exports[`Modal component renders with padding disabled 1`] = `
[
  <Modal
    animationType="fade"
    hardwareAccelerated={false}
    onRequestClose={[Function]}
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 0,
        "width": "100%",
      }
    }
    transparent={true}
    visible={true}
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessible={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "backgroundColor": "rgba(0, 0, 0, 0.41)",
          "flexBasis": 0,
          "flexGrow": 1,
          "flexShrink": 0,
          "justifyContent": "center",
          "paddingBottom": 0,
          "paddingLeft": 20,
          "paddingRight": 20,
          "paddingTop": 0,
          "width": "100%",
        }
      }
      testID="TouchableWithoutFeedback"
    >
      <View
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessible={false}
        focusable={false}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        paddingEnabled={false}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 9,
            "borderBottomRightRadius": 9,
            "borderTopLeftRadius": 9,
            "borderTopRightRadius": 9,
            "elevation": 10,
            "overflow": "hidden",
            "paddingBottom": 0,
            "paddingLeft": 0,
            "paddingRight": 0,
            "paddingTop": 0,
            "shadowColor": "#000000",
            "shadowOffset": {
              "height": 0,
              "width": 0,
            },
            "shadowOpacity": 0.11,
            "shadowRadius": 6,
            "width": "100%",
          }
        }
      >
        Children
      </View>
    </View>
  </Modal>,
  ",",
]
`;
