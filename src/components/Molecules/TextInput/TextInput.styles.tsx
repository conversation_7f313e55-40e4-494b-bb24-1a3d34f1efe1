import { styled } from 'styled-components/native';

type StyledTextInputProps = {
  shadow: boolean;
  clearButtonEnabled: boolean;
};

export const StyledTextInput: any = styled.TextInput<StyledTextInputProps>`
  flex: 1;
  min-height: 48px;
  max-height: 90px;
  padding: 5px 10px;
  font-size: 16px;
  color: ${(p) => p.theme.subscriptionMfe.textInput.color};
  background-color: ${(p) => p.theme.subscriptionMfe.textInput.backgroundColor};
  border: 1px solid ${(p) => p.theme.subscriptionMfe.textInput.borderColor};
  border-radius: 0px;
  ${({ shadow, theme }) =>
    shadow &&
    `
      shadow-color: ${theme.subscriptionMfe.color.black};
      shadow-offset: 0 0;
      shadow-opacity: 0.11;
      shadow-radius: 7px;
      elevation: 11;
    `}
  ${({ active, theme }: any) =>
    active &&
    `
      border-color: ${theme.subscriptionMfe.color.primary};
    `}
  ${({ success, theme }: any) =>
    success &&
    `
      color: ${theme.subscriptionMfe.textInput.success.color};
      background-color: ${theme.subscriptionMfe.textInput.success.backgroundColor};
      border-color: ${theme.subscriptionMfe.color.state.success};
    `}
  ${({ error, theme }: any) =>
    error &&
    `
      color: ${theme.subscriptionMfe.cancelSubscription.color.primary};
      border-color: ${theme.subscriptionMfe.cancelSubscription.color.primary};
    `}
  ${({ clearButtonEnabled }) =>
    clearButtonEnabled &&
    `
      padding-right: 48px;
      padding-left: 48px;
    `}
  ${({ disabled, theme }: any) =>
    disabled &&
    `
      color: ${theme.subscriptionMfe.textInput.disabled.color};
      background-color: ${theme.subscriptionMfe.textInput.disabled.backgroundColor};
      border-color: ${theme.subscriptionMfe.textInput.borderColor};
    `}
`;

export const StyledTextWrapper = styled.View`
  min-height: 48px;
  justify-content: center;
`;

export const TextInputContainer = styled.View`
  flex: 1;
  min-height: 57px;
`;

export const LabelContainer = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
`;

export const Label = styled.Text<{ bold?: boolean }>`
  flex-shrink: 1;
  margin-bottom: 7px;
  font-size: 14px;
  line-height: 24px;
  ${({ bold, theme }: any) =>
    bold &&
    `
      font-weight: bold;
      color: ${theme.subscriptionMfe.textInput.labelTextColor};
    `}
`;

export const AssistiveText = styled.Text`
  padding-left: 20px;
  padding-top: 5px;
  font-size: 11px;
  line-height: 16px;
  letter-spacing: 0.7px;
  color: rgba(17, 17, 17, 0.8);
`;

export const LabelFiller = styled.View`
  flex: 0;
`;

export const ClearInputButton = styled.TouchableOpacity`
  position: absolute;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  margin-right: 24px;
  right: 0;
  width: 24px;
  height: 24px;
`;

export const ErrorImgWrapper = styled.View`
  position: absolute;
  right: 10px;
`;
