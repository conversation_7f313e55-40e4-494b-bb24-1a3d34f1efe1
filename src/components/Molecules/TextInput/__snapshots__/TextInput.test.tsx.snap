// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TextInput component renders default 1`] = `
[
  <View
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "minHeight": 57,
      }
    }
  >
    <View
      style={
        {
          "flexDirection": "row",
          "flexWrap": "wrap",
        }
      }
    />
    <View
      style={
        {
          "justifyContent": "center",
          "minHeight": 48,
        }
      }
    >
      <TextInput
        accessibilityLabel="Enter undefined"
        accessible={true}
        active={false}
        clearButtonEnabled={false}
        error={false}
        invalid={false}
        onBlur={[Function]}
        onFocus={[Function]}
        placeholderTextColor="rgba(17, 17, 17, 0.65)"
        shadow={false}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 0,
            "borderBottomRightRadius": 0,
            "borderColor": "#DEDEDE",
            "borderStyle": "solid",
            "borderTopLeftRadius": 0,
            "borderTopRightRadius": 0,
            "borderWidth": 1,
            "color": "#000000",
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "fontSize": 16,
            "maxHeight": 90,
            "minHeight": 48,
            "paddingBottom": 5,
            "paddingLeft": 10,
            "paddingRight": 10,
            "paddingTop": 5,
          }
        }
        success={false}
        testID="Enter undefined"
      />
    </View>
  </View>,
  ",",
]
`;

exports[`TextInput component renders with assistive text 1`] = `
[
  <View
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "minHeight": 57,
      }
    }
  >
    <View
      style={
        {
          "flexDirection": "row",
          "flexWrap": "wrap",
        }
      }
    >
      <View
        style={
          {
            "flexBasis": 0,
            "flexGrow": 0,
            "flexShrink": 1,
          }
        }
      />
      <Text
        accessibilityRole="text"
        style={
          {
            "color": "rgba(17, 17, 17, 0.8)",
            "fontSize": 11,
            "letterSpacing": 0.7,
            "lineHeight": 16,
            "paddingLeft": 20,
            "paddingTop": 5,
          }
        }
      >
        Assistive text
      </Text>
    </View>
    <View
      style={
        {
          "justifyContent": "center",
          "minHeight": 48,
        }
      }
    >
      <TextInput
        accessibilityLabel="Enter undefined"
        accessible={true}
        active={false}
        clearButtonEnabled={false}
        error={false}
        invalid={false}
        onBlur={[Function]}
        onFocus={[Function]}
        placeholderTextColor="rgba(17, 17, 17, 0.65)"
        shadow={false}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 0,
            "borderBottomRightRadius": 0,
            "borderColor": "#DEDEDE",
            "borderStyle": "solid",
            "borderTopLeftRadius": 0,
            "borderTopRightRadius": 0,
            "borderWidth": 1,
            "color": "#000000",
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "fontSize": 16,
            "maxHeight": 90,
            "minHeight": 48,
            "paddingBottom": 5,
            "paddingLeft": 10,
            "paddingRight": 10,
            "paddingTop": 5,
          }
        }
        success={false}
        testID="Enter undefined"
      />
    </View>
  </View>,
  ",",
]
`;

exports[`TextInput component renders with error 1`] = `
[
  <View
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "minHeight": 57,
      }
    }
  >
    <View
      style={
        {
          "flexDirection": "row",
          "flexWrap": "wrap",
        }
      }
    />
    <View
      style={
        {
          "justifyContent": "center",
          "minHeight": 48,
        }
      }
    >
      <TextInput
        accessibilityLabel="Enter undefined"
        accessible={true}
        active={false}
        clearButtonEnabled={false}
        error={true}
        invalid={false}
        onBlur={[Function]}
        onFocus={[Function]}
        placeholderTextColor="rgba(17, 17, 17, 0.65)"
        shadow={false}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 0,
            "borderBottomRightRadius": 0,
            "borderColor": "#e64949",
            "borderStyle": "solid",
            "borderTopLeftRadius": 0,
            "borderTopRightRadius": 0,
            "borderWidth": 1,
            "color": "#e64949",
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "fontSize": 16,
            "maxHeight": 90,
            "minHeight": 48,
            "paddingBottom": 5,
            "paddingLeft": 10,
            "paddingRight": 10,
            "paddingTop": 5,
          }
        }
        success={false}
        testID="Enter undefined"
      />
      <View
        style={
          {
            "position": "absolute",
            "right": 10,
          }
        }
      >
        <Image
          source={
            {
              "testUri": "../../../assets/images/alert.png",
            }
          }
        />
      </View>
    </View>
    <View
      justify="flex-start"
      style={
        {
          "flexDirection": "row",
          "justifyContent": "flex-start",
          "paddingTop": 7,
        }
      }
    >
      <Text
        status="error"
        style={
          {
            "color": "#CF0001",
            "fontSize": 11,
            "letterSpacing": 0.7,
            "lineHeight": 16,
          }
        }
      >
        Error Message
      </Text>
    </View>
  </View>,
  ",",
]
`;

exports[`TextInput component renders with label 1`] = `
[
  <View
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "minHeight": 57,
      }
    }
  >
    <View
      style={
        {
          "flexDirection": "row",
          "flexWrap": "wrap",
        }
      }
    >
      <Text
        accessibilityRole="text"
        bold={true}
        style={
          {
            "color": "#353535",
            "flexShrink": 1,
            "fontSize": 14,
            "fontWeight": "bold",
            "lineHeight": 24,
            "marginBottom": 7,
          }
        }
      >
        Test label
      </Text>
      <View
        style={
          {
            "flexBasis": 0,
            "flexGrow": 0,
            "flexShrink": 1,
          }
        }
      />
    </View>
    <View
      style={
        {
          "justifyContent": "center",
          "minHeight": 48,
        }
      }
    >
      <TextInput
        accessibilityLabel="Enter undefined"
        accessible={true}
        active={false}
        clearButtonEnabled={false}
        disabled={true}
        error={false}
        invalid={false}
        onBlur={[Function]}
        onFocus={[Function]}
        placeholderTextColor="rgba(17, 17, 17, 0.65)"
        shadow={true}
        style={
          {
            "backgroundColor": "#F5F5F5",
            "borderBottomLeftRadius": 0,
            "borderBottomRightRadius": 0,
            "borderColor": "#DEDEDE",
            "borderStyle": "solid",
            "borderTopLeftRadius": 0,
            "borderTopRightRadius": 0,
            "borderWidth": 1,
            "color": "#353535",
            "elevation": 11,
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "fontSize": 16,
            "maxHeight": 90,
            "minHeight": 48,
            "paddingBottom": 5,
            "paddingLeft": 10,
            "paddingRight": 10,
            "paddingTop": 5,
            "shadowColor": "#000000",
            "shadowOffset": {
              "height": 0,
              "width": 0,
            },
            "shadowOpacity": 0.11,
            "shadowRadius": 7,
          }
        }
        success={false}
        testID="Enter undefined"
      />
    </View>
  </View>,
  ",",
]
`;

exports[`TextInput component renders with maxLength 1`] = `
[
  <View
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "minHeight": 57,
      }
    }
  >
    <View
      style={
        {
          "flexDirection": "row",
          "flexWrap": "wrap",
        }
      }
    />
    <View
      style={
        {
          "justifyContent": "center",
          "minHeight": 48,
        }
      }
    >
      <TextInput
        accessibilityLabel="Enter undefined"
        accessible={true}
        active={false}
        clearButtonEnabled={false}
        error={false}
        invalid={false}
        maxLength={50}
        onBlur={[Function]}
        onFocus={[Function]}
        placeholderTextColor="rgba(17, 17, 17, 0.65)"
        shadow={false}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 0,
            "borderBottomRightRadius": 0,
            "borderColor": "#DEDEDE",
            "borderStyle": "solid",
            "borderTopLeftRadius": 0,
            "borderTopRightRadius": 0,
            "borderWidth": 1,
            "color": "#000000",
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "fontSize": 16,
            "maxHeight": 90,
            "minHeight": 48,
            "paddingBottom": 5,
            "paddingLeft": 10,
            "paddingRight": 10,
            "paddingTop": 5,
          }
        }
        success={false}
        testID="Enter undefined"
      />
    </View>
  </View>,
  ",",
]
`;

exports[`TextInput component renders with placeholder 1`] = `
[
  <View
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "minHeight": 57,
      }
    }
  >
    <View
      style={
        {
          "flexDirection": "row",
          "flexWrap": "wrap",
        }
      }
    />
    <View
      style={
        {
          "justifyContent": "center",
          "minHeight": 48,
        }
      }
    >
      <TextInput
        accessibilityLabel="Enter Test"
        accessible={true}
        active={false}
        clearButtonEnabled={true}
        error={false}
        invalid={false}
        onBlur={[Function]}
        onFocus={[Function]}
        placeholder="Test placeholder"
        placeholderTextColor="rgba(17, 17, 17, 0.65)"
        shadow={false}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 0,
            "borderBottomRightRadius": 0,
            "borderColor": "#DEDEDE",
            "borderStyle": "solid",
            "borderTopLeftRadius": 0,
            "borderTopRightRadius": 0,
            "borderWidth": 1,
            "color": "#000000",
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "fontSize": 16,
            "maxHeight": 90,
            "minHeight": 48,
            "paddingBottom": 5,
            "paddingLeft": 48,
            "paddingRight": 48,
            "paddingTop": 5,
          }
        }
        success={false}
        testID="Enter Test"
      />
      <View
        accessibilityLabel="Clear"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={false}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "alignItems": "center",
            "borderBottomLeftRadius": 12,
            "borderBottomRightRadius": 12,
            "borderTopLeftRadius": 12,
            "borderTopRightRadius": 12,
            "height": 24,
            "justifyContent": "center",
            "marginRight": 24,
            "opacity": 1,
            "position": "absolute",
            "right": 0,
            "width": 24,
          }
        }
        testID="Clear"
      >
        <RNSVGSvgView
          align="xMidYMid"
          bbHeight="16px"
          bbWidth="16px"
          focusable={false}
          height="16px"
          meetOrSlice={0}
          minX={0}
          minY={0}
          style={
            [
              {
                "backgroundColor": "transparent",
                "borderWidth": 0,
              },
              {
                "flex": 0,
                "height": 16,
                "width": 16,
              },
            ]
          }
          vbHeight={16}
          vbWidth={16}
          width="16px"
        >
          <RNSVGGroup
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
          >
            <RNSVGGroup
              fill={null}
              name="illustarations-and-Icons"
              propList={
                [
                  "fill",
                  "stroke",
                ]
              }
              stroke={null}
            >
              <RNSVGGroup
                fill={
                  {
                    "payload": 4278190080,
                    "type": 0,
                  }
                }
                matrix={
                  [
                    1,
                    0,
                    0,
                    1,
                    -592,
                    -2998,
                  ]
                }
                name="Icons"
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      588,
                      2994,
                    ]
                  }
                  name="atom-/-icon-/-line-/-custom-/-close"
                >
                  <RNSVGPath
                    d="M5.2843055,4.58859116 L5.35355339,4.64644661 L12,11.293 L18.6464466,4.64644661 L18.7156945,4.58859116 C18.9105626,4.45359511 19.179987,4.47288026 19.3535534,4.64644661 C19.5488155,4.84170876 19.5488155,5.15829124 19.3535534,5.35355339 L19.3535534,5.35355339 L12.707,12 L19.3535534,18.6464466 C19.5488155,18.8417088 19.5488155,19.1582912 19.3535534,19.3535534 C19.179987,19.5271197 18.9105626,19.5464049 18.7156945,19.4114088 L18.6464466,19.3535534 L12,12.707 L5.35355339,19.3535534 L5.2843055,19.4114088 C5.08943736,19.5464049 4.82001296,19.5271197 4.64644661,19.3535534 C4.45118446,19.1582912 4.45118446,18.8417088 4.64644661,18.6464466 L4.64644661,18.6464466 L11.293,12 L4.64644661,5.35355339 C4.45118446,5.15829124 4.45118446,4.84170876 4.64644661,4.64644661 C4.82001296,4.47288026 5.08943736,4.45359511 5.2843055,4.58859116 Z"
                    fill={
                      {
                        "payload": 4280361249,
                        "type": 0,
                      }
                    }
                    name="Combined-Shape"
                    propList={
                      [
                        "fill",
                      ]
                    }
                  />
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGGroup>
        </RNSVGSvgView>
      </View>
    </View>
  </View>,
  ",",
]
`;

exports[`TextInput component renders without shadow 1`] = `
[
  <View
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "minHeight": 57,
      }
    }
  >
    <View
      style={
        {
          "flexDirection": "row",
          "flexWrap": "wrap",
        }
      }
    />
    <View
      style={
        {
          "justifyContent": "center",
          "minHeight": 48,
        }
      }
    >
      <TextInput
        accessibilityLabel="Enter undefined"
        accessible={true}
        active={false}
        clearButtonEnabled={false}
        error={false}
        invalid={false}
        onBlur={[Function]}
        onFocus={[Function]}
        placeholderTextColor="rgba(17, 17, 17, 0.65)"
        shadow={false}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 0,
            "borderBottomRightRadius": 0,
            "borderColor": "#DEDEDE",
            "borderStyle": "solid",
            "borderTopLeftRadius": 0,
            "borderTopRightRadius": 0,
            "borderWidth": 1,
            "color": "#000000",
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "fontSize": 16,
            "maxHeight": 90,
            "minHeight": 48,
            "paddingBottom": 5,
            "paddingLeft": 10,
            "paddingRight": 10,
            "paddingTop": 5,
          }
        }
        success={false}
        testID="Enter undefined"
      />
    </View>
  </View>,
  ",",
]
`;
