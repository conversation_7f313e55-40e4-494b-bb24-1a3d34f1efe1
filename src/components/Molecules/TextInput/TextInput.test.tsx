import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import TextInput from './TextInput';

describe('TextInput component', () => {
  it('renders default', () => {
    const TextInputTest = render(
      <Theme>
        <TextInput />,
      </Theme>,
    );
    expect(TextInputTest.toJSON()).toMatchSnapshot();
  });

  it('renders with label', () => {
    const TextInputTest = render(
      <Theme>
        <TextInput label="Test label" labelTextBold shadow disabled />,
      </Theme>,
    );
    expect(TextInputTest.toJSON()).toMatchSnapshot();
    expect(TextInputTest.getByText('Test label'));
  });

  it('renders with placeholder', () => {
    const TextInputTest = render(
      <Theme>
        <TextInput
          placeholder="Test placeholder"
          accessibilityLabel="Test"
          clearButtonEnabled
        />
        ,
      </Theme>,
    );
    expect(TextInputTest.toJSON()).toMatchSnapshot();
    expect(TextInputTest.getByPlaceholderText('Test placeholder'));
  });

  it('renders with maxLength', () => {
    const TextInputTest = render(
      <Theme>
        <TextInput maxLength={50} />,
      </Theme>,
    );
    expect(TextInputTest.toJSON()).toMatchSnapshot();
  });

  it('renders with assistive text', () => {
    const TextInputTest = render(
      <Theme>
        <TextInput assistiveText="Assistive text" />,
      </Theme>,
    );
    expect(TextInputTest.toJSON()).toMatchSnapshot();
  });

  it('renders with error', () => {
    const TextInputTest = render(
      <Theme>
        <TextInput error="Error Message" />,
      </Theme>,
    );
    expect(TextInputTest.toJSON()).toMatchSnapshot();
    expect(TextInputTest.getByText('Error Message'));
  });

  it('renders with correct error', () => {
    const TextInputTest = render(
      <Theme>
        <TextInput error="Error Message" clearButtonEnabled />,
      </Theme>,
    );
    expect(TextInputTest.getByText('Error Message'));
  });

  it('renders with correct success message', () => {
    const TextInputTest = render(
      <Theme>
        <TextInput success successText="Success Message" clearButtonEnabled />,
      </Theme>,
    );
    expect(TextInputTest.getByText('Success Message'));
  });

  it('renders without shadow', () => {
    const TextInputTest = render(
      <Theme>
        <TextInput shadow={false} />,
      </Theme>,
    );
    expect(TextInputTest.toJSON()).toMatchSnapshot();
  });

  it('should call onBlur', () => {
    const onBlurMock = jest.fn();
    const TextInputTest = render(
      <Theme>
        <TextInput onBlur={onBlurMock} accessibilityLabel="test" />,
      </Theme>,
    );

    fireEvent(TextInputTest.getByTestId('Enter test'), 'onBlur');
    expect(onBlurMock).toBeCalled();
  });

  it('should call onFocus', () => {
    const onFocusMock = jest.fn();
    const TextInputTest = render(
      <Theme>
        <TextInput onFocus={onFocusMock} accessibilityLabel="test" />,
      </Theme>,
    );

    fireEvent(TextInputTest.getByTestId('Enter test'), 'onFocus');
    expect(onFocusMock).toBeCalled();
  });
});
