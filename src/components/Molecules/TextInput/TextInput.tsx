import React, { useState } from 'react';
import { Image, TextInputProps } from 'react-native';
import { useTheme } from 'styled-components/native';

import alertCheckIcon from '../../../../assets/images/alert.png';
import circleCheckIcon from '../../../../assets/images/circleCheck.png';
import { CloseIcon, ErrorLabel } from '../../Atoms';
import * as S from './TextInput.styles';

interface IProps extends TextInputProps {
  assistiveText?: string;
  disabled?: boolean;
  label?: string;
  labelTextBold?: boolean;
  error?: boolean | string;
  success?: boolean;
  successText?: string;
  shadow?: boolean;
  accessibilityLabel?: string;
  clearButtonEnabled?: boolean;
  validCode?: boolean;
  clearButtonClb?: VoidFunction;
  onFocus?: VoidFunction;
  onBlur?: VoidFunction;
  maxLength?: number;
}

const TextInput = ({
  assistiveText,
  label,
  labelTextBold = false,
  error = false,
  success = false,
  successText,
  shadow = false,
  accessibilityLabel,
  clearButtonEnabled = false,
  validCode,
  clearButtonClb,
  onFocus: onFocusClb = () => {},
  onBlur: onBlurClb = () => {},
  maxLength,
  ...props
}: IProps) => {
  const [active, setActive] = useState(false);
  const theme = useTheme();

  const onFocus = () => {
    setActive(true);
    onFocusClb();
  };

  const onBlur = () => {
    setActive(false);
    onBlurClb();
  };

  const clearInputButtonColor = () => {
    if (error) {
      return theme.subscriptionMfe.textInput.error.color;
    }
    if (success) {
      return theme.subscriptionMfe.textInput.success.color;
    }
    return theme.subscriptionMfe.textInput.clearButtonColor;
  };

  return (
    <S.TextInputContainer>
      <S.LabelContainer>
        {label && (
          <S.Label
            accessibilityLabel={accessibilityLabel}
            accessibilityRole="text"
            testID={accessibilityLabel}
            bold={labelTextBold}
          >
            {label}
          </S.Label>
        )}
        {(label || assistiveText) && <S.LabelFiller />}
        {assistiveText && (
          <S.AssistiveText accessibilityRole="text">
            {assistiveText}
          </S.AssistiveText>
        )}
      </S.LabelContainer>
      <S.StyledTextWrapper>
        <S.StyledTextInput
          placeholderTextColor="rgba(17, 17, 17, 0.65)"
          maxLength={maxLength}
          clearButtonEnabled={clearButtonEnabled}
          error={!!error}
          success={success}
          active={active}
          shadow={shadow}
          accessible
          accessibilityLabel={`Enter ${accessibilityLabel}`}
          testID={`Enter ${accessibilityLabel}`}
          onFocus={onFocus}
          onBlur={onBlur}
          invalid={validCode === false}
          {...props}
        />
        {clearButtonEnabled && (
          <S.ClearInputButton
            onPress={clearButtonClb}
            testID="Clear"
            accessibilityLabel="Clear"
          >
            <CloseIcon
              width="16px"
              height="16px"
              color={clearInputButtonColor()}
            />
          </S.ClearInputButton>
        )}
        {/* This errorinput icon is exclamation->which doesnt clear the error where as clear icon on click clears the input entered */}
        {error && !clearButtonEnabled && (
          <S.ErrorImgWrapper>
            <Image source={alertCheckIcon} />
          </S.ErrorImgWrapper>
        )}
        {validCode && !clearButtonEnabled && (
          <S.ErrorImgWrapper>
            <Image source={circleCheckIcon} />
          </S.ErrorImgWrapper>
        )}
      </S.StyledTextWrapper>
      {((error && typeof error === 'string') || (success && successText)) && (
        <ErrorLabel status={error ? 'error' : 'success'} testID="ErrorLabel">
          {error || successText}
        </ErrorLabel>
      )}
    </S.TextInputContainer>
  );
};

export default TextInput;
