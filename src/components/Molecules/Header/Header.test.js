import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Text } from 'react-native';

import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import { CloseIcon } from '../../Atoms';
import Header from './Header';

const mockTranslations = TRANSLATIONS.en_GB;

describe('Card component', () => {
  it('renders with params', () => {
    const CardTest = render(
      <Theme>
        <Header text={'Welcome'} />
      </Theme>,
    );
    expect(CardTest.toJSON()).toMatchSnapshot();
  });
  it('should call the onPressBack callback when "back" is pressed', () => {
    const onBackPressMock = jest.fn();
    const { getByTestId } = render(
      <Theme>
        <Header left="Back" text={'Welcome'} onPressBack={onBackPressMock} />
      </Theme>,
    );
    fireEvent(getByTestId('BackPress'), 'onPressBack');
    expect(onBackPressMock).toHaveBeenCalled();
  });
  it('should call the onPressClear callback when "clear" is pressed', () => {
    const onClearPressMock = jest.fn();
    const { getByTestId } = render(
      <Theme>
        <Header
          right="Clear"
          text={'Welcome'}
          onPressClear={onClearPressMock}
        />
      </Theme>,
    );
    fireEvent(getByTestId('ClearPress'), 'onPressClear');
    expect(onClearPressMock).toHaveBeenCalled();
  });
  it('should call the onPressBack callback when the back icon is pressed', () => {
    const onBackPressMock = jest.fn();
    const { getByTestId } = render(
      <Theme>
        <Header
          left="Back"
          text={'Welcome'}
          onPressBack={onBackPressMock}
          leftIcon={<CloseIcon color="#000" />}
        />
      </Theme>,
    );
    fireEvent(getByTestId('BackPressIcon'), 'onPressBack');
    expect(onBackPressMock).toHaveBeenCalled();
  });
  it('It should render the children if it is present', () => {
    const HeaderWithChildren = render(
      <Theme>
        <Header
          title="Example"
          children={<Text>{mockTranslations.testing.HELLO}</Text>}
        />
      </Theme>,
    );
    expect(HeaderWithChildren.toJSON()).toMatchSnapshot();
  });
});
