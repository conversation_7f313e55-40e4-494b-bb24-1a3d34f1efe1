import React from 'react';

import * as S from './Header.styles';

interface IProps {
  title: string;
  left?: string;
  right?: string;
  onPressBack?: any;
  onPressClear?: any;
  leftIcon?: any;
  transparent?: boolean;
  children?: React.ReactNode;
}

export default (props: IProps) => {
  const {
    title,
    left,
    right,
    onPressBack,
    onPressClear,
    leftIcon,
    transparent,
    children,
  } = props;
  return (
    <S.HeaderContainer>
      <S.Card transparent={transparent}>
        {leftIcon ? (
          <S.ButtonIcon
            accessibilityLabel="close"
            accessibilityRole="button"
            onPress={onPressBack}
            testID="BackPressIcon"
          >
            {leftIcon}
          </S.ButtonIcon>
        ) : (
          <S.Button onPress={onPressBack} testID="BackPress">
            <S.CardLeftText>{left}</S.CardLeftText>
          </S.Button>
        )}
        <S.CardTitleText transparent={transparent}>{title}</S.CardTitleText>
        <S.Button onPress={onPressClear} testID="ClearPress">
          <S.CardRightText right={right}>{right}</S.CardRightText>
        </S.Button>
      </S.Card>
      {children && <S.Container>{children}</S.Container>}
    </S.HeaderContainer>
  );
};
