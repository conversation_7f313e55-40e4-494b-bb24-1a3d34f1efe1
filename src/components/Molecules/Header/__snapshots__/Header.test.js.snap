// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Card component It should render the children if it is present 1`] = `
<View
  style={
    {
      "flexDirection": "column",
      "justifyContent": "space-between",
      "zIndex": 2,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "#FFFFFF",
        "flexDirection": "row",
        "height": 40,
        "justifyContent": "space-between",
        "paddingBottom": 8,
        "paddingLeft": 16,
        "paddingRight": 16,
        "paddingTop": 8,
        "zIndex": 2,
      }
    }
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "minWidth": 44,
          "opacity": 1,
        }
      }
      testID="BackPress"
    >
      <Text
        style={
          {
            "alignSelf": "center",
            "fontSize": 16,
            "paddingLeft": 10,
            "paddingRight": 10,
          }
        }
      />
    </View>
    <Text
      style={
        {
          "alignSelf": "center",
          "color": "#000000",
          "fontSize": 20,
          "lineHeight": 24,
          "paddingLeft": 10,
          "paddingRight": 10,
        }
      }
    >
      Example
    </Text>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "minWidth": 44,
          "opacity": 1,
        }
      }
      testID="ClearPress"
    >
      <Text
        style={
          {
            "alignSelf": "center",
            "color": "#CF0001",
            "fontSize": 16,
            "paddingLeft": 10,
            "paddingRight": 10,
          }
        }
      />
    </View>
  </View>
  <View
    style={
      {
        "backgroundColor": "#FFFFFF",
        "borderBottomColor": "#dddddd",
        "borderBottomLeftRadius": 16,
        "borderBottomRightRadius": 16,
        "borderBottomWidth": 1,
        "elevation": 2,
        "paddingBottom": 24,
        "paddingLeft": 32,
        "paddingRight": 32,
        "paddingTop": 16,
        "shadowColor": "#000000",
        "shadowOffset": {
          "height": 4,
          "width": 0,
        },
        "shadowOpacity": 0.2,
        "shadowRadius": 4,
        "width": "100%",
        "zIndex": 1,
      }
    }
  >
    <Text>
      Hello
    </Text>
  </View>
</View>
`;

exports[`Card component renders with params 1`] = `
<View
  style={
    {
      "flexDirection": "column",
      "justifyContent": "space-between",
      "zIndex": 2,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "#FFFFFF",
        "flexDirection": "row",
        "height": 40,
        "justifyContent": "space-between",
        "paddingBottom": 8,
        "paddingLeft": 16,
        "paddingRight": 16,
        "paddingTop": 8,
        "zIndex": 2,
      }
    }
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "minWidth": 44,
          "opacity": 1,
        }
      }
      testID="BackPress"
    >
      <Text
        style={
          {
            "alignSelf": "center",
            "fontSize": 16,
            "paddingLeft": 10,
            "paddingRight": 10,
          }
        }
      />
    </View>
    <Text
      style={
        {
          "alignSelf": "center",
          "color": "#000000",
          "fontSize": 20,
          "lineHeight": 24,
          "paddingLeft": 10,
          "paddingRight": 10,
        }
      }
    />
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "minWidth": 44,
          "opacity": 1,
        }
      }
      testID="ClearPress"
    >
      <Text
        style={
          {
            "alignSelf": "center",
            "color": "#CF0001",
            "fontSize": 16,
            "paddingLeft": 10,
            "paddingRight": 10,
          }
        }
      />
    </View>
  </View>
</View>
`;
