import { styled } from 'styled-components/native';

interface ITransparentProp {
  transparent?: boolean;
}

export const HeaderContainer = styled.View<ITransparentProp>`
  flex-direction: column;
  justify-content: space-between;
  z-index: 2;
`;

export const Card = styled.View<ITransparentProp>`
  height: 40px;
  flex-direction: row;
  background-color: ${(p) =>
    p.transparent ? 'transparent' : p.theme.subscriptionMfe.header.background};
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  z-index: 2;
`;

export const CardLeftText = styled.Text`
  font-size: 16px;
  align-self: center;
  padding-right: 10px;
  padding-left: 10px;
`;

export const CardTitleText = styled.Text<ITransparentProp>`
  font-size: 20px;
  line-height: 24px;
  align-self: center;
  padding-right: 10px;
  padding-left: 10px;
  color: ${(props) =>
    props.transparent
      ? props.theme.subscriptionMfe.text.color.white
      : props.theme.subscriptionMfe.text.color.primary};
`;

export const CardRightText = styled.Text<{ right?: string }>`
  font-size: 16px;
  align-self: center;
  padding-right: 10px;
  padding-left: 10px;
  color: ${(props: any) =>
    props.right === 'Skip'
      ? props.theme.subscriptionMfe.text.color.white
      : props.theme.subscriptionMfe.color.state.error};
`;

export const ButtonIcon = styled.TouchableOpacity`
  width: 44px;
  height: 100%;
  justify-content: center;
`;

export const Button = styled.TouchableOpacity`
  min-width: 44px;
`;

export const Container = styled.View`
  width: 100%;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  border-bottom-color: #dddddd;
  border-bottom-width: 1px;
  background-color: ${(p) => p.theme.subscriptionMfe.color.white};
  padding: 16px 32px 24px 32px;
  shadow-color: ${(p) => p.theme.subscriptionMfe.shadow.default.color};
  shadow-offset: 0px 4px;
  shadow-opacity: ${(p) => p.theme.subscriptionMfe.shadow.default.opacity};
  shadow-radius: 4px;
  elevation: ${(p) => p.theme.subscriptionMfe.shadow.default.elevation};
  z-index: 1;
`;
