import { fireEvent, render, screen } from '@testing-library/react-native';
import React from 'react';

import { UserTypeEnum } from '../../../common/enums';
import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import NetworkConnectionError from './NetworkConnectionError';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: {
    id: '123',
    balance: '1.99',
    gocardless: { mandateStatus: 'applied' },
    tagIds: [],
  },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  featureFlags: {
    enableInvoicesList: true,
    enableOfferDetails: true,
    enableRFID: true,
  },
  isInternetReachable: true,
};

jest.mock('react-native-device-info', () => {
  return {
    hasNotch: jest.fn(),
    mockRNDeviceInfo: jest.fn(),
  };
});

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);

jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockUserInfoObj = {
  userInfo: {
    type: UserTypeEnum.NEW,
    gocardless: {},
  },
  isLoading: true,
  refetchUserInfo: jest.fn(),
};
const mockUserInfo = jest.fn().mockReturnValue(mockUserInfoObj);
jest.mock('../../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfo(),
}));

const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

jest.mock('../../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

const renderComponent = () =>
  render(
    <Theme>
      <NetworkConnectionError
        isHeaderRequired={true}
        headingText={mockUseSettingsObj.t.gocardless.text.SUBSCRIBE_HEADER}
        onPress={() => jest.fn()}
      />
    </Theme>,
  );

describe('NetworkConnectionError Component', () => {
  it('should render correctly', () => {
    const { getByText } = renderComponent();
    expect(
      getByText(mockUseSettingsObj.t.gocardless.text.SUBSCRIBE_HEADER),
    ).toBeDefined();

    const header = screen.getByTestId('backIcon');

    fireEvent(header, 'onPressBack');

    expect(mockHostNavigationObj.navigation.goBack).toHaveBeenCalled();
  });
});
