import { NoSignalError } from '@bp/ui-components/mobile/core';
import React from 'react';

import { useHostNavigation } from '../../../providers/HostNavigationProvider';
import { useSettings } from '../../../providers/Settings';
import { getCountrySpecificText } from '../../../utils/helpers';
import { BackIcon, Header } from '../..';
import * as S from './NetworkConnectionError.styles';

interface INetworkConnectionErrorProps {
  isHeaderRequired: boolean;
  headingText: string | '';
  onPress: () => void;
}

const NetworkConnectionError = ({
  isHeaderRequired,
  headingText,
  onPress,
}: INetworkConnectionErrorProps) => {
  const { t, user } = useSettings();
  const { navigation } = useHostNavigation();

  return (
    <S.ScreenWrapper>
      {isHeaderRequired && (
        <S.HeaderPosition>
          <Header
            title={headingText}
            onPressBack={() => navigation.goBack()}
            leftIcon={<BackIcon color="black" />}
          />
        </S.HeaderPosition>
      )}

      <NoSignalError
        loadingFailedText={getCountrySpecificText(
          t,
          'networkConnectionError.text.FAILED_HEADING_TEXT',
          user?.country,
        )}
        subheadingText={t.networkConnectionError.text.SUB_HEADING_TEXT}
        retryBtnText={t.networkConnectionError.text.RETRY}
        retryBtnOnPress={onPress}
      />
    </S.ScreenWrapper>
  );
};

export default NetworkConnectionError;
