// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CancelSubscriptionWidget rendering Should render correctly 1`] = `
<View
  accessibilityHint="test"
  accessibilityLabel="test"
  accessibilityState={
    {
      "busy": undefined,
      "checked": undefined,
      "disabled": false,
      "expanded": undefined,
      "selected": undefined,
    }
  }
  accessibilityValue={
    {
      "max": undefined,
      "min": undefined,
      "now": undefined,
      "text": undefined,
    }
  }
  accessible={true}
  collapsable={false}
  focusable={true}
  onBlur={[Function]}
  onClick={[Function]}
  onFocus={[Function]}
  onResponderGrant={[Function]}
  onResponderMove={[Function]}
  onResponderRelease={[Function]}
  onResponderTerminate={[Function]}
  onResponderTerminationRequest={[Function]}
  onStartShouldSetResponder={[Function]}
  style={
    {
      "alignItems": "center",
      "borderColor": "#dedede",
      "borderLeftWidth": 0,
      "borderRightWidth": 0,
      "borderStyle": "solid",
      "borderTopWidth": 0,
      "borderWidth": 1,
      "display": "flex",
      "flexDirection": "row",
      "paddingBottom": 25,
      "paddingLeft": 24,
      "paddingRight": 24,
      "paddingTop": 25,
    }
  }
  testID="cancelWidget"
>
  <RNSVGSvgView
    align="xMidYMid"
    bbHeight="24"
    bbWidth="24"
    focusable={false}
    height="24"
    meetOrSlice={0}
    minX={0}
    minY={0}
    style={
      [
        {
          "backgroundColor": "transparent",
          "borderWidth": 0,
        },
        {
          "flex": 0,
          "height": 24,
          "width": 24,
        },
      ]
    }
    testID="rightArrow"
    vbHeight={24}
    vbWidth={24}
    width="24"
  >
    <RNSVGGroup
      fill={
        {
          "payload": 4278190080,
          "type": 0,
        }
      }
    >
      <RNSVGGroup
        fill={null}
        propList={
          [
            "fill",
          ]
        }
      >
        <RNSVGPath
          d="M0 0h24v24H0z"
          fill={null}
          propList={
            [
              "fill",
            ]
          }
        />
        <RNSVGPath
          d="M15 7.38c1.8 1.02 3 3 3 5.22 0 3.3-2.7 6-6 6s-6-2.7-6-6c0-2.22 1.2-4.14 3-5.22m3-1.98v4.2"
          fill={
            {
              "payload": 4278190080,
              "type": 0,
            }
          }
          propList={
            [
              "stroke",
            ]
          }
          stroke={
            {
              "payload": 4293282121,
              "type": 0,
            }
          }
        />
      </RNSVGGroup>
    </RNSVGGroup>
  </RNSVGSvgView>
  <Text
    style={
      {
        "color": "#e64949",
        "fontSize": 16,
        "fontWeight": "400",
        "lineHeight": 28,
        "marginLeft": 16,
      }
    }
  >
    test
  </Text>
  <View
    style={
      {
        "display": "flex",
        "flexBasis": 0,
        "flexDirection": "row",
        "flexGrow": 1,
        "flexShrink": 1,
        "justifyContent": "flex-end",
      }
    }
  >
    <RNSVGSvgView
      align="xMidYMid"
      bbHeight="24"
      bbWidth="24"
      focusable={false}
      height="24"
      meetOrSlice={0}
      minX={0}
      minY={0}
      style={
        [
          {
            "backgroundColor": "transparent",
            "borderWidth": 0,
          },
          {
            "flex": 0,
            "height": 24,
            "width": 24,
          },
        ]
      }
      testID="rightArrow"
      vbHeight={24}
      vbWidth={24}
      width="24"
    >
      <RNSVGGroup
        fill={
          {
            "payload": 4278190080,
            "type": 0,
          }
        }
      >
        <RNSVGGroup
          fill={null}
          propList={
            [
              "fill",
            ]
          }
        >
          <RNSVGPath
            d="M0 0h24v24H0z"
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
          />
          <RNSVGPath
            d="M8.646 18.354a.5.5 0 0 1-.057-.638l.057-.07L14.293 12 8.646 6.354a.5.5 0 0 1-.057-.638l.057-.07a.5.5 0 0 1 .638-.057l.07.057 6 6a.5.5 0 0 1 .057.638l-.057.07-6 6a.5.5 0 0 1-.708 0z"
            fill={
              {
                "payload": 4293282121,
                "type": 0,
              }
            }
            propList={
              [
                "fill",
              ]
            }
          />
        </RNSVGGroup>
      </RNSVGGroup>
    </RNSVGSvgView>
  </View>
</View>
`;
