import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import theme from '../../../themes/Theme.default';
import { CancelSubscriptionWidget } from './CancelSubscriptionWidget';

const mockCancel = jest.fn();

describe('CancelSubscriptionWidget rendering', () => {
  it('Should render correctly', () => {
    const result = render(
      <Theme>
        <CancelSubscriptionWidget
          title="test"
          onCancel={() => {}}
          theme={theme}
          disabled={false}
        />
      </Theme>,
    );
    expect(result.toJSON()).toMatchSnapshot();
  });
  it('Cancel function should trigger on press', () => {
    const { getByTestId } = render(
      <Theme>
        <CancelSubscriptionWidget
          title="test2"
          onCancel={mockCancel}
          theme={theme}
          disabled={false}
        />
      </Theme>,
    );
    const button = getByTestId('cancelWidget');
    expect(button).toBeDefined();
    fireEvent(button, 'onCancel');
    expect(mockCancel).toHaveBeenCalled();
  });
});
