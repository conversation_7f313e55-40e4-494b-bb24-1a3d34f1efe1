import { styled } from 'styled-components/native';

export const ButtonWrapper = styled.Pressable`
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 25px 24px;
  border: 1px solid #dedede;
  border-left-width: 0;
  border-right-width: 0;
  border-top-width: 0;
`;

export const NormalText = styled.Text`
  font-size: 16px;
  line-height: 28px;
  font-weight: 400;
  margin-left: 16px;
  color: ${({ theme }) =>
    theme.subscriptionMfe.cancelSubscription.color.primary};
`;

export const RightArrowContainer = styled.View`
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
`;
