import React from 'react';

import { ICancelWidget } from '../../../common/interfaces';
import { RightArrow, ShutDown } from '../../Atoms';
import * as S from './CancelSubscriptionWidget.styles';

export const CancelSubscriptionWidget = ({
  title,
  theme,
  onCancel,
  disabled,
}: ICancelWidget) => {
  return (
    <S.ButtonWrapper
      testID="cancelWidget"
      disabled={disabled}
      onPress={onCancel}
      accessibilityLabel={title}
      accessibilityHint={title}
    >
      <ShutDown
        color={theme.subscriptionMfe?.cancelSubscription.color.primary}
      />
      <S.NormalText>{title}</S.NormalText>
      <S.RightArrowContainer>
        <RightArrow
          color={theme.subscriptionMfe?.cancelSubscription.color.primary}
        />
      </S.RightArrowContainer>
    </S.ButtonWrapper>
  );
};
