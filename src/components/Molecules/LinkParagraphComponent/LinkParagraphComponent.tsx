import React from 'react';

import { useSettings } from '../../../providers/Settings';
import CustomLinkParagraphComponent from '../CustomLinkParagraphComponent/CustomLinkParagraphComponent';
import * as S from './LinkParagraphComponent.style';
const LinkParagraphComponent = () => {
  const { t } = useSettings();
  const PRICING_URL = 'https://chargers.bppulse.com/';

  return (
    <S.ParagraphContainer>
      <S.Message>
        <CustomLinkParagraphComponent
          text={t.gocardless.text.ASTERISK_TEXT}
          links={[
            { text: t.gocardless.text.ASTERISK_TEXT_LINK, url: PRICING_URL },
          ]}
        />
      </S.Message>
    </S.ParagraphContainer>
  );
};

export default LinkParagraphComponent;
