import { styled } from 'styled-components/native';

export const ParagraphContainer = styled.Text`
  margin: 10px 24px 16px 24px;
  font-family: 'Roboto-Regular';
`;

export const Message = styled.Text`
  text-align: left;
  font-weight: 300;
  line-height: 23px;
  letter-spacing: 0.2px;
  font-size: 13px;
  color: ${(p) => p.theme.subscriptionMfe.color.black2};
`;

export const Link = styled.Text`
  text-align: center;
  font-size: 13px;
  align-items: flex-start;
  color: #000096;
  text-decoration: underline;
`;
