import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Linking } from 'react-native';

import { useSettings } from '../../../providers/Settings';
import Theme from '../../../themes/Theme';
import LinkParagraphComponent from './LinkParagraphComponent';

jest.mock('../../../providers/Settings', () => ({
  useSettings: jest.fn(),
}));

jest.mock('react-native/Libraries/Linking/Linking', () => ({
  openURL: jest.fn(),
}));

describe('LinkParagraphComponent', () => {
  const mockTranslations = {
    gocardless: {
      text: {
        ASTERISK_TEXT: 'This is a test before {{link}} and after.',
        ASTERISK_TEXT_LINK: 'Click here',
      },
    },
  };

  const mockPricingUrl = 'https://chargers.bppulse.com/';

  const mockUseSettingsObj = {
    t: mockTranslations,
  };

  beforeEach(() => {
    useSettings.mockReturnValue(mockUseSettingsObj);
  });

  it('renders the full text including the link', () => {
    const { getByText } = render(
      <Theme>
        <LinkParagraphComponent />
      </Theme>,
    );
    const fullText = 'This is a test before Click here and after.';
    expect(getByText(fullText)).toBeTruthy();
  });

  it('renders the link with the correct label', () => {
    const { getByText } = render(
      <Theme>
        <LinkParagraphComponent />
      </Theme>,
    );
    const link = getByText(mockTranslations.gocardless.text.ASTERISK_TEXT_LINK);
    expect(link).toBeTruthy();
  });

  it('calls Linking.openURL with the correct URL when the link is clicked', () => {
    const { getByText } = render(
      <Theme>
        <LinkParagraphComponent />
      </Theme>,
    );
    const link = getByText(mockTranslations.gocardless.text.ASTERISK_TEXT_LINK);

    fireEvent.press(link);

    expect(Linking.openURL).toHaveBeenCalledWith(mockPricingUrl);
  });
});
