import { styled } from 'styled-components/native';

export const Container = styled.SafeAreaView<{ backgroundColor: string }>`
  background-color: ${({ backgroundColor }) => backgroundColor};
  flex-direction: row;
`;

export const TextWrapper = styled.View`
  align-items: center;
  flex: 1;
`;

export const Text = styled.Text<{ color: string }>`
  font-size: 20px;
  line-height: 30px;
  letter-spacing: 0.33px;
  text-align: center;
  color: ${({ color }) => color};
`;
