import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { View } from 'react-native';

import MultilineHeader from './MultilineHeader';

const LEFT_BUTTON_WRAPPER_TEST_ID = 'multi-line-header-left-button-wrapper';
const RIGHT_BUTTON_DUMMY_TEST_ID = 'multi-line-header-right-dummy';

const renderComponent = () =>
  render(
    <MultilineHeader
      title={`First line\nSecond line\nThird line`}
      LeftButton={<View />}
    />,
  );

const SIMULATED_WIDTH = 123;

describe('<MultilineHeader />', () => {
  it('should correctly split title into seperate lines', () => {
    const { toJSON } = renderComponent();

    expect(toJSON()).toMatchSnapshot();
  });
  it('should correctly set dummy width', () => {
    const { getByTestId } = renderComponent();

    expect(getByTestId(LEFT_BUTTON_WRAPPER_TEST_ID)).toBeDefined();

    fireEvent(getByTestId(LEFT_BUTTON_WRAPPER_TEST_ID), 'layout', {
      nativeEvent: { layout: { width: SIMULATED_WIDTH } },
    });

    expect(
      getByTestId(RIGHT_BUTTON_DUMMY_TEST_ID).props.style.width,
    ).toStrictEqual(SIMULATED_WIDTH);
  });
});
