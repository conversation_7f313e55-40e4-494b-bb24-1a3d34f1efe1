import React, { useMemo, useState } from 'react';
import { LayoutChangeEvent, View } from 'react-native';

import * as S from './MultilineHeader.styles';

type Props = {
  title: string;
  LeftButton: React.ReactElement;
  color?: string;
  backgroundColor?: string;
};

const MultilineHeader = ({
  title,
  LeftButton,
  color = 'black',
  backgroundColor = '#fff',
}: Props) => {
  const lines = useMemo(() => title.split('\n'), [title]);

  const [dummyWidth, setDummyWidth] = useState(0);

  const handleLeftButtonLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setDummyWidth(width);
  };

  return (
    <S.Container backgroundColor={backgroundColor}>
      <View
        onLayout={handleLeftButtonLayout}
        testID="multi-line-header-left-button-wrapper"
      >
        {LeftButton}
      </View>
      <S.TextWrapper>
        {lines.map((line) => (
          <S.Text key={line} color={color}>
            {line}
          </S.Text>
        ))}
      </S.TextWrapper>
      <View
        testID="multi-line-header-right-dummy"
        style={{ width: dummyWidth }}
      />
    </S.Container>
  );
};

export default MultilineHeader;
