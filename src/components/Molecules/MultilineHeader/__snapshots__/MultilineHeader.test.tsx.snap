// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<MultilineHeader /> should correctly split title into seperate lines 1`] = `
<RCTSafeAreaView
  backgroundColor="#fff"
  style={
    {
      "backgroundColor": "#fff",
      "flexDirection": "row",
    }
  }
>
  <View
    onLayout={[Function]}
    testID="multi-line-header-left-button-wrapper"
  >
    <View />
  </View>
  <View
    style={
      {
        "alignItems": "center",
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
      }
    }
  >
    <Text
      color="black"
      style={
        {
          "color": "black",
          "fontSize": 20,
          "letterSpacing": 0.33,
          "lineHeight": 30,
          "textAlign": "center",
        }
      }
    >
      First line
    </Text>
    <Text
      color="black"
      style={
        {
          "color": "black",
          "fontSize": 20,
          "letterSpacing": 0.33,
          "lineHeight": 30,
          "textAlign": "center",
        }
      }
    >
      Second line
    </Text>
    <Text
      color="black"
      style={
        {
          "color": "black",
          "fontSize": 20,
          "letterSpacing": 0.33,
          "lineHeight": 30,
          "textAlign": "center",
        }
      }
    >
      Third line
    </Text>
  </View>
  <View
    style={
      {
        "width": 0,
      }
    }
    testID="multi-line-header-right-dummy"
  />
</RCTSafeAreaView>
`;
