// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RowWithIcon rendering Should render correctly 1`] = `
<View
  style={
    {
      "alignItems": "flex-start",
      "flexDirection": "row",
      "paddingBottom": 0,
      "paddingLeft": 16,
      "paddingRight": 16,
      "paddingTop": 0,
    }
  }
>
  <View
    style={
      {
        "marginRight": 8,
        "paddingTop": 8,
        "width": 20,
      }
    }
  />
  <Text
    style={
      {
        "paddingBottom": 12,
        "paddingLeft": 0,
        "paddingRight": 24,
        "paddingTop": 0,
      }
    }
  >
    <Text
      style={
        {
          "color": "#1D1D26",
          "fontSize": 16,
          "fontWeight": "bold",
          "lineHeight": 28,
          "textAlign": "left",
        }
      }
    >
      Test
      

    </Text>
    <Text
      style={
        {
          "color": "#1D1D26",
          "fontSize": 13,
          "lineHeight": 24,
          "textAlign": "left",
        }
      }
    />
  </Text>
</View>
`;
