import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import { RowWithIcon } from './RowWithIcon';

describe('RowWithIcon rendering', () => {
  it('Should render correctly', () => {
    const result = render(
      <Theme>
        <RowWithIcon id="1" title="Test" icon="" subTitle="" />
      </Theme>,
    );
    expect(result.toJSON()).toMatchSnapshot();
  });
});
