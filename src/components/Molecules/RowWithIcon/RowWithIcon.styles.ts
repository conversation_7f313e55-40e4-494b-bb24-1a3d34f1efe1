import { styled } from 'styled-components/native';
export const Title = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.black2};
  font-size: 16px;
  letter-spacing: 0.1px;
  line-height: 28px;

  padding: 0 24px 16px 24px;
`;

export const TableRow = styled.View`
  flex-direction: row;
  align-items: flex-start;
  padding: 0 16px;
`;

export const LeftImageView = styled.View<{ type?: string }>`
  width: 20px;
  padding-top: 8px;
  margin-right: 8px;
`;

export const WrapperText = styled.Text`
  padding: 0 24px 12px 0;
`;

export const TableTitle = styled.Text`
  text-align: left;
  font-size: 16px;
  font-weight: bold;
  line-height: 28px;
  color: ${({ theme }) => theme.subscriptionMfe.color.black2};
`;

export const TableSubTitle = styled.Text`
  text-align: left;
  font-size: 13px;
  line-height: 24px;
  color: ${({ theme }) => theme.subscriptionMfe.color.black2};
`;
