import React from 'react';

import { IRowWithData } from '../../../common/interfaces';
import * as S from './RowWithIcon.styles';

export interface IProps {
  title: string;
  subTitle?: string;
  description?: string;
  type?: 'primary' | 'secondary';
}

export const RowWithIcon = ({ id, icon, title, subTitle }: IRowWithData) => {
  return (
    <S.TableRow key={id}>
      <S.LeftImageView>{icon}</S.LeftImageView>
      <S.WrapperText>
        <S.TableTitle>
          {title}
          {'\n'}
        </S.TableTitle>
        <S.TableSubTitle>{subTitle}</S.TableSubTitle>
      </S.WrapperText>
    </S.TableRow>
  );
};
