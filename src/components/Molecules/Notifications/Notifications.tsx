import { SimpleNotification } from '@bp/ui-components/mobile/core';
import React from 'react';

import CardNotAdded from '../../../../assets/svg/alertCardNotAdded.svg';
import Card from '../../../../assets/svg/basicCreditCard.svg';
import { useSettings } from '../../../providers/Settings';

interface NotificationsProps {
  success: boolean;
  onComplete: () => unknown;
}

export const Notifications = ({ success, onComplete }: NotificationsProps) => {
  const { t } = useSettings();

  return (
    <SimpleNotification
      color={success ? '#222' : '#9E3232'}
      iconType="small"
      title={
        success
          ? t.notifications.cardSuccesfullyUpdated
          : t.notifications.cardUnableToUpdate
      }
      icon={success ? <Card /> : <CardNotAdded />}
      duration={1000}
      timeoutDuration={5000}
      onComplete={onComplete}
      bottomPosition={24}
    />
  );
};
