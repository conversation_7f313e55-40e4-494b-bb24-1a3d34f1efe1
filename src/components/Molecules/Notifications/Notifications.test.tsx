import { render } from '@testing-library/react-native';
import React from 'react';

import TRANSLATIONS from '../../../translations/messages.json';
import { updateTranslations } from '../../../utils/updateTranslations';
import { Notifications } from './Notifications';

const mockTranslations = TRANSLATIONS;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'bp' }),
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));
describe('<Notifications />', () => {
  it('renders succesfully on payment update', () => {
    const { getByText } = render(
      <Notifications success={true} onComplete={() => {}} />,
    );

    expect(getByText('Payment card successfully updated')).toBeDefined();
  });
  it('renders succesfully when payment update fails', () => {
    const { getByText } = render(
      <Notifications success={false} onComplete={() => {}} />,
    );

    expect(getByText('Unable to update payment card')).toBeDefined();
  });
});
