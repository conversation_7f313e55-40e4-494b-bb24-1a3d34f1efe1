import React from 'react';

import * as S from './FootnoteDetail.styles';

const Detail: React.FC<{
  left?: string;
  right: string | React.ReactNode;
  accessibilityHintMsg?: string;
  isLink?: boolean;
}> = React.memo(
  ({ left, right, accessibilityHintMsg = '', isLink = false }) => (
    <S.DetailWrapper>
      {left && <S.DetailKey>{left}</S.DetailKey>}
      <S.DetailContent
        accessibilityRole={isLink ? 'link' : 'text'}
        accessibilityHint={accessibilityHintMsg ? accessibilityHintMsg : ''}
        accessible={true}
        importantForAccessibility="yes"
      >
        {right}
      </S.DetailContent>
    </S.DetailWrapper>
  ),
);

export default Detail;
