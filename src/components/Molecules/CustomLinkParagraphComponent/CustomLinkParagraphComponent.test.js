import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { Linking } from 'react-native';

import CustomLinkParagraphComponent from './CustomLinkParagraphComponent';

jest.mock('react-native/Libraries/Linking/Linking', () => ({
  openURL: jest.fn(),
}));
const firstLink = 'First Link';
const secondLink = 'Second Link';

describe('CustomLinkParagraphComponent', () => {
  const text =
    'Check this out {{link}} and don’t miss this {{link}} for more info.';
  const links = [
    { text: firstLink, url: 'https://example.com/first' },
    { text: secondLink, url: 'https://example.com/second' },
  ];

  it('renders static text and links in correct order', () => {
    const { toJSON } = render(
      <CustomLinkParagraphComponent text={text} links={links} />,
    );

    expect(toJSON()).toMatchSnapshot();
  });

  it('calls Linking.openURL with correct URL on link press', () => {
    const { getByText } = render(
      <CustomLinkParagraphComponent text={text} links={links} />,
    );

    fireEvent.press(getByText('First Link'));
    expect(Linking.openURL).toHaveBeenCalledWith('https://example.com/first');

    fireEvent.press(getByText('Second Link'));
    expect(Linking.openURL).toHaveBeenCalledWith('https://example.com/second');
  });

  it('does not render extra links if there are more placeholders than links', () => {
    const shortLinks = [{ text: 'Only Link', url: 'https://example.com/only' }];
    const { getByText, queryByText } = render(
      <CustomLinkParagraphComponent text={text} links={shortLinks} />,
    );

    expect(getByText('Only Link')).toBeTruthy();
    expect(queryByText(secondLink)).toBeNull();
  });
});
