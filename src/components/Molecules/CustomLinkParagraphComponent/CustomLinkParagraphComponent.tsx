import React from 'react';
import { Linking } from 'react-native';

import * as S from './CustomLinkParagraphComponent.styles';

type LinkInfo = {
  text: string;
  url: string;
};

type LinkParagraphProps = {
  text: string;
  links: LinkInfo[];
};

const CustomLinkParagraphComponent: React.FC<LinkParagraphProps> = ({
  text,
  links,
}) => {
  const LINK_TAG = '{{link}}';
  const parts = text.split(LINK_TAG);

  return (
    <>
      {parts.map((part, index) => {
        return (
          <React.Fragment key={index}>
            {part}
            {index < links.length && (
              <S.Link
                onPress={() => Linking.openURL(links[index].url)}
                accessibilityRole="button"
                accessibilityLabel={links[index].text}
              >
                {links[index].text}
              </S.Link>
            )}
          </React.Fragment>
        );
      })}
    </>
  );
};
export default CustomLinkParagraphComponent;
