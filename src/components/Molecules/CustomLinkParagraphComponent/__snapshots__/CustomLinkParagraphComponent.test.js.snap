// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`CustomLinkParagraphComponent renders static text and links in correct order 1`] = `
[
  "Check this out ",
  <Text
    accessibilityLabel="First Link"
    accessibilityRole="button"
    onPress={[Function]}
    style={
      {
        "alignItems": "flex-start",
        "color": "#000096",
        "fontSize": 12,
        "textAlign": "center",
        "textDecorationColor": "black",
        "textDecorationLine": "underline",
        "textDecorationStyle": "solid",
      }
    }
  >
    First Link
  </Text>,
  " and don’t miss this ",
  <Text
    accessibilityLabel="Second Link"
    accessibilityRole="button"
    onPress={[Function]}
    style={
      {
        "alignItems": "flex-start",
        "color": "#000096",
        "fontSize": 12,
        "textAlign": "center",
        "textDecorationColor": "black",
        "textDecorationLine": "underline",
        "textDecorationStyle": "solid",
      }
    }
  >
    Second Link
  </Text>,
  " for more info.",
]
`;
