import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import CheckBoxInput from './CheckBoxInput';

describe('CheckBoxInput component', () => {
  it('renders default', () => {
    const CheckBoxTest = render(
      <Theme>
        <CheckBoxInput />,
      </Theme>,
    );
    expect(CheckBoxTest.toJSON()).toMatchSnapshot();
  });

  it('renders with label', () => {
    const CheckBoxTest = render(
      <Theme>
        <CheckBoxInput label="Test label" />,
      </Theme>,
    );
    expect(CheckBoxTest.toJSON()).toMatchSnapshot();
  });

  it('renders checked', () => {
    const CheckBoxTest = render(
      <Theme>
        <CheckBoxInput defaultChecked={true} />,
      </Theme>,
    );
    expect(CheckBoxTest.toJSON()).toMatchSnapshot();
  });

  it('renders with error', async () => {
    const CheckBoxTest = render(
      <Theme>
        <CheckBoxInput error={true} errorMessage="Error message" />,
      </Theme>,
    );
    expect(CheckBoxTest.toJSON()).toMatchSnapshot();
    const message = await CheckBoxTest.findByText('Error message');
    expect(message).toBeDefined();
  });

  it('should fire onPress when pressed', () => {
    const onPressMock = jest.fn();
    const { getAllByTestId } = render(
      <Theme>
        <CheckBoxInput testID="Checkbox ID" onPress={onPressMock} />
      </Theme>,
    );

    const TouchableCheckBox = getAllByTestId('Checkbox ID')[0];
    fireEvent.press(TouchableCheckBox);
    expect(onPressMock).toHaveBeenCalled();

    const Check = TouchableCheckBox.props.children;
    expect(Check).toBeTruthy();
  });
});
