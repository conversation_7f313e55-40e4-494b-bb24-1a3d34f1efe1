import { styled } from 'styled-components/native';

interface Checked {
  checked: boolean;
}
interface ICheckbox {
  checked: boolean;
  type: string;
  noBorderRadius?: boolean;
}
interface ICheckboxlabel {
  color: string;
}

export const ViewContainer = styled.View<Checked>`
  flex-direction: column;
`;

export const CheckBoxInputContainer = styled.View<{ alignCenter?: boolean }>`
  flex-basis: auto;
  flex-direction: row;
  align-items: ${({ alignCenter }) => (alignCenter ? 'center' : 'start')};
`;

const subscriptionCardFontSize = ({ theme }: any) =>
  theme.subscriptionMfe.card['text-medium'].size;

const subscriptionCardLineHeight = ({ theme }: any) =>
  theme.subscriptionMfe.card['text-medium'].lineHeight;

export const CheckBox = styled.View<ICheckbox>`
  flex-basis: 24px;
  height: 24px;
  width: 24px;
  border: 1px solid
    ${({ checked, theme, type }) =>
      checked
        ? theme.subscriptionMfe.checkBox[
            type as keyof typeof theme.subscriptionMfe.checkBox
          ].checked.borderColor
        : theme.subscriptionMfe.checkBox[
            type as keyof typeof theme.subscriptionMfe.checkBox
          ].borderColor};
  border-radius: ${({ noBorderRadius }) => (noBorderRadius ? '0px' : '4px')};
  background-color: ${({ checked, theme, type }: any) =>
    checked
      ? theme.subscriptionMfe.checkBox[
          type as keyof typeof theme.subscriptionMfe.checkBox
        ].checked.backgroundColor
      : theme.subscriptionMfe.checkBox[
          type as keyof typeof theme.subscriptionMfe.checkBox
        ].backgroundColor};
`;

export const Check = styled.View`
  padding: 12.5%;
`;

export const Label = styled.Text<ICheckboxlabel>`
  flex-shrink: 1;
  margin: auto 0;
  margin-left: 16px;
  line-height: 18px;
  letter-spacing: 0.6px;
  color: ${({ color }: any) => (color ? color : 'rgba(17, 17, 17, 0.7)')};
  font-size: ${subscriptionCardFontSize};
  line-height: ${subscriptionCardLineHeight};
`;

export const LabelNode = styled.View`
  flex-shrink: 1;
  margin: auto 0;
  margin-left: 16px;
  font-size: ${subscriptionCardFontSize};
  line-height: ${subscriptionCardLineHeight};
`;
