import React, { ReactNode, useEffect, useState } from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import { useTheme } from 'styled-components/native';

import { ErrorLabel, Tick } from '../../Atoms';
import * as S from './CheckBoxInput.styles';

interface IProps {
  label?: string | ReactNode;
  defaultChecked?: boolean;
  onPress?: Function;
  error?: boolean;
  color?: string;
  errorMessage?: string;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
  type?: 'primary' | 'secondary';
  noBorderRadius?: boolean;
  alignCenter?: boolean;
}

const CheckBoxInput = ({
  label,
  defaultChecked,
  onPress,
  error,
  errorMessage,
  accessibilityLabel,
  accessibilityHint,
  testID,
  color = '',
  type = 'primary',
  noBorderRadius = false,
  alignCenter = false,
}: IProps) => {
  const theme = useTheme();
  const [checked, setChecked] = useState(false);

  useEffect(() => {
    if (defaultChecked !== undefined) {
      setChecked(defaultChecked);
    }
  }, [defaultChecked]);

  const onChecked = () => {
    const newChecked = !checked;
    setChecked(newChecked);

    if (onPress) {
      onPress(newChecked);
    }
  };
  return (
    <S.ViewContainer checked={checked} testID="CheckBoxInput">
      <S.CheckBoxInputContainer alignCenter={alignCenter}>
        <TouchableWithoutFeedback
          testID={testID}
          onPress={onChecked}
          accessibilityRole="checkbox"
          accessibilityLabel={accessibilityLabel}
          accessibilityHint={accessibilityHint}
        >
          <S.CheckBox
            type={type}
            checked={checked}
            testID="CheckBox"
            noBorderRadius={noBorderRadius}
          >
            {checked && (
              <S.Check>
                <Tick
                  width="100%"
                  height="100%"
                  color={theme.subscriptionMfe.checkBox[type].tick}
                />
              </S.Check>
            )}
          </S.CheckBox>
        </TouchableWithoutFeedback>
        {label && typeof label === 'string' ? (
          <S.Label color={color} onPress={onChecked}>
            {label}
          </S.Label>
        ) : (
          <S.LabelNode>{label}</S.LabelNode>
        )}
      </S.CheckBoxInputContainer>
      {error && errorMessage && <ErrorLabel>{errorMessage}</ErrorLabel>}
    </S.ViewContainer>
  );
};

export default CheckBoxInput;
