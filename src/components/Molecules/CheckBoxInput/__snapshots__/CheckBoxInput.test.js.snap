// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CheckBoxInput component renders checked 1`] = `
[
  <View
    checked={true}
    style={
      {
        "flexDirection": "column",
      }
    }
    testID="CheckBoxInput"
  >
    <View
      alignCenter={false}
      style={
        {
          "alignItems": "start",
          "flexBasis": "auto",
          "flexDirection": "row",
        }
      }
    >
      <View
        accessibilityRole="checkbox"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessible={true}
        checked={true}
        focusable={true}
        noBorderRadius={false}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 4,
            "borderBottomRightRadius": 4,
            "borderColor": "#000096",
            "borderStyle": "solid",
            "borderTopLeftRadius": 4,
            "borderTopRightRadius": 4,
            "borderWidth": 1,
            "flexBasis": 24,
            "height": 24,
            "width": 24,
          }
        }
        testID="CheckBox"
        type="primary"
      >
        <View
          style={
            {
              "paddingBottom": "12.5%",
              "paddingLeft": "12.5%",
              "paddingRight": "12.5%",
              "paddingTop": "12.5%",
            }
          }
        >
          <RNSVGSvgView
            align="xMidYMid"
            bbHeight="100%"
            bbWidth="100%"
            focusable={false}
            height="100%"
            meetOrSlice={0}
            minX={0}
            minY={0}
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": "100%",
                  "width": "100%",
                },
              ]
            }
            vbHeight={15}
            vbWidth={18}
            width="100%"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                name="illustarations-and-Icons"
                propList={
                  [
                    "fill",
                    "stroke",
                  ]
                }
                stroke={null}
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      -591,
                      -2951,
                    ]
                  }
                  name="Icons"
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        588,
                        2946,
                      ]
                    }
                    name="atom-/-icon-/-line-/-custom-/-tick"
                  >
                    <RNSVGPath
                      d="M19.603688,5.69514462 C19.772055,5.47626755 20.0859783,5.43532103 20.3048554,5.60368801 C20.4994128,5.75334754 20.5533831,6.01801456 20.444725,6.22870617 L20.396312,6.30485538 L10.396312,19.3048554 C10.2327348,19.5175057 9.93610389,19.559217 9.72231328,19.4159688 L9.64644661,19.3535534 L3.64644661,13.3535534 C3.45118446,13.1582912 3.45118446,12.8417088 3.64644661,12.6464466 C3.82001296,12.4728803 4.08943736,12.4535951 4.2843055,12.5885912 L4.35355339,12.6464466 L9.95,18.243 L19.603688,5.69514462 Z"
                      fill={
                        {
                          "payload": 4278190230,
                          "type": 0,
                        }
                      }
                      name="Path-21"
                      propList={
                        [
                          "fill",
                        ]
                      }
                    />
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
      </View>
      <View
        style={
          {
            "flexShrink": 1,
            "fontSize": 14,
            "lineHeight": 20,
            "marginBottom": "auto",
            "marginLeft": 16,
            "marginRight": 0,
            "marginTop": "auto",
          }
        }
      />
    </View>
  </View>,
  ",",
]
`;

exports[`CheckBoxInput component renders default 1`] = `
[
  <View
    checked={false}
    style={
      {
        "flexDirection": "column",
      }
    }
    testID="CheckBoxInput"
  >
    <View
      alignCenter={false}
      style={
        {
          "alignItems": "start",
          "flexBasis": "auto",
          "flexDirection": "row",
        }
      }
    >
      <View
        accessibilityRole="checkbox"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessible={true}
        checked={false}
        focusable={true}
        noBorderRadius={false}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 4,
            "borderBottomRightRadius": 4,
            "borderColor": "#DEDEDE",
            "borderStyle": "solid",
            "borderTopLeftRadius": 4,
            "borderTopRightRadius": 4,
            "borderWidth": 1,
            "flexBasis": 24,
            "height": 24,
            "width": 24,
          }
        }
        testID="CheckBox"
        type="primary"
      />
      <View
        style={
          {
            "flexShrink": 1,
            "fontSize": 14,
            "lineHeight": 20,
            "marginBottom": "auto",
            "marginLeft": 16,
            "marginRight": 0,
            "marginTop": "auto",
          }
        }
      />
    </View>
  </View>,
  ",",
]
`;

exports[`CheckBoxInput component renders with error 1`] = `
[
  <View
    checked={false}
    style={
      {
        "flexDirection": "column",
      }
    }
    testID="CheckBoxInput"
  >
    <View
      alignCenter={false}
      style={
        {
          "alignItems": "start",
          "flexBasis": "auto",
          "flexDirection": "row",
        }
      }
    >
      <View
        accessibilityRole="checkbox"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessible={true}
        checked={false}
        focusable={true}
        noBorderRadius={false}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 4,
            "borderBottomRightRadius": 4,
            "borderColor": "#DEDEDE",
            "borderStyle": "solid",
            "borderTopLeftRadius": 4,
            "borderTopRightRadius": 4,
            "borderWidth": 1,
            "flexBasis": 24,
            "height": 24,
            "width": 24,
          }
        }
        testID="CheckBox"
        type="primary"
      />
      <View
        style={
          {
            "flexShrink": 1,
            "fontSize": 14,
            "lineHeight": 20,
            "marginBottom": "auto",
            "marginLeft": 16,
            "marginRight": 0,
            "marginTop": "auto",
          }
        }
      />
    </View>
    <View
      justify="flex-start"
      style={
        {
          "flexDirection": "row",
          "justifyContent": "flex-start",
          "paddingTop": 7,
        }
      }
    >
      <Text
        status="error"
        style={
          {
            "color": "#CF0001",
            "fontSize": 11,
            "letterSpacing": 0.7,
            "lineHeight": 16,
          }
        }
      >
        Error message
      </Text>
    </View>
  </View>,
  ",",
]
`;

exports[`CheckBoxInput component renders with label 1`] = `
[
  <View
    checked={false}
    style={
      {
        "flexDirection": "column",
      }
    }
    testID="CheckBoxInput"
  >
    <View
      alignCenter={false}
      style={
        {
          "alignItems": "start",
          "flexBasis": "auto",
          "flexDirection": "row",
        }
      }
    >
      <View
        accessibilityRole="checkbox"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": undefined,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessible={true}
        checked={false}
        focusable={true}
        noBorderRadius={false}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderBottomLeftRadius": 4,
            "borderBottomRightRadius": 4,
            "borderColor": "#DEDEDE",
            "borderStyle": "solid",
            "borderTopLeftRadius": 4,
            "borderTopRightRadius": 4,
            "borderWidth": 1,
            "flexBasis": 24,
            "height": 24,
            "width": 24,
          }
        }
        testID="CheckBox"
        type="primary"
      />
      <Text
        color=""
        onPress={[Function]}
        style={
          {
            "color": "rgba(17, 17, 17, 0.7)",
            "flexShrink": 1,
            "fontSize": 14,
            "letterSpacing": 0.6,
            "lineHeight": 20,
            "marginBottom": "auto",
            "marginLeft": 16,
            "marginRight": 0,
            "marginTop": "auto",
          }
        }
      >
        Test label
      </Text>
    </View>
  </View>,
  ",",
]
`;
