import { styled } from 'styled-components/native';

export const ScrollContainer = styled.ScrollView.attrs({
  contentContainerStyle: {
    flexGrow: 1,
  },
  alwaysBounceVertical: false,
})``;

export const Title = styled.Text`
  font-size: 28px;
  font-weight: normal;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-align: center;
  color: ${({ theme }) => theme.subscriptionMfe.color.grey.thirteen};
  margin-bottom: 16px;
  margin-horizontal: 24px;
`;

export const ImageContainer = styled.View`
  margin-vertical: 5%;
  align-items: center;
`;

export const Subtitle = styled.Text`
  font-size: 18px;
  line-height: 32px;
  letter-spacing: 0.15px;
  margin-horizontal: 24px;
  color: ${({ theme }) => theme.subscriptionMfe.color.grey.thirteen};
  text-align: center;
`;

type ChildrenWrapperProps = {
  bottomInset: number;
};

export const ChildrenWrapper = styled.View<ChildrenWrapperProps>`
  margin-horizontal: 24px;
  padding-bottom: ${({ bottomInset }) => bottomInset + 24}px;
`;
