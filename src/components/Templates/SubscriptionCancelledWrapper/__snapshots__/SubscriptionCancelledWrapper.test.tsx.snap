// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SubscriptionCancelledWrapper /> should correctly render component 1`] = `
[
  <RCTScrollView
    alwaysBounceVertical={false}
    contentContainerStyle={
      {
        "flexGrow": 1,
      }
    }
    style={{}}
    testID="test-screen-wrapper-container"
  >
    <View>
      <Text
        style={
          {
            "color": "#111111",
            "fontSize": 28,
            "fontWeight": "normal",
            "letterSpacing": 0.5,
            "lineHeight": 40,
            "marginBottom": 16,
            "marginHorizontal": 24,
            "textAlign": "center",
          }
        }
        testID="test-screen-wrapper-title"
      >
        Title
      </Text>
      <View
        style={
          {
            "alignItems": "center",
            "marginVertical": "5%",
          }
        }
        testID="test-screen-wrapper-image-container"
      >
        <SvgMock />
      </View>
      <Text
        numberOfLines={5}
        style={
          {
            "color": "#111111",
            "fontSize": 18,
            "letterSpacing": 0.15,
            "lineHeight": 32,
            "marginHorizontal": 24,
            "textAlign": "center",
          }
        }
        testID="test-screen-wrapper-subtitle"
      >
        Subtitle
      </Text>
    </View>
  </RCTScrollView>,
  <View
    bottomInset={0}
    style={
      {
        "marginHorizontal": 24,
        "paddingBottom": 24,
      }
    }
    testID="test-screen-wrapper-children-wrapper"
  >
    <View>
      <Text>
        Test
      </Text>
    </View>
  </View>,
]
`;
