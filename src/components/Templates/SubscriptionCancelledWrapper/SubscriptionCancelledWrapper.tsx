import React, { PropsWithChildren } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import * as S from './SubscriptionCancelledWrapper.styles';

type Props = PropsWithChildren<{
  title: string;
  subtitle: string;
  image: React.ReactNode;
  testId: string;
}>;

const SubscriptionCancelledWrapper: React.FC<Props> = ({
  title,
  subtitle,
  testId,
  image,
  children,
}) => {
  const insets = useSafeAreaInsets();

  return (
    <>
      <S.ScrollContainer testID={`${testId}-wrapper-container`}>
        <S.Title testID={`${testId}-wrapper-title`}>{title}</S.Title>
        <S.ImageContainer testID={`${testId}-wrapper-image-container`}>
          {image}
        </S.ImageContainer>
        <S.Subtitle numberOfLines={5} testID={`${testId}-wrapper-subtitle`}>
          {subtitle}
        </S.Subtitle>
      </S.ScrollContainer>
      <S.ChildrenWrapper
        bottomInset={insets.bottom}
        testID={`${testId}-wrapper-children-wrapper`}
      >
        {children}
      </S.ChildrenWrapper>
    </>
  );
};

export default SubscriptionCancelledWrapper;
