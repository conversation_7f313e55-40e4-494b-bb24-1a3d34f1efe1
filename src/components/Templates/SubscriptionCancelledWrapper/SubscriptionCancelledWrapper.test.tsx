/* eslint-disable @calm/react-intl/missing-formatted-message */
import { render } from '@testing-library/react-native';
import React from 'react';
import { Text, View } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import CalendarConfirmed from '../../../../assets/svg/calendarConfirmed.svg';
import Theme from '../../../themes/Theme';
import SubscriptionCancelledWrapper from './SubscriptionCancelledWrapper';

const TEST_ID = 'test-screen';

const renderComponent = () =>
  render(
    <Theme>
      <SafeAreaProvider>
        <SubscriptionCancelledWrapper
          title="Title"
          subtitle="Subtitle"
          image={<CalendarConfirmed />}
          testId={TEST_ID}
        >
          <View>
            <Text>Test</Text>
          </View>
        </SubscriptionCancelledWrapper>
      </SafeAreaProvider>
    </Theme>,
  );

const MAIN_WRAPPER = `${TEST_ID}-wrapper-container`;
const TITLE_WRAPPER = `${TEST_ID}-wrapper-title`;
const IMAGE_CONTAINER = `${TEST_ID}-wrapper-image-container`;
const SUBTITLE_CONTAINER = `${TEST_ID}-wrapper-subtitle`;
const CHILDREN_WRAPPER = `${TEST_ID}-wrapper-children-wrapper`;

describe('<SubscriptionCancelledWrapper />', () => {
  it('should correctly render component', () => {
    const { getByTestId, toJSON } = renderComponent();

    expect(getByTestId(MAIN_WRAPPER)).toBeDefined();
    expect(getByTestId(TITLE_WRAPPER)).toBeDefined();
    expect(getByTestId(IMAGE_CONTAINER)).toBeDefined();
    expect(getByTestId(SUBTITLE_CONTAINER)).toBeDefined();
    expect(getByTestId(CHILDREN_WRAPPER)).toBeDefined();

    expect(toJSON()).toMatchSnapshot();
  });
});
