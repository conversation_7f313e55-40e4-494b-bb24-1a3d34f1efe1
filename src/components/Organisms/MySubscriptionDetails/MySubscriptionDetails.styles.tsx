import { styled } from 'styled-components/native';

interface NormalTextProps {
  needsPadding?: boolean;
  needsPaddingBottom?: boolean;
}

export const Wrapper = styled.View``;

export const NormalText = styled.Text<NormalTextProps>`
  padding-left: ${({ needsPadding }) => (needsPadding ? '24px' : '0')};
  font-size: 16px;
  line-height: 28px;
  font-weight: 400;
  color: ${({ theme }) => theme.subscriptionMfe.gocardless.text.banner};
  padding-bottom: ${({ needsPadding, needsPaddingBottom }) =>
    needsPadding || needsPaddingBottom ? '8px' : '0'};
`;

export const InformationsContainer = styled.View`
  min-height: 221px;
  padding: 16px 24px;
  border: 1px solid #dedede;
  border-left-width: 0;
  border-right-width: 0;
`;

export const LoadingIndicatorContainer = styled.View`
  flex: 1;
  align-items: center;
  justify-content: center;
`;

export const TextWrapper = styled.View`
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  padding-bottom: 8px;
`;

export const SmallText = styled.Text<{ bold?: boolean }>`
  font-size: 13px;
  line-height: 23px;
  font-weight: ${({ bold }) => (bold ? '700' : '400')};
  color: ${({ theme }) => theme.subscriptionMfe.gocardless.text.banner};
`;
