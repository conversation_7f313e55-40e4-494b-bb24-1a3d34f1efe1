import { LoadingIndicator } from '@bp/ui-components/mobile';
import React from 'react';

import { ISubscriptionDetails } from '../../../common/interfaces';
import { useSettings } from '../../../providers/Settings';
import { WalletSubscriptionData } from '../../../providers/WalletSubscriptionDataProvider';
import {
  formatCurrency,
  formatDate,
  getCurrency,
  getOrdinalDay,
  processBillingItems,
} from '../../../utils/helpers';
import * as S from './MySubscriptionDetails.styles';

export const MySubscriptionDetails = ({
  isMySubscription = false,
  userCountry,
}: ISubscriptionDetails) => {
  const { walletSubscriptionData } = WalletSubscriptionData();
  const { t: translations } = useSettings();

  const subscriptionAmount = processBillingItems(
    walletSubscriptionData?.getWalletSubscription.billingItems,
  );

  return (
    <S.Wrapper>
      <S.NormalText needsPadding>
        {translations.mySubscription.SUBSCRIPTION_DETAILS.TITLE}
      </S.NormalText>
      <S.InformationsContainer>
        {walletSubscriptionData ? (
          <>
            <S.TextWrapper testID="my-subscription-details-start-date-row">
              <S.SmallText>
                {translations.mySubscription.SUBSCRIPTION_DETAILS.START_DATE}
              </S.SmallText>
              <S.SmallText>
                {formatDate(
                  walletSubscriptionData?.getWalletSubscription.createdAt,
                )}
              </S.SmallText>
            </S.TextWrapper>
            <S.TextWrapper>
              <S.SmallText>
                {translations.mySubscription.SUBSCRIPTION_DETAILS.BILLING_DATE}
              </S.SmallText>
              <S.SmallText>
                {getOrdinalDay(
                  walletSubscriptionData?.getWalletSubscription.nextBillingDate,
                )}
              </S.SmallText>
            </S.TextWrapper>
            <S.TextWrapper>
              <S.SmallText>
                {
                  translations.mySubscription.SUBSCRIPTION_DETAILS
                    .BILLING_SCHEDULE
                }
              </S.SmallText>
              <S.SmallText>{'Monthly'}</S.SmallText>
            </S.TextWrapper>
            <S.TextWrapper>
              <S.SmallText>
                {isMySubscription
                  ? translations.mySubscription.SUBSCRIPTION_DETAILS
                      .EXCLUDE_DISCOUNT
                  : translations.mySubscription.SUBSCRIPTION_DETAILS
                      .SUBSCRIPTION}
              </S.SmallText>
              <S.SmallText>
                {formatCurrency({
                  amount: subscriptionAmount.amount,
                  currency: getCurrency(userCountry),
                })}
              </S.SmallText>
            </S.TextWrapper>
            {isMySubscription && subscriptionAmount.discount !== 0 && (
              <S.TextWrapper>
                <S.SmallText>
                  {translations.mySubscription.SUBSCRIPTION_DETAILS.DISCOUNT}
                </S.SmallText>
                <S.SmallText>
                  {formatCurrency({
                    amount: subscriptionAmount?.discount,
                    currency: getCurrency(userCountry),
                  })}
                </S.SmallText>
              </S.TextWrapper>
            )}
            <S.TextWrapper>
              <S.SmallText bold>
                {translations.mySubscription.SUBSCRIPTION_DETAILS.TOTAL}
              </S.SmallText>
              <S.SmallText bold>
                {formatCurrency({
                  amount: subscriptionAmount.totalAmount,
                  currency: getCurrency(userCountry),
                })}
              </S.SmallText>
            </S.TextWrapper>
          </>
        ) : (
          <S.LoadingIndicatorContainer>
            <LoadingIndicator color="black" />
          </S.LoadingIndicatorContainer>
        )}
      </S.InformationsContainer>
    </S.Wrapper>
  );
};
