import { render } from '@testing-library/react-native';
import React from 'react';

import { UserCountry } from '../../../common/enums';
import { ISubscriptionDetails } from '../../../common/interfaces';
import Theme from '../../../themes/Theme';
import TRANSLATIONS from '../../../translations/messages.json';
import * as helpers from '../../../utils/helpers';
import { rawData } from '../../../utils/mockData';
import { MySubscriptionDetails } from './MySubscriptionDetails';

const mockFormatDate = jest.spyOn(helpers, 'formatDate');

const mockTranslations = TRANSLATIONS;
const mockUseSettingsObj = {
  t: mockTranslations,
  isInternetReachable: true,
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);

jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const START_DATE_ROW = 'my-subscription-details-start-date-row';

const mockWalletSubscriptionObj = {
  walletSubscriptionData: rawData,
  subscription: {
    billingItemId: '651dce90-2497-4a13-9503-e16cccbb375b',
    planId: 'ac7e03e1-0da2-4515-8412-7ec556f32b8d',
    name: 'Plan A - September 14',
    amount: '0',
    currency: 'GBP',
  },
  totalValue: 7.99,
  currency: 'GBP',
};

const mockWalletSubscriptionData = jest
  .fn()
  .mockReturnValue(mockWalletSubscriptionObj);
jest.mock('../../../providers/WalletSubscriptionDataProvider', () => ({
  WalletSubscriptionData: () => mockWalletSubscriptionData(),
}));

const renderComponent = ({
  isMySubscription = true,
  userCountry = UserCountry.UK,
}: Partial<Pick<ISubscriptionDetails, 'userCountry' | 'isMySubscription'>>) =>
  render(
    <Theme>
      <MySubscriptionDetails
        isMySubscription={isMySubscription}
        userCountry={userCountry}
      />
    </Theme>,
  );

describe('SubscriptionDetails rendering', () => {
  beforeEach(() => mockFormatDate.mockClear());

  it('Should render correctly', () => {
    const { toJSON } = renderComponent({
      isMySubscription: false,
    });

    expect(toJSON()).toMatchSnapshot();
  });

  it('Should render discount text when the user has a subscription', () => {
    const { getByText } = renderComponent({});

    expect(
      getByText(mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.DISCOUNT),
    ).toBeDefined();
  });

  it('Should render loading indicator until subscription is fetched', () => {
    mockWalletSubscriptionData.mockResolvedValueOnce({});
    const { getByTestId } = renderComponent({});

    expect(getByTestId('loadingIndicator')).toBeDefined();
  });

  describe('Start Date Row', () => {
    it('should correctly render row', () => {
      renderComponent({});

      expect(mockFormatDate).toHaveBeenCalledTimes(1);
      expect(mockFormatDate).toHaveBeenNthCalledWith(
        1,
        '2023-07-03T14:41:31.711Z',
      );
      expect(mockFormatDate).toHaveNthReturnedWith(1, '03/07/2023');
    });

    it('should trigger formatDate when rendering row', () => {
      const { getByTestId } = renderComponent({});

      expect(getByTestId(START_DATE_ROW).props).toMatchSnapshot();
    });
  });
});
