// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SubscriptionDetails rendering Should render correctly 1`] = `
<View
  style={{}}
>
  <Text
    needsPadding={true}
    style={
      {
        "color": "#212121",
        "fontSize": 16,
        "fontWeight": "400",
        "lineHeight": 28,
        "paddingBottom": 8,
        "paddingLeft": 24,
      }
    }
  >
    Subscription details
  </Text>
  <View
    style={
      {
        "borderColor": "#dedede",
        "borderLeftWidth": 0,
        "borderRightWidth": 0,
        "borderStyle": "solid",
        "borderWidth": 1,
        "minHeight": 221,
        "paddingBottom": 16,
        "paddingLeft": 24,
        "paddingRight": 24,
        "paddingTop": 16,
      }
    }
  >
    <View
      style={
        {
          "display": "flex",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingBottom": 8,
        }
      }
      testID="my-subscription-details-start-date-row"
    >
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
          }
        }
      >
        Start date
      </Text>
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
          }
        }
      >
        03/07/2023
      </Text>
    </View>
    <View
      style={
        {
          "display": "flex",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingBottom": 8,
        }
      }
    >
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
          }
        }
      >
        Billing date
      </Text>
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
          }
        }
      >
        3rd
      </Text>
    </View>
    <View
      style={
        {
          "display": "flex",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingBottom": 8,
        }
      }
    >
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
          }
        }
      >
        Billing schedule
      </Text>
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
          }
        }
      >
        Monthly
      </Text>
    </View>
    <View
      style={
        {
          "display": "flex",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingBottom": 8,
        }
      }
    >
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
          }
        }
      >
        Subscription
      </Text>
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
          }
        }
      >
        £8.00
      </Text>
    </View>
    <View
      style={
        {
          "display": "flex",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingBottom": 8,
        }
      }
    >
      <Text
        bold={true}
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "700",
            "lineHeight": 23,
          }
        }
      >
        Total
      </Text>
      <Text
        bold={true}
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "700",
            "lineHeight": 23,
          }
        }
      >
        £7.00
      </Text>
    </View>
  </View>
</View>
`;

exports[`SubscriptionDetails rendering Start Date Row should trigger formatDate when rendering row 1`] = `
{
  "children": [
    <Styled(Text)>
      Start date
    </Styled(Text)>,
    <Styled(Text)>
      03/07/2023
    </Styled(Text)>,
  ],
  "style": {
    "display": "flex",
    "flexDirection": "row",
    "justifyContent": "space-between",
    "paddingBottom": 8,
  },
  "testID": "my-subscription-details-start-date-row",
}
`;
