import { Spacer } from '@bp/ui-components/mobile/core';
import React from 'react';

import { UserTypeEnum } from '../../../common/enums';
import { ICancelSubscriptionContent } from '../../../common/interfaces';
import { useSettings } from '../../../providers/Settings';
import { formatCurrency, getCurrency } from '../../../utils/helpers';
import {
  ChargingOnGoIcon,
  PolarPayAsUGoIcon,
  PolarPomIcon,
  PolarRFIDIcon,
  Tablerow,
} from '../../Atoms';
import { RowWithIcon } from '../../Molecules/RowWithIcon/RowWithIcon';
import { cancelSubscriptionData } from './CancelSubscriptionContent.functions';
import * as S from './CancelSubscriptionContent.styles';

export const CancelSubscriptionContent = ({
  userType,
  theme,
  userCountry,
}: ICancelSubscriptionContent) => {
  const { t: translations, featureFlags, locale } = useSettings();
  const subsChargingRatesPromo = featureFlags.subsChargingRatesPromo;
  return userType === UserTypeEnum.SUBS_WALLET ? (
    <S.FlexWrapper>
      <S.Title>
        {translations.cancelSubscription.SUBTITLE.replace(
          '{{percentageDiscount}}',
          featureFlags.introPercentageDiscount.toString(),
        )}
      </S.Title>
      {cancelSubscriptionData({
        theme,
        translations,
        subsChargingRatesPromo,
        userCountry,
        percentageDiscount: featureFlags.introPercentageDiscount.toString(),
        locale,
      }).map(RowWithIcon)}
    </S.FlexWrapper>
  ) : (
    <S.Wrapper>
      <S.TopViewContainer>
        <Spacer vSpace={24} />
        <S.BannerText>
          {translations.gocardless.text.CANCEL_SUBS_TITLE}
        </S.BannerText>
        <Spacer vSpace={18} />
        <S.SubTitle>
          {translations.gocardless.text.CANCEL_SUBS_SUBTITLE}
        </S.SubTitle>
        <Spacer vSpace={18} />
        <S.FreeText>
          {translations.gocardless.text.CANCEL_SUBS_FREETEXT}
        </S.FreeText>
        <Spacer vSpace={18} />
      </S.TopViewContainer>
      <S.MiddleViewContainer>
        <Tablerow
          text={translations.gocardless.text.RFID_WILL_BE_DISABLED}
          icon={
            <PolarRFIDIcon color={theme.subscriptionMfe?.text.color.tertiary} />
          }
          hasSeperator={false}
        />
        <Tablerow
          text={translations.gocardless.text.PULSE_WILL_BE_DISABLED}
          icon={
            <ChargingOnGoIcon
              color={theme.subscriptionMfe?.text.color.tertiary}
            />
          }
          hasSeperator={false}
        />
        <Tablerow
          text={translations.gocardless.text.LOSE_REWARDS.replace(
            '{{rate}}',
            formatCurrency({
              amount: 0.35,
              currency: getCurrency(userCountry),
            }),
          )}
          icon={
            <PolarPomIcon color={theme.subscriptionMfe?.text.color.tertiary} />
          }
          hasSeperator={false}
        />
        <Tablerow
          text={translations.gocardless.text.FINAL_PAYMENT}
          icon={
            <PolarPayAsUGoIcon
              color={theme.subscriptionMfe?.text.color.tertiary}
            />
          }
          hasSeperator={false}
        />
      </S.MiddleViewContainer>
    </S.Wrapper>
  );
};
