import { Dimensions } from 'react-native';
import { styled } from 'styled-components/native';

const phoneWidth = Dimensions.get('screen').width > 320 ? 24 : 8;

export const Wrapper = styled.View``;

export const FlexWrapper = styled.View`
  margin-top: 16px;
`;

export const TopViewContainer = styled.View`
  padding-right: ${phoneWidth}px;
  padding-left: ${phoneWidth}px;
`;

export const BannerText = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.grey.one};
  font-size: 20px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
  padding-right: ${phoneWidth}px;
  padding-left: ${phoneWidth}px;
`;

export const SubTitle = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.black2};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const FreeText = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.black2};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: left;
`;

export const MiddleViewContainer = styled.View`
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding-right: 16px;
`;

export const Title = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.black2};
  font-size: 16px;
  letter-spacing: 0.1px;
  line-height: 28px;

  padding: 0 24px 16px 24px;
`;
