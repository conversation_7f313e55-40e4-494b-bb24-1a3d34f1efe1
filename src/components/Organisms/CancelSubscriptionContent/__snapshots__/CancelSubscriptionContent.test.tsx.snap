// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CancelSubscriptionContent rendering Should render correctly 1`] = `
<View
  style={{}}
>
  <View
    style={
      {
        "paddingLeft": 24,
        "paddingRight": 24,
      }
    }
  >
    <View
      style={
        {
          "height": 24,
          "width": 1,
        }
      }
    />
    <Text
      style={
        {
          "color": "#212121",
          "fontSize": 20,
          "letterSpacing": 0,
          "lineHeight": 24,
          "paddingLeft": 24,
          "paddingRight": 24,
          "textAlign": "center",
        }
      }
    >
      Thinking of leaving?
    </Text>
    <View
      style={
        {
          "height": 18,
          "width": 1,
        }
      }
    />
    <Text
      style={
        {
          "color": "#1D1D26",
          "fontSize": 16,
          "letterSpacing": 0,
          "lineHeight": 24,
          "textAlign": "center",
        }
      }
    >
      You’ll miss out on your exclusive access to the bp pulse network.
    </Text>
    <View
      style={
        {
          "height": 18,
          "width": 1,
        }
      }
    />
    <Text
      style={
        {
          "color": "#1D1D26",
          "fontSize": 16,
          "letterSpacing": 0,
          "lineHeight": 24,
          "textAlign": "left",
        }
      }
    >
      If you cancel...
    </Text>
    <View
      style={
        {
          "height": 18,
          "width": 1,
        }
      }
    />
  </View>
  <View
    style={
      {
        "alignItems": "center",
        "flexWrap": "wrap",
        "justifyContent": "center",
        "paddingRight": 16,
      }
    }
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "borderBottomWidth": 0,
          "borderColor": "#D8D8D8",
          "borderTopWidth": 0,
          "flexDirection": "row",
          "justifyContent": "space-between",
          "opacity": 1,
          "paddingBottom": 16,
          "paddingTop": 16,
        }
      }
    >
      <View
        style={
          {
            "alignItems": "center",
            "flexDirection": "row",
            "flexShrink": 1,
            "justifyContent": "flex-start",
            "marginBottom": 0,
          }
        }
        type="primary"
      >
        <View
          style={
            {
              "alignItems": "center",
              "justifyContent": "center",
              "paddingLeft": 32,
              "paddingTop": 0,
              "width": 20,
            }
          }
          type="primary"
        >
          <RNSVGSvgView
            align="xMidYMid"
            bbHeight="20"
            bbWidth="20"
            focusable={false}
            height="20"
            meetOrSlice={0}
            minX={0}
            minY={0}
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 20,
                  "width": 20,
                },
              ]
            }
            vbHeight={20}
            vbWidth={20}
            width="20"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                propList={
                  [
                    "fill",
                    "stroke",
                  ]
                }
                stroke={null}
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      -590,
                      -1556,
                    ]
                  }
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        588,
                        1554,
                      ]
                    }
                  >
                    <RNSVGPath
                      d="M14,6 C14.2761424,6 14.5,6.22385763 14.5,6.5 C14.5,6.74545989 14.3231248,6.94960837 14.0898756,6.99194433 L14,7 L4.5,7 C3.72030388,7 3.07955132,7.59488808 3.00686658,8.35553999 L3,8.5 L3,19.5 C3,20.2796961 3.59488808,20.9204487 4.35553999,20.9931334 L4.5,21 L15.5,21 C16.2796961,21 16.9204487,20.4051119 16.9931334,19.64446 L17,19.5 L17,10 C17,9.72385763 17.2238576,9.5 17.5,9.5 C17.7454599,9.5 17.9496084,9.67687516 17.9919443,9.91012437 L18,10 L18,19.5 C18,20.8254834 16.9684641,21.9100387 15.6643757,21.9946823 L15.5,22 L4.5,22 C3.1745166,22 2.08996133,20.9684641 2.00531768,19.6643757 L2,19.5 L2,8.5 C2,7.1745166 3.03153594,6.08996133 4.33562431,6.00531768 L4.5,6 L14,6 Z M7.5,10.5 C8.88071187,10.5 10,11.6192881 10,13 C10,14.0880385 9.30493675,15.0137363 8.33461011,15.3572936 L9.9472136,17.7763932 C10.0707082,18.0233825 9.97059605,18.323719 9.7236068,18.4472136 C9.5040608,18.5569866 9.24236387,18.4900826 9.10018522,18.3003914 L9.0527864,18.2236068 L7.0527864,15.2236068 C6.92929178,14.9766175 7.02940395,14.676281 7.2763932,14.5527864 C7.32447207,14.528747 7.3745724,14.5131805 7.42499629,14.5055211 L7.5,14.5 C8.32842712,14.5 9,13.8284271 9,13 C9,12.2203039 8.40511192,11.5795513 7.64446001,11.5068666 L7.5,11.5 L6.5,11.5 C6.25454011,11.5 6.05039163,11.6768752 6.00805567,11.9101244 L6,12 L6,18 C6,18.2761424 5.77614237,18.5 5.5,18.5 C5.25454011,18.5 5.05039163,18.3231248 5.00805567,18.0898756 L5,18 L5,12 C5,11.2203039 5.59488808,10.5795513 6.35553999,10.5068666 L6.5,10.5 L7.5,10.5 Z M15,10.5 C15.2761424,10.5 15.5,10.7238576 15.5,11 C15.5,11.2454599 15.3231248,11.4496084 15.0898756,11.4919443 L15,11.5 L13,11.5 C12.7545401,11.5 12.5503916,11.6768752 12.5080557,11.9101244 L12.5,12 L12.5,13.5 L14,13.5 C14.2761424,13.5 14.5,13.7238576 14.5,14 C14.5,14.2454599 14.3231248,14.4496084 14.0898756,14.4919443 L14,14.5 L12.5,14.5 L12.5,18 C12.5,18.2761424 12.2761424,18.5 12,18.5 C11.7545401,18.5 11.5503916,18.3231248 11.5080557,18.0898756 L11.5,18 L11.5,12 C11.5,11.2203039 12.0948881,10.5795513 12.85554,10.5068666 L13,10.5 L15,10.5 Z M20.6819805,3.31801948 C22.4393398,5.0753788 22.4393398,7.9246212 20.6819805,9.68198052 C20.4867184,9.87724266 20.1701359,9.87724266 19.9748737,9.68198052 C19.7796116,9.48671837 19.7796116,9.17013588 19.9748737,8.97487373 C21.3417088,7.60803871 21.3417088,5.39196129 19.9748737,4.02512627 C18.6080387,2.65829124 16.3919613,2.65829124 15.0251263,4.02512627 C14.8298641,4.22038841 14.5132816,4.22038841 14.3180195,4.02512627 C14.1227573,3.82986412 14.1227573,3.51328163 14.3180195,3.31801948 C16.0753788,1.56066017 18.9246212,1.56066017 20.6819805,3.31801948 Z M17.5,5 C18.3284271,5 19,5.67157288 19,6.5 C19,7.32842712 18.3284271,8 17.5,8 C16.6715729,8 16,7.32842712 16,6.5 C16,5.67157288 16.6715729,5 17.5,5 Z M17.5,6 C17.2238576,6 17,6.22385763 17,6.5 C17,6.77614237 17.2238576,7 17.5,7 C17.7761424,7 18,6.77614237 18,6.5 C18,6.22385763 17.7761424,6 17.5,6 Z"
                      fill={
                        {
                          "payload": 4278190230,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "fill",
                        ]
                      }
                    />
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
        <View
          style={
            {
              "flexDirection": "column",
              "flexShrink": 1,
              "justifyContent": "center",
              "paddingLeft": 28,
              "paddingRight": 8,
            }
          }
        >
          <Text
            ellipsizeMode="tail"
            style={
              {
                "alignItems": "flex-start",
                "color": "#1D1D26",
                "fontSize": 16,
                "textAlign": "left",
              }
            }
            textColor="#1D1D26"
          >
            Your access card or key fob will no longer be active for use at pulse points.
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "height": 30,
            "justifyContent": "center",
            "paddingBottom": 32,
            "transform": [
              {
                "rotate": "-90deg",
              },
            ],
            "width": 30,
          }
        }
      />
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "borderBottomWidth": 0,
          "borderColor": "#D8D8D8",
          "borderTopWidth": 0,
          "flexDirection": "row",
          "justifyContent": "space-between",
          "opacity": 1,
          "paddingBottom": 16,
          "paddingTop": 16,
        }
      }
    >
      <View
        style={
          {
            "alignItems": "center",
            "flexDirection": "row",
            "flexShrink": 1,
            "justifyContent": "flex-start",
            "marginBottom": 0,
          }
        }
        type="primary"
      >
        <View
          style={
            {
              "alignItems": "center",
              "justifyContent": "center",
              "paddingLeft": 32,
              "paddingTop": 0,
              "width": 20,
            }
          }
          type="primary"
        >
          <RNSVGSvgView
            bbHeight="24"
            bbWidth="24"
            focusable={false}
            height="24"
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 24,
                  "width": 24,
                },
              ]
            }
            width="24"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                propList={
                  [
                    "fill",
                    "stroke",
                  ]
                }
                stroke={null}
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      -590,
                      -644,
                    ]
                  }
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        588,
                        642,
                      ]
                    }
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          2,
                          2,
                        ]
                      }
                    >
                      <RNSVGPath
                        d="M15.0658784,0.251930531 C15.3381714,-0.224582136 16.0681486,0.0220722076 15.995614,0.56608186 L15.995614,0.56608186 L15.07,7.5 L19.5018094,7.5 C19.8484189,7.5 20.080858,7.83950965 19.9779221,8.15356538 L19.9779221,8.15356538 L19.9450732,8.23133782 L13.9432638,19.7313378 C13.6863867,20.2235371 12.9385545,19.9929757 13.0034247,19.4415794 L13.0034247,19.4415794 L13.937,11.5 L9.5,11.5 C9.14562031,11.5 8.91328081,11.1466111 9.02944566,10.8300718 L9.02944566,10.8300718 L9.06587843,10.7519305 Z M14.666,2.967 L10.361,10.5 L14.5,10.5 C14.769367,10.5 14.9844253,10.7115509 14.99925,10.9705539 L14.99925,10.9705539 L14.9965753,11.0584206 L14.314,16.858 L18.676,8.5 L14.5,8.5 C14.2278448,8.5 14.0117495,8.2842647 14.0003869,8.02260574 L14.0003869,8.02260574 L14.004386,7.93391814 L14.666,2.967 Z M7.5,14 C7.77614237,14 8,14.2238576 8,14.5 C8,14.7761424 7.77614237,15 7.5,15 L4.5,15 C4.22385763,15 4,14.7761424 4,14.5 C4,14.2238576 4.22385763,14 4.5,14 L7.5,14 Z M7.5,7 C7.77614237,7 8,7.22385763 8,7.5 C8,7.77614237 7.77614237,8 7.5,8 L0.5,8 C0.223857625,8 3.38176876e-17,7.77614237 0,7.5 C-3.38176876e-17,7.22385763 0.223857625,7 0.5,7 L7.5,7 Z M11.5,3 C11.7761424,3 12,3.22385763 12,3.5 C12,3.77614237 11.7761424,4 11.5,4 L4.5,4 C4.22385763,4 4,3.77614237 4,3.5 C4,3.22385763 4.22385763,3 4.5,3 L11.5,3 Z"
                        fill={
                          {
                            "payload": 4278190230,
                            "type": 0,
                          }
                        }
                        propList={
                          [
                            "fill",
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
        <View
          style={
            {
              "flexDirection": "column",
              "flexShrink": 1,
              "justifyContent": "center",
              "paddingLeft": 28,
              "paddingRight": 8,
            }
          }
        >
          <Text
            ellipsizeMode="tail"
            style={
              {
                "alignItems": "flex-start",
                "color": "#1D1D26",
                "fontSize": 16,
                "textAlign": "left",
              }
            }
            textColor="#1D1D26"
          >
            You’ll no longer be eligible for free charging at selected pulse points.
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "height": 30,
            "justifyContent": "center",
            "paddingBottom": 32,
            "transform": [
              {
                "rotate": "-90deg",
              },
            ],
            "width": 30,
          }
        }
      />
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "borderBottomWidth": 0,
          "borderColor": "#D8D8D8",
          "borderTopWidth": 0,
          "flexDirection": "row",
          "justifyContent": "space-between",
          "opacity": 1,
          "paddingBottom": 16,
          "paddingTop": 16,
        }
      }
    >
      <View
        style={
          {
            "alignItems": "center",
            "flexDirection": "row",
            "flexShrink": 1,
            "justifyContent": "flex-start",
            "marginBottom": 0,
          }
        }
        type="primary"
      >
        <View
          style={
            {
              "alignItems": "center",
              "justifyContent": "center",
              "paddingLeft": 32,
              "paddingTop": 0,
              "width": 20,
            }
          }
          type="primary"
        >
          <RNSVGSvgView
            bbHeight="24"
            bbWidth="24"
            focusable={false}
            height="24"
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 24,
                  "width": 24,
                },
              ]
            }
            width="24"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                propList={
                  [
                    "fill",
                    "stroke",
                  ]
                }
                stroke={null}
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      -592,
                      -1126,
                    ]
                  }
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        588,
                        1122,
                      ]
                    }
                  >
                    <RNSVGPath
                      d="M12,4 C16.418278,4 20,7.581722 20,12 C20,16.418278 16.418278,20 12,20 C7.581722,20 4,16.418278 4,12 C4,7.581722 7.581722,4 12,4 Z M12,5 C8.13400675,5 5,8.13400675 5,12 C5,15.8659932 8.13400675,19 12,19 C15.8659932,19 19,15.8659932 19,12 C19,8.13400675 15.8659932,5 12,5 Z M16,12 C16.2761424,12 16.5,12.2238576 16.5,12.5 C16.5,14.9852814 14.4852814,17 12,17 C9.51471863,17 7.5,14.9852814 7.5,12.5 C7.5,12.2238576 7.72385763,12 8,12 L8,12 Z M15.463,13 L8.536,13 L8.54580909,13.0677182 C8.80701426,14.6691343 10.1550039,15.9054397 11.8079648,15.9948211 L11.8079648,15.9948211 L12,16 C13.739697,16 15.1829394,14.7307273 15.4541909,13.0677182 L15.4541909,13.0677182 L15.463,13 Z M9,8 C9.55228475,8 10,8.44771525 10,9 C10,9.55228475 9.55228475,10 9,10 C8.44771525,10 8,9.55228475 8,9 C8,8.44771525 8.44771525,8 9,8 Z M15,8 C15.5522847,8 16,8.44771525 16,9 C16,9.55228475 15.5522847,10 15,10 C14.4477153,10 14,9.55228475 14,9 C14,8.44771525 14.4477153,8 15,8 Z"
                      fill={
                        {
                          "payload": 4278190230,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "fill",
                        ]
                      }
                    />
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
        <View
          style={
            {
              "flexDirection": "column",
              "flexShrink": 1,
              "justifyContent": "center",
              "paddingLeft": 28,
              "paddingRight": 8,
            }
          }
        >
          <Text
            ellipsizeMode="tail"
            style={
              {
                "alignItems": "flex-start",
                "color": "#1D1D26",
                "fontSize": 16,
                "textAlign": "left",
              }
            }
            textColor="#1D1D26"
          >
            You’ll miss out on our best value tariffs starting from £0.35/kWh
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "height": 30,
            "justifyContent": "center",
            "paddingBottom": 32,
            "transform": [
              {
                "rotate": "-90deg",
              },
            ],
            "width": 30,
          }
        }
      />
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "borderBottomWidth": 0,
          "borderColor": "#D8D8D8",
          "borderTopWidth": 0,
          "flexDirection": "row",
          "justifyContent": "space-between",
          "opacity": 1,
          "paddingBottom": 16,
          "paddingTop": 16,
        }
      }
    >
      <View
        style={
          {
            "alignItems": "center",
            "flexDirection": "row",
            "flexShrink": 1,
            "justifyContent": "flex-start",
            "marginBottom": 0,
          }
        }
        type="primary"
      >
        <View
          style={
            {
              "alignItems": "center",
              "justifyContent": "center",
              "paddingLeft": 32,
              "paddingTop": 0,
              "width": 20,
            }
          }
          type="primary"
        >
          <RNSVGSvgView
            bbHeight="24"
            bbWidth="24"
            focusable={false}
            height="24"
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 24,
                  "width": 24,
                },
              ]
            }
            width="24"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                propList={
                  [
                    "fill",
                    "stroke",
                  ]
                }
                stroke={null}
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      -592,
                      -1079,
                    ]
                  }
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        588,
                        1074,
                      ]
                    }
                  >
                    <RNSVGPath
                      d="M16,5 C17.1045695,5 18,5.8954305 18,7 L18,8 C19.1045695,8 20,8.8954305 20,10 L20,14 C20,15.1045695 19.1045695,16 18,16 L18,17 C18,18.1045695 17.1045695,19 16,19 L6,19 C4.8954305,19 4,18.1045695 4,17 L4,7 C4,5.8954305 4.8954305,5 6,5 L16,5 Z M16,6 L6,6 C5.48716416,6 5.06449284,6.38604019 5.00672773,6.88337887 L5,7 L5,17 C5,17.5128358 5.38604019,17.9355072 5.88337887,17.9932723 L6,18 L16,18 C16.5128358,18 16.9355072,17.6139598 16.9932723,17.1166211 L17,17 L17,16 L14,16 C12.8954305,16 12,15.1045695 12,14 L12,10 C12,8.8954305 12.8954305,8 14,8 L17,8 L17,7 C17,6.48716416 16.6139598,6.06449284 16.1166211,6.00672773 L16,6 Z M18,9 L14,9 C13.4871642,9 13.0644928,9.38604019 13.0067277,9.88337887 L13,10 L13,14 C13,14.5128358 13.3860402,14.9355072 13.8833789,14.9932723 L14,15 L18,15 C18.5128358,15 18.9355072,14.6139598 18.9932723,14.1166211 L19,14 L19,10 C19,9.48716416 18.6139598,9.06449284 18.1166211,9.00672773 L18,9 Z M15,11 C15.5522847,11 16,11.4477153 16,12 C16,12.5522847 15.5522847,13 15,13 C14.4477153,13 14,12.5522847 14,12 C14,11.4477153 14.4477153,11 15,11 Z"
                      fill={
                        {
                          "payload": 4278190230,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "fill",
                        ]
                      }
                    />
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
        <View
          style={
            {
              "flexDirection": "column",
              "flexShrink": 1,
              "justifyContent": "center",
              "paddingLeft": 28,
              "paddingRight": 8,
            }
          }
        >
          <Text
            ellipsizeMode="tail"
            style={
              {
                "alignItems": "flex-start",
                "color": "#1D1D26",
                "fontSize": 16,
                "textAlign": "left",
              }
            }
            textColor="#1D1D26"
          >
            We’ll take your final payment at the end of your billing cycle and cancel your direct debit - you’ll then need to top up your credit to charge.
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "height": 30,
            "justifyContent": "center",
            "paddingBottom": 32,
            "transform": [
              {
                "rotate": "-90deg",
              },
            ],
            "width": 30,
          }
        }
      />
    </View>
  </View>
</View>
`;
