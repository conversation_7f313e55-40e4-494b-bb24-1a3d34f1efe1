import { render } from '@testing-library/react-native';
import React from 'react';

import { UserCountry } from '../../../common/enums';
import Theme from '../../../themes/Theme';
import theme from '../../../themes/Theme.default';
import TRANSLATIONS from '../../../translations/messages.json';
import { updateTranslations } from '../../../utils/updateTranslations';
import { CancelSubscriptionContent } from './CancelSubscriptionContent';

const mockTranslations = TRANSLATIONS;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'bp' }),
  isInternetReachable: true,
  featureFlags: {
    subsChargingRatesPromo: 0.44,
  },
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

describe('CancelSubscriptionContent rendering', () => {
  it('Should render correctly', () => {
    const result = render(
      <Theme>
        <CancelSubscriptionContent theme={theme} userCountry={UserCountry.UK} />
      </Theme>,
    );
    expect(result.toJSON()).toMatchSnapshot();
  });
});
