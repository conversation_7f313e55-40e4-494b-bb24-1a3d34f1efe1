import { PulseDataItemIds, UserCountry } from '../../../common/enums';
import { TRANSLATIONS } from '../../../translations';
import { formatCurrency, getCurrency } from '../../../utils/helpers';
import { cancelSubscriptionData } from './CancelSubscriptionContent.functions';

jest.mock('../../../utils/helpers', () => ({
  formatCurrency: jest.fn().mockReturnValue('£0.44'),
  getCurrency: jest.fn().mockReturnValue('GBP'),
}));

describe('cancelSubscriptionData', () => {
  const mockTheme = {
    subscriptionMfe: {
      text: {
        color: {
          tertiary: '#000000',
        },
      },
    },
  };

  const mockTranslations = TRANSLATIONS.en_GB;

  const mockSubsChargingRatesPromo = 0.44;
  const mockUserCountry = UserCountry.UK;

  it('should return an array with correct length', () => {
    const result = cancelSubscriptionData({
      // @ts-expect-error
      theme: mockTheme,
      translations: mockTranslations,
      subsChargingRatesPromo: mockSubsChargingRatesPromo,
      userCountry: mockUserCountry,
    });

    expect(result).toHaveLength(3);
  });

  it('should return correct data for each step', () => {
    const result = cancelSubscriptionData({
      // @ts-expect-error
      theme: mockTheme,
      translations: mockTranslations,
      subsChargingRatesPromo: mockSubsChargingRatesPromo,
      userCountry: mockUserCountry,
      percentageDiscount: '{{percentageDiscount}}',
    });

    expect(result[0]).toEqual({
      id: PulseDataItemIds.BP_PULSE_CARD,
      icon: expect.any(Object),
      subTitle: mockTranslations.cancelSubscription.STEP_1.SUBTITLE,
      title: mockTranslations.cancelSubscription.STEP_1.TITLE,
    });

    expect(result[1]).toEqual({
      id: PulseDataItemIds.LOWEST_RATES,
      icon: expect.any(Object),
      subTitle: mockTranslations.cancelSubscription.STEP_2.SUBTITLE,
      title: mockTranslations.cancelSubscription.STEP_2.TITLE,
    });

    expect(result[2]).toEqual({
      id: PulseDataItemIds.DIAMOND,
      icon: expect.any(Object),
      subTitle: '',
      title: mockTranslations.cancelSubscription.STEP_3.TITLE.replace(
        '{{rate}}',
        '£0.44',
      ),
    });
  });

  it('should call formatCurrency with correct arguments', () => {
    cancelSubscriptionData({
      // @ts-expect-error
      theme: mockTheme,
      translations: mockTranslations,
      subsChargingRatesPromo: mockSubsChargingRatesPromo,
      userCountry: mockUserCountry,
    });

    expect(formatCurrency).toHaveBeenCalledWith({
      amount: mockSubsChargingRatesPromo,
      currency: 'GBP',
    });
  });

  it('should call getCurrency with correct argument', () => {
    cancelSubscriptionData({
      // @ts-expect-error
      theme: mockTheme,
      translations: mockTranslations,
      subsChargingRatesPromo: mockSubsChargingRatesPromo,
      userCountry: mockUserCountry,
    });

    expect(getCurrency).toHaveBeenCalledWith(mockUserCountry);
  });
});
