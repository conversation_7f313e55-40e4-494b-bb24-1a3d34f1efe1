import React from 'react';

import { PulseDataItemIds, UserCountry } from '../../../common/enums';
import { IMySubscriptionData } from '../../../common/interfaces';
import { formatCurrency, getCurrency } from '../../../utils/helpers';
import { ChargingOnGoIcon, OfferIcon, PolarDiamondIcon } from '../../Atoms';

export const cancelSubscriptionData = ({
  theme,
  translations,
  subsChargingRatesPromo,
  userCountry,
  percentageDiscount,
}: IMySubscriptionData) => {
  return [
    {
      id: PulseDataItemIds.BP_PULSE_CARD,
      icon: (
        <ChargingOnGoIcon color={theme.subscriptionMfe?.text.color.tertiary} />
      ),
      title: translations.cancelSubscription.STEP_1.TITLE,
      subTitle: translations.cancelSubscription.STEP_1.SUBTITLE.replace(
        '{{percentageDiscount}}',
        percentageDiscount ?? '',
      ),
    },
    {
      id: PulseDataItemIds.LOWEST_RATES,
      icon: <OfferIcon color={theme.subscriptionMfe?.text.color.tertiary} />,
      title: translations.cancelSubscription.STEP_2.TITLE,
      subTitle: translations.cancelSubscription.STEP_2.SUBTITLE,
    },
    ...(userCountry !== UserCountry.DE
      ? [
          {
            id: PulseDataItemIds.DIAMOND,
            icon: (
              <PolarDiamondIcon
                color={theme.subscriptionMfe?.text.color.tertiary}
              />
            ),
            title: translations.cancelSubscription.STEP_3.TITLE.replace(
              '{{rate}}',
              formatCurrency({
                amount: subsChargingRatesPromo,
                currency: getCurrency(userCountry),
              }),
            ),
            subTitle: '',
          },
        ]
      : []),
  ];
};
