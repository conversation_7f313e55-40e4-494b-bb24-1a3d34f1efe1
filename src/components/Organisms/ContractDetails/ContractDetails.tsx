import React from 'react';

import { useSettings } from '../../../providers/Settings';
import * as S from '../../Organisms/SubscriptionDetails/SubscriptionDetails.styles';

interface ContractDetailsProps {
  billingDay: number;
}

export const ContractDetails: React.FC<ContractDetailsProps> = ({
  billingDay,
}) => {
  const { t } = useSettings();

  const getDaySuffix = (day: number) => {
    if (day >= 11 && day <= 13) {
      return 'th';
    }
    switch (day % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  };

  return (
    <>
      <S.TextWrapper>
        <S.SmallText>
          {t.mySubscription.SUBSCRIPTION_DETAILS.CONTRACT_TERM}
        </S.SmallText>
        <S.SmallText>
          {t.mySubscription.SUBSCRIPTION_DETAILS.CONTRACT_TERM_VALUE}
        </S.SmallText>
      </S.TextWrapper>

      <S.TextWrapper>
        <S.SmallText>
          {t.mySubscription.SUBSCRIPTION_DETAILS.BILLING_DATE}
        </S.SmallText>
        <S.SmallText>
          {t.mySubscription.SUBSCRIPTION_DETAILS.BILLING_DATE_VALUE?.replace(
            '{{day}}',
            `${billingDay}${getDaySuffix(billingDay)}`,
          )}
        </S.SmallText>
      </S.TextWrapper>

      <S.TextWrapper>
        <S.SmallText flex>
          {t.mySubscription.SUBSCRIPTION_DETAILS.CANCELLATION}
        </S.SmallText>
        <S.SmallText flex align="right">
          {t.mySubscription.SUBSCRIPTION_DETAILS.CANCELLATION_VALUE}
        </S.SmallText>
      </S.TextWrapper>
    </>
  );
};
