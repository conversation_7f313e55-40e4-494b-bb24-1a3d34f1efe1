import React from 'react';

import { Currency } from '../../../common/enums';
import { useSettings } from '../../../providers/Settings';
import { formatCurrency } from '../../../utils/helpers';
import * as S from '../SubscriptionDetails/SubscriptionDetails.styles';

interface PaymentSummaryProps {
  subscription: number;
  totalAmount: number;
  currency?: string;
  isFirstTimeSubscriber: boolean;
  hasActiveOffer: boolean;
}

export const PaymentSummary: React.FC<PaymentSummaryProps> = ({
  subscription,
  totalAmount,
  currency = Currency.GBP,
  isFirstTimeSubscriber,
  hasActiveOffer,
}) => {
  const { t } = useSettings();

  const renderTotalAmount = () => {
    if (isFirstTimeSubscriber) {
      return (
        <>
          <S.NormalText strikethrough>
            {formatCurrency({ amount: subscription, currency })}
          </S.NormalText>{' '}
          {formatCurrency({ amount: 0, currency })}
        </>
      );
    }

    if (hasActiveOffer && totalAmount < subscription) {
      return (
        <>
          <S.NormalText strikethrough>
            {formatCurrency({ amount: subscription, currency })}
          </S.NormalText>{' '}
          {formatCurrency({ amount: totalAmount, currency })}
        </>
      );
    }

    return formatCurrency({ amount: subscription, currency });
  };

  return (
    <>
      <S.HorizontalLine />
      <S.TextWrapper>
        <S.NormalText>
          {t.mySubscription.SUBSCRIPTION_DETAILS.TOTAL_TO_PAY}
        </S.NormalText>
        <S.NormalText>{renderTotalAmount()}</S.NormalText>
      </S.TextWrapper>
    </>
  );
};
