import { render } from '@testing-library/react-native';
import React from 'react';

import { Currency } from '../../../common/enums';
import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import { PaymentSummary } from './PaymentSummary';

const mockTranslations = TRANSLATIONS.en_GB;

const mockUseSettings = jest.fn().mockReturnValue({
  t: mockTranslations,
});

jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockFormatCurrency = jest
  .fn()
  .mockImplementation(({ amount, currency }) => {
    const symbol = currency === Currency.GBP ? '£' : '€';
    return `${symbol}${amount.toFixed(2)}`;
  });

jest.mock('../../../utils/helpers', () => ({
  formatCurrency: (params: any) => mockFormatCurrency(params),
}));

describe('PaymentSummary', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('First-time subscriber', () => {
    it('Should show strikethrough price and £0.00 for first-time subscribers', () => {
      const { getAllByText } = render(
        <Theme>
          <PaymentSummary
            subscription={7.85}
            totalAmount={7.85}
            currency="GBP"
            isFirstTimeSubscriber={true}
            hasActiveOffer={false}
          />
        </Theme>,
      );

      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 7.85,
        currency: 'GBP',
      });
      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 0,
        currency: 'GBP',
      });

      const allTextElements = getAllByText(/£/);
      expect(allTextElements.length).toBeGreaterThan(0);
    });

    it('Should show strikethrough and £0.00 even with an offer for first-time subscribers', () => {
      const { getAllByText } = render(
        <Theme>
          <PaymentSummary
            subscription={7.85}
            totalAmount={5.0}
            currency="GBP"
            isFirstTimeSubscriber={true}
            hasActiveOffer={true}
          />
        </Theme>,
      );

      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 7.85,
        currency: 'GBP',
      });
      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 0,
        currency: 'GBP',
      });

      const allTextElements = getAllByText(/£/);
      expect(allTextElements.length).toBeGreaterThan(0);
    });
  });

  describe('Returning subscriber with offer', () => {
    it('Should show strikethrough original price and discounted price', () => {
      const { getAllByText } = render(
        <Theme>
          <PaymentSummary
            subscription={7.85}
            totalAmount={5.0}
            currency="GBP"
            isFirstTimeSubscriber={false}
            hasActiveOffer={true}
          />
        </Theme>,
      );

      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 7.85,
        currency: 'GBP',
      });
      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 5.0,
        currency: 'GBP',
      });

      const allTextElements = getAllByText(/£/);
      expect(allTextElements.length).toBeGreaterThan(0);
    });

    it('Should show full price when offer makes it free', () => {
      const { getAllByText } = render(
        <Theme>
          <PaymentSummary
            subscription={7.85}
            totalAmount={0}
            currency="GBP"
            isFirstTimeSubscriber={false}
            hasActiveOffer={true}
          />
        </Theme>,
      );

      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 7.85,
        currency: 'GBP',
      });
      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 0,
        currency: 'GBP',
      });

      const allTextElements = getAllByText(/£/);
      expect(allTextElements.length).toBeGreaterThan(0);
    });
  });

  describe('Returning subscriber without offer', () => {
    it('Should show only the regular price without strikethrough', () => {
      const { getByText } = render(
        <Theme>
          <PaymentSummary
            subscription={7.85}
            totalAmount={7.85}
            currency="GBP"
            isFirstTimeSubscriber={false}
            hasActiveOffer={false}
          />
        </Theme>,
      );

      expect(mockFormatCurrency).toHaveBeenCalledTimes(1);
      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 7.85,
        currency: 'GBP',
      });
      expect(getByText('£7.85')).toBeDefined();
    });
  });

  describe('Currency handling', () => {
    it('Should format EUR currency correctly', () => {
      render(
        <Theme>
          <PaymentSummary
            subscription={10.0}
            totalAmount={10.0}
            currency="EUR"
            isFirstTimeSubscriber={false}
            hasActiveOffer={false}
          />
        </Theme>,
      );

      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 10.0,
        currency: 'EUR',
      });
    });
  });

  it('Should render the total to pay label', () => {
    const { getByText } = render(
      <Theme>
        <PaymentSummary
          subscription={7.85}
          totalAmount={7.85}
          currency="GBP"
          isFirstTimeSubscriber={false}
          hasActiveOffer={false}
        />
      </Theme>,
    );

    expect(
      getByText(
        mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.TOTAL_TO_PAY,
      ),
    ).toBeDefined();
  });

  it('Should render horizontal line', () => {
    const { UNSAFE_getByType } = render(
      <Theme>
        <PaymentSummary
          subscription={7.85}
          totalAmount={7.85}
          currency="GBP"
          isFirstTimeSubscriber={false}
          hasActiveOffer={false}
        />
      </Theme>,
    );

    const horizontalLine = UNSAFE_getByType(
      require('../SubscriptionDetails/SubscriptionDetails.styles')
        .HorizontalLine,
    );
    expect(horizontalLine).toBeDefined();
  });
});
