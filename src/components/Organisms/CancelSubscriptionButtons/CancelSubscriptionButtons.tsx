import { <PERSON><PERSON>, ButtonAction, Spacer } from '@bp/ui-components/mobile/core';
import { Route, useRoute } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';

import { SubscriptionScreenNames, UserTypeEnum } from '../../../common/enums';
import { ICancelSubscriptionButtons } from '../../../common/interfaces';
import { useSettings } from '../../../providers/Settings';
import { useUserInfo } from '../../../providers/UserInfoProvider';
import { hasInactiveMembership } from '../../../utils/helpers';
import { CancelButton } from '../../Atoms';
import { CheckBoxInput } from '../../Molecules';
import * as S from './CancelSubscriptionButtons.styles';
interface RouteParams {
  from?: string;
}
export const CancelSubscriptionButtons = ({
  userType,
  nextBillingdate,
  setModalVisible,
  navigate,
  cancelMembership,
  theme,
  isInternetReachable,
  loading,
}: ICancelSubscriptionButtons) => {
  const { t: translations } = useSettings();
  const { userInfo } = useUserInfo();
  const route = useRoute<Route<string, RouteParams>>();
  const [tick, setTick] = useState(false);
  useEffect(() => {
    if (route?.params?.from === 'CancelSubscriptionFailed') {
      setTick(true);
    } else {
      setTick(false);
    }
  }, [route.params]);

  const onPressDisplayModal = () => {
    setModalVisible(true);
  };

  const handleDoNotCancel = () => {
    if (hasInactiveMembership(userInfo?.membership)) {
      navigate(SubscriptionScreenNames.SubscriptionPaused);
    } else {
      navigate(SubscriptionScreenNames.MySubscription);
    }
  };

  return userType === UserTypeEnum.SUBS_WALLET ? (
    <S.Wrapper>
      <CheckBoxInput
        noBorderRadius
        alignCenter
        defaultChecked={tick}
        onPress={() => setTick((prev) => !prev)}
        label={
          nextBillingdate
            ? translations.cancelSubscription.TICK_TEXT.replace(
                '{{activeUntil}}',
                nextBillingdate,
              )
            : translations.cancelSubscription.TICK_TEXT_SUBSCRIPTION_PAUSED
        }
        testID={translations.cancelSubscription.TICK_TEXT}
        accessibilityLabel={translations.cancelSubscription.TICK_TEXT}
        color={theme.subscriptionMfe?.color.grey.one}
      />
      <Spacer vSpace={24} />
      <Button
        disabled={!tick || !isInternetReachable}
        type={ButtonAction.SECONDARY}
        accessibilityLabel={
          translations.gocardless.accessibility.CANCEL_BUTTON.LABEL
        }
        accessibilityHint={
          translations.gocardless.accessibility.CANCEL_BUTTON.HINT
        }
        onPress={() => cancelMembership()}
        loading={loading}
        loadingText={translations.gocardless.buttons.CANCEL_BTN}
        loadingColor={theme.subscriptionMfe?.color.grey.two}
      >
        {translations.gocardless.buttons.CANCEL_BTN}
      </Button>
      <Spacer vSpace={16} />
      <Button
        type={ButtonAction.PRIMARY}
        accessibilityLabel={
          translations.gocardless.accessibility.DO_NOT_CANCEL_BUTTON.LABEL
        }
        accessibilityHint={
          translations.gocardless.accessibility.DO_NOT_CANCEL_BUTTON.HINT
        }
        onPress={() => handleDoNotCancel()}
        disabled={loading}
      >
        {translations.gocardless.buttons.DO_NOT_CANCEL_BTN}
      </Button>
    </S.Wrapper>
  ) : (
    <S.Wrapper>
      <CancelButton
        accessibilityLabel={
          translations.gocardless.accessibility.CANCEL_BUTTON.LABEL
        }
        accessibilityHint={
          translations.gocardless.accessibility.CANCEL_BUTTON.HINT
        }
        onPress={() => onPressDisplayModal()}
      >
        {translations.gocardless.buttons.CANCEL_BTN}
      </CancelButton>
      <Spacer vSpace={16} />
      <Button
        type={ButtonAction.SENARY}
        onPress={() => navigate(SubscriptionScreenNames.GoCardlessMembership)}
      >
        {translations.gocardless.buttons.IWANTTOSTAY}
      </Button>
    </S.Wrapper>
  );
};
