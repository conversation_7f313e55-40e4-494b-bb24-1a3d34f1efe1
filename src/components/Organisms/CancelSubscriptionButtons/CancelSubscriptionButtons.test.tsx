import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import { SubscriptionScreenNames, UserTypeEnum } from '../../../common/enums';
import Theme from '../../../themes/Theme';
import theme from '../../../themes/Theme.default';
import { TRANSLATIONS } from '../../../translations';
import { CancelSubscriptionButtons } from './CancelSubscriptionButtons';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  isInternetReachable: true,
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
const mockSetModalVisible = jest.fn();
const mockNavigate = jest.fn();
const mockCancelMembership = jest.fn();

jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockUseRoute = jest.fn().mockReturnValue({
  params: { from: 'CancelSubscriptionFailed' },
});

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useRoute: () => mockUseRoute(),
}));

describe('CancelSubscriptionButtons rendering', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    const result = render(
      <Theme>
        <CancelSubscriptionButtons
          nextBillingdate={'03/05/2024'}
          theme={theme}
          setModalVisible={() => {}}
          cancelMembership={mockCancelMembership}
          navigate={() => {}}
        />
      </Theme>,
    );
    expect(result.toJSON()).toMatchSnapshot();
  });

  it.each([
    [
      UserTypeEnum.SUBS_WALLET,
      true,
      mockTranslations.cancelSubscription.TICK_TEXT,
    ],
    [UserTypeEnum.SUBS, false, mockTranslations.gocardless.buttons.IWANTTOSTAY],
  ])(
    'should render correct elements for userType %s',
    (userType, shouldRenderTickBox) => {
      const { queryByTestId, queryByText } = render(
        <Theme>
          <CancelSubscriptionButtons
            userType={userType}
            theme={theme}
            setModalVisible={() => {}}
            cancelMembership={mockCancelMembership}
            navigate={() => {}}
          />
        </Theme>,
      );

      const tickBox = queryByTestId(
        mockTranslations.cancelSubscription.TICK_TEXT,
      );
      const stayButton = queryByText(
        mockTranslations.gocardless.buttons.IWANTTOSTAY,
      );

      if (shouldRenderTickBox) {
        expect(tickBox).toBeTruthy();
        expect(stayButton).toBeNull();
      } else {
        expect(tickBox).toBeNull();
        expect(stayButton).toBeTruthy();
      }
    },
  );

  it('should handle checkbox toggle and cancel button state for SUBS_WALLET user', () => {
    const { getByTestId, queryByText } = render(
      <Theme>
        <CancelSubscriptionButtons
          userType={UserTypeEnum.SUBS_WALLET}
          theme={theme}
          setModalVisible={mockSetModalVisible}
          cancelMembership={mockCancelMembership}
          navigate={mockNavigate}
          isInternetReachable={true}
        />
      </Theme>,
    );

    const tickBox = getByTestId(mockTranslations.cancelSubscription.TICK_TEXT);
    let cancelButton = queryByText(
      mockTranslations.gocardless.buttons.CANCEL_BTN,
    );

    expect(tickBox.props.checked).toBe(true);
    expect(cancelButton.props.disabled).toBe(false);

    fireEvent.press(tickBox);
    expect(tickBox.props.checked).toBe(false);
    cancelButton = queryByText(mockTranslations.gocardless.buttons.CANCEL_BTN);
    expect(cancelButton.props.disabled).toBe(true);

    fireEvent.press(tickBox);
    expect(tickBox.props.checked).toBe(true);
    cancelButton = queryByText(mockTranslations.gocardless.buttons.CANCEL_BTN);
    expect(cancelButton.props.disabled).toBe(false);
  });

  it.each([
    [
      UserTypeEnum.SUBS_WALLET,
      mockTranslations.gocardless.buttons.CANCEL_BTN,
      true,
    ],
    [UserTypeEnum.SUBS, mockTranslations.gocardless.buttons.CANCEL_BTN, true],
    [
      UserTypeEnum.SUBS_WALLET,
      mockTranslations.gocardless.buttons.DO_NOT_CANCEL_BTN,
      false,
    ],
    [UserTypeEnum.SUBS, mockTranslations.gocardless.buttons.IWANTTOSTAY, false],
  ])(
    'should handle button press correctly for userType %s and button %s',
    (userType, buttonText, shouldCallSetModalVisible) => {
      const { getByText } = render(
        <Theme>
          <CancelSubscriptionButtons
            userType={userType}
            theme={theme}
            setModalVisible={mockSetModalVisible}
            cancelMembership={mockCancelMembership}
            navigate={mockNavigate}
            isInternetReachable={true}
          />
        </Theme>,
      );

      const button = getByText(buttonText);
      fireEvent.press(button);

      if (userType === UserTypeEnum.SUBS_WALLET) {
        if (buttonText === "Don't cancel my subscription") {
          expect(mockNavigate).toHaveBeenCalledWith(
            SubscriptionScreenNames.MySubscription,
          );
        } else {
          expect(mockCancelMembership).toHaveBeenCalled();
        }
      } else if (shouldCallSetModalVisible) {
        expect(mockSetModalVisible).toHaveBeenCalledWith(true);
      } else {
        expect(mockNavigate).toHaveBeenCalledWith(
          SubscriptionScreenNames.GoCardlessMembership,
        );
      }
    },
  );

  it('should disable cancel button when checkbox is unchecked for SUBS_WALLET user', () => {
    mockUseRoute.mockReturnValueOnce({ params: { from: 'SomeOtherRoute' } });

    const { getByText } = render(
      <Theme>
        <CancelSubscriptionButtons
          userType={UserTypeEnum.SUBS_WALLET}
          theme={theme}
          setModalVisible={mockSetModalVisible}
          cancelMembership={mockCancelMembership}
          navigate={mockNavigate}
          isInternetReachable={true}
        />
      </Theme>,
    );

    const cancelButton = getByText(
      mockTranslations.gocardless.buttons.CANCEL_BTN,
    );
    expect(cancelButton.props.disabled).toBe(true);
  });
});
