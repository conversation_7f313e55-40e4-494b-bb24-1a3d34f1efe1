// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CancelSubscriptionButtons rendering should render correctly 1`] = `
<View
  style={{}}
>
  <View
    accessibilityHint="Cancel gocard account"
    accessibilityLabel="Cancel gocard account"
    accessibilityRole="button"
    accessibilityState={
      {
        "busy": undefined,
        "checked": undefined,
        "disabled": false,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessibilityValue={
      {
        "max": undefined,
        "min": undefined,
        "now": undefined,
        "text": undefined,
      }
    }
    accessible={true}
    collapsable={false}
    focusable={true}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      {
        "alignSelf": "stretch",
        "backgroundColor": "#F5CCCC",
        "borderBottomLeftRadius": 28,
        "borderBottomRightRadius": 28,
        "borderBottomWidth": 2,
        "borderColor": "#F5CCCC",
        "borderLeftWidth": 2,
        "borderRightWidth": 2,
        "borderTopLeftRadius": 28,
        "borderTopRightRadius": 28,
        "borderWidth": 2,
        "height": 55,
        "justifyContent": "center",
        "lineHeight": 55,
        "opacity": 1,
      }
    }
    testID="CancelButton"
  >
    <Text
      disabled={false}
      size="normal"
      style={
        {
          "alignItems": "center",
          "color": "#CF0001",
          "display": "flex",
          "fontSize": 16,
          "justifyContent": "center",
          "letterSpacing": 0.7,
          "textAlign": "center",
          "textDecorationColor": "#CF0001",
          "textDecorationLine": "none",
          "textDecorationStyle": "solid",
        }
      }
    >
      Cancel my subscription
    </Text>
  </View>
  <View
    style={
      {
        "height": 16,
        "width": 1,
      }
    }
  />
  <View
    accessibilityRole="button"
    accessibilityState={
      {
        "busy": undefined,
        "checked": undefined,
        "disabled": false,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessibilityValue={
      {
        "max": undefined,
        "min": undefined,
        "now": undefined,
        "text": undefined,
      }
    }
    accessible={true}
    collapsable={false}
    focusable={true}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      {
        "alignItems": "center",
        "backgroundColor": "transparent",
        "borderBottomLeftRadius": 23,
        "borderBottomRightRadius": 23,
        "borderColor": "transparent",
        "borderStyle": "solid",
        "borderTopLeftRadius": 23,
        "borderTopRightRadius": 23,
        "borderWidth": 0,
        "justifyContent": "center",
        "minHeight": 46,
        "opacity": 1,
        "paddingHorizontal": 0,
        "paddingVertical": 12,
      }
    }
  >
    <View>
      <Text
        disabled={false}
        inverted={false}
        size="large"
        style={
          {
            "color": "#0164cc",
            "fontFamily": "Roboto-Regular",
            "fontSize": 15,
            "letterSpacing": 0.7,
            "textAlign": "center",
          }
        }
        type="senary"
      >
        I want to stay
      </Text>
      <View
        style={
          {
            "paddingTop": 4,
          }
        }
      >
        <View
          backgroundColor="#0164cc"
          borderRadius={0}
          height={1}
          style={
            {
              "backgroundColor": "#0164cc",
              "borderBottomLeftRadius": 0,
              "borderBottomRightRadius": 0,
              "borderTopLeftRadius": 0,
              "borderTopRightRadius": 0,
              "height": 1,
              "width": "100%",
            }
          }
        />
      </View>
    </View>
  </View>
</View>
`;
