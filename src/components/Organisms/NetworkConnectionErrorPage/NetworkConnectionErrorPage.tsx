import React, { useState } from 'react';

import { useSettings } from '../../../providers/Settings';
import { useUserInfo } from '../../../providers/UserInfoProvider';
import { getCountrySpecificText } from '../../../utils/helpers';
import NetworkConnectionError from '../../Molecules/NetworkConnectionError/NetworkConnectionError';
import { LoadingPage } from '../LoadingPage/LoadingPage';
import * as S from './NetworkConnectionErrorPage.styles';

export const NetworkConnectionErrorPage = () => {
  const { t, user } = useSettings();

  const { isLoading, refetchUserInfo } = useUserInfo();
  const [simulatedLoading, setSimulatedLoading] = useState(false);
  const loading = isLoading || simulatedLoading;

  const loadUserInfo = () => {
    setSimulatedLoading(true);
    refetchUserInfo?.();
    setTimeout(() => {
      setSimulatedLoading(false);
    }, 1000);
  };

  if (loading) {
    return <LoadingPage text={t.gocardless.text.LOADING} />;
  }

  return (
    <S.ScreenWrapper>
      <NetworkConnectionError
        isHeaderRequired={true}
        headingText={getCountrySpecificText(
          t,
          'gocardless.text.SUBSCRIBE_HEADER',
          user?.country,
        )}
        onPress={loadUserInfo}
      />
    </S.ScreenWrapper>
  );
};
