import { act, fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import { UserTypeEnum } from '../../../common/enums';
import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import { NetworkConnectionErrorPage } from './NetworkConnectionErrorPage';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: {
    id: '123',
    balance: '1.99',
    gocardless: { mandateStatus: 'applied' },
    tagIds: [],
  },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  featureFlags: {
    enableInvoicesList: true,
    enableOfferDetails: true,
    enableRFID: true,
  },
  isInternetReachable: true,
};

jest.mock('react-native-device-info', () => {
  return {
    hasNotch: jest.fn(),
    mockRNDeviceInfo: jest.fn(),
  };
});

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);

jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockUserInfoObj = {
  userInfo: {
    type: UserTypeEnum.NEW,
    gocardless: {},
  },
  isLoading: true,
  refetchUserInfo: jest.fn(),
};
const mockUserInfo = jest.fn().mockReturnValue(mockUserInfoObj);
jest.mock('../../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfo(),
}));

const renderComponent = () =>
  render(
    <Theme>
      <NetworkConnectionErrorPage />
    </Theme>,
  );

describe('NetworkConnectionErrorPage Component', () => {
  it('renders loading page when loading is true', () => {
    const { getByText } = renderComponent();
    expect(
      getByText(mockUseSettingsObj.t.gocardless.text.LOADING),
    ).toBeDefined();
  });

  it('should show the No Signal Error if there is no internet connection', () => {
    mockUseSettings.mockReturnValueOnce({
      ...mockUseSettingsObj,
      isInternetReachable: false,
    });
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      isLoading: false,
    });

    const { getByText } = renderComponent();

    expect(
      getByText(
        mockUseSettingsObj.t.networkConnectionError.text.SUB_HEADING_TEXT,
      ),
    ).toBeDefined();

    mockUseSettings.mockReturnValue(mockUseSettingsObj);
    expect(mockUseSettingsObj.isInternetReachable).toEqual(true);
  });

  it('should show the No Signal Error if there is an error retrieving userInfo', async () => {
    mockUseSettings.mockReturnValueOnce({
      ...mockUseSettingsObj,
      isInternetReachable: true,
    });
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      isInternetReachable: true,
      error: 'Error',
      isLoading: false,
    });
    const { getByText } = renderComponent();
    act(() => {
      jest.useFakeTimers();
    });

    expect(
      getByText(
        mockUseSettingsObj.t.networkConnectionError.text.SUB_HEADING_TEXT,
      ),
    ).toBeDefined();
    mockUseSettings.mockReturnValue(mockUseSettingsObj);
    mockUserInfo.mockReturnValue(mockUserInfoObj);
    expect(mockUseSettingsObj.isInternetReachable).toEqual(true);
  });

  it('should set a simulated loading state if the retry button is pressed on no internet connection', async () => {
    mockUseSettings.mockReturnValueOnce({
      ...mockUseSettingsObj,
      isInternetReachable: false,
    });
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      isInternetReachable: false,
      isLoading: false,
    });
    const { getByText } = renderComponent();
    const button = getByText(
      mockUseSettingsObj.t.networkConnectionError.text.RETRY,
    );
    fireEvent.press(button);

    act(() => {
      jest.useFakeTimers();
    });

    expect(mockUserInfoObj.refetchUserInfo).toHaveBeenCalled();
    act(() => {
      jest.useFakeTimers();
    });
    mockUseSettings.mockReturnValue(mockUseSettingsObj);
    mockUserInfo.mockReturnValue(mockUserInfoObj);
    expect(mockUseSettingsObj.isInternetReachable).toEqual(true);
  });
});
