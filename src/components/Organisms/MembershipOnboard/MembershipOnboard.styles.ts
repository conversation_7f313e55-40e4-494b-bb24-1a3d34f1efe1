import { Dimensions } from 'react-native';
import { styled } from 'styled-components/native';

const phoneWidth = Dimensions.get('screen').width > 320 ? 24 : 8;

export const MembershipOnboardContainer = styled.SafeAreaView`
  flex-direction: column;
  background-color: #ffffff;
  padding-top: 0px;
  flex: 1 0 0;
  display: flex;
  flex-grow: 1;
`;

export const ScreenWrapper = styled.ScrollView.attrs(() => ({
  contentContainerStyle: {
    flexGrow: 1,
    paddingTop: 0,
  },
  bounces: false,
}))`
  flex: 1;
  display: flex;
  padding-top: 0;
`;

export const Wrapper = styled.SafeAreaView`
  flex: 1 0 0;
  margin: 10px 30px 24px 24px;
`;

export const Container = styled.SafeAreaView`
  margin-top: 10px;
  flex: 1;
  flex-direction: row;
  margin-right: 16px;
`;

export const ContainerWrapper = styled.View``;

export const IconContainer = styled.View<{ paddingTop?: boolean }>`
  padding-right: 6px;
  padding-top: ${({ paddingTop }) => (paddingTop ? '7px' : '0px')};
`;

export const InfoHeader = styled.Text`
  font-family: Roboto-Bold;
  font-size: 16px;
  line-height: 26px;
  font-weight: bold;
  color: rgb(17, 17, 17);
`;

export const InfoContent = styled.Text`
  flex-direction: column;
  font-family: Roboto-Regular;
  font-size: 13px;
  line-height: 23px;
  letter-spacing: 0.2px;
  color: rgb(17, 17, 17);
`;
export const WelcomeMsg = styled.Text`
  flex-direction: column;
  font-family: Roboto-Bold;
  font-size: 16px;
  font-weight: bold;
  line-height: 28px;
  letter-spacing: 0.1px;
  color: rgb(17, 17, 17);
`;

export const TariffWrapper = styled.View`
  margin: 0px 10px 10px 24px;
`;

export const TariffHeader = styled.Text`
  padding-left: 10px;
  font-family: Roboto-Regular;
  font-size: 16px;
  line-height: 23px;
  letter-spacing: 0.25px;
  color: rgb(17, 17, 17);
`;

export const TariffContentWrapper = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin: 0px 12px 0px 12px;
`;

export const LeftContent = styled.Text<{
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
}>`
  flex: 1;
  padding-left: 10px;
  font-family: ${({ fontFamily }) => fontFamily || 'Roboto-Light'};
  font-size: ${({ fontSize }) => fontSize || '13px'};
  ${({ fontWeight }) => (fontWeight ? `font-weight: ${fontWeight};` : '')};
  line-height: 28px;
  letter-spacing: 0.15px;
  color: rgb(17, 17, 17);
  width: 70%;
  text-align: left;
  margin: 12px;
`;
export const RightContent = styled.Text`
  font-family: Roboto-Medium;
  font-size: 13px;
  line-height: 28px;
  letter-spacing: 0.15px;
  color: rgb(17, 17, 17);
  width: 40%;
  text-align: right;
  align-self: center;
  margin: 12px;
`;

export const Line = styled.View`
  margin: 0px 0px 5px 0px;
  border: 0.6px solid #ededed;
`;
export const MidLine = styled.View`
  margin: 0px 20px 0px 30px;
  border: 0.8px solid #ededed;
`;

export const BottomViewContainer = styled.View`
  background-color: white;
  width: 100%;
  padding: 20px ${phoneWidth}px;
  margin-bottom: 0;
  padding-bottom: 30px;
  box-shadow: 0px 50px 40px ${(p) => p.theme.subscriptionMfe.color.primary};
`;
