import { useWallet, WalletScreenNames } from '@bp/bppay-wallet-feature';
import {
  Button,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import React, { useEffect } from 'react';

import {
  SubsAnalyticsEventSubsSubscribeAndSaveScreenCTAClick,
  SubsAnalyticsEventSubsSubscribeAndSaveScreenOpen,
} from '../../../analytics/events';
import {
  SubscriptionScreenNames,
  UserCountry,
  UserTypeEnum,
} from '../../../common/enums';
import { MembershipSectionProps } from '../../../common/interfaces';
import { useSettings } from '../../../providers/Settings';
import { useUserInfo } from '../../../providers/UserInfoProvider';
import theme from '../../../themes/Theme.default';
import {
  formatCurrency,
  getAnalyticsPayloadPaygWallet,
  getAnalyticsPayloadPaygWalletClick,
  getCurrency,
} from '../../../utils/helpers';
import { HeaderWithBackground } from '../../Atoms/HeaderWithBackground/HeaderWithBackground';
import { CustomLinkParagraphComponent, FootnoteDetail } from '../../Molecules';
import { details, sections, tariffs } from './MembershipOnboard.functions';
import * as S from './MembershipOnboard.styles';

const MembershipOnboard = (props: any) => {
  const { userInfo, resubscribed } = useUserInfo();
  const { t, featureFlags, tariffPricing, locale, onAnalyticsEvent } =
    useSettings();
  const { navigation } = props;

  const { selectedCard, triggerAddPaymentCardFlow } = useWallet();

  const subscriptionAmount = formatCurrency({
    amount: featureFlags.subscriptionAmount,
    currency: getCurrency(userInfo?.country),
    locale,
  });

  const subTitle = t.membershipOnboard.subTitle.replace(
    '{{amount}}',
    subscriptionAmount,
  );

  const buttonText = resubscribed
    ? t.membershipOnboard.buttons.restartComfortMember.label
    : t.membershipOnboard.buttons.comfortMember.label;

  const buttonAccessibilityLabel = resubscribed
    ? t.membershipOnboard.buttons.restartComfortMember.accessibility.label
    : t.membershipOnboard.buttons.comfortMember.accessibility.label;

  const buttonAccessibilityHint = resubscribed
    ? t.membershipOnboard.buttons.restartComfortMember.accessibility.hint
    : t.membershipOnboard.buttons.comfortMember.accessibility.hint;

  const handleNavigation = () => {
    navigation.navigate('MyComfortMembershipActivation', {
      screen: SubscriptionScreenNames.MyComfortMembershipActivation,
      params: {},
    });
  };

  const onPressHandler = () => {
    onAnalyticsEvent(
      SubsAnalyticsEventSubsSubscribeAndSaveScreenCTAClick(
        getAnalyticsPayloadPaygWalletClick(
          userInfo,
          resubscribed,
          !!selectedCard,
        ),
      ),
    );
    if (userInfo?.type === UserTypeEnum.PAYG_WALLET && !selectedCard) {
      triggerAddPaymentCardFlow({
        customNavigationKey: {
          name: SubscriptionScreenNames.MyComfortMembershipActivation,
          key: SubscriptionScreenNames.MyComfortMembershipActivation,
        },
        isSubsDefault: true,
        onAddCardUnsuccessfulAnalytics: (error?: any) => {
          if (error) {
            console.error('Add card failed:', error);
          } else {
            navigation.navigate(WalletScreenNames.CardFailed);
          }
        },
      });
    } else {
      handleNavigation();
    }
  };
  useEffect(() => {
    if (userInfo) {
      onAnalyticsEvent(
        SubsAnalyticsEventSubsSubscribeAndSaveScreenOpen(
          getAnalyticsPayloadPaygWallet(userInfo, resubscribed),
        ),
      );
    }
  }, [userInfo, resubscribed, onAnalyticsEvent]);

  return (
    <>
      <S.MembershipOnboardContainer>
        <S.ScreenWrapper keyboardShouldPersistTaps="always">
          <HeaderWithBackground
            title={t.membershipOnboard.title.replace(
              '{{extraTariffDiscountPercentage}}',
              featureFlags.extraTariffDiscountPercentage.toString(),
            )}
            subTitle={subTitle}
            type="secondary"
          />
          <S.Wrapper>
            {sections(t, subscriptionAmount, userInfo, featureFlags).map(
              (section) => (
                <Section
                  key={section.header}
                  {...section}
                  resubscribed={resubscribed}
                  freeTrailHeader={t.membershipOnboard.points.title1}
                  welcomeMsg={t.membershipOnboard.points.welcomeTitle}
                  disableIntroOffer={featureFlags.disableIntroOffer}
                />
              ),
            )}
          </S.Wrapper>

          <S.TariffWrapper>
            <S.TariffHeader>{t.membershipOnboard.table.heading}</S.TariffHeader>
          </S.TariffWrapper>

          <S.Line />

          {tariffs(
            subscriptionAmount,
            userInfo?.country || UserCountry.DE,
            locale,
            featureFlags['subsChargeRateDC>50kW'],
            featureFlags['subsChargeRateDC<50kW'],
            featureFlags['subsChargeRateAC<22kW'],
            t.membershipOnboard.table,
          ).map((tariff, index) => {
            console.log('tariff', tariff);
            return (
              <>
                <S.TariffContentWrapper key={tariff.left.title}>
                  <S.LeftContent {...tariff.left.style}>
                    {tariff.left.title}
                  </S.LeftContent>
                  {tariff.right === '' ? (
                    tariff.right
                  ) : (
                    <S.RightContent>{tariff.right}</S.RightContent>
                  )}
                </S.TariffContentWrapper>
                {(index === 0 || tariff.hasEndLine) && <S.MidLine />}
              </>
            );
          })}

          <S.Line />
          {details(t).map(({ left, right }) => (
            <FootnoteDetail key={left} left={left} right={right} />
          ))}
          <FootnoteDetail
            left={t.membershipOnboard.smallText.key2}
            right={t.membershipOnboard.smallText.line2.replace(
              '{{extraTariffDiscountPercentage}}',
              featureFlags.extraTariffDiscountPercentage.toString(),
            )}
          />
          <FootnoteDetail
            left={t.membershipOnboard.smallText.key3}
            right={
              <>
                <CustomLinkParagraphComponent
                  text={t.membershipOnboard.smallText.hyperlink}
                  links={[
                    {
                      text: t.membershipOnboard.smallText.hyperlink_part_2,
                      url: tariffPricing,
                    },
                  ]}
                />
              </>
            }
            accessibilityHintMsg={
              t.membershipOnboard.smallText.hyperlinkPart2AccHint
            }
            isLink={true}
          />

          <FootnoteDetail right={t.membershipOnboard.smallText.line4} />
        </S.ScreenWrapper>
      </S.MembershipOnboardContainer>

      <S.BottomViewContainer>
        <Button
          testID="membershipOnboardBtn"
          type={ButtonAction.PRIMARY}
          onPress={onPressHandler}
          size={ButtonSize.XLARGE}
          accessibilityLabel={buttonAccessibilityLabel}
          accessibilityHint={buttonAccessibilityHint}
        >
          {buttonText}
        </Button>
      </S.BottomViewContainer>
    </>
  );
};

const Section: React.FC<MembershipSectionProps> = React.memo(
  ({
    Icon,
    header,
    content,
    alignTop,
    resubscribed,
    freeTrailHeader,
    welcomeMsg,
    disableIntroOffer,
  }) => {
    return resubscribed && header === freeTrailHeader ? (
      <S.Container>
        <S.WelcomeMsg>{welcomeMsg}</S.WelcomeMsg>
      </S.Container>
    ) : (
      ((!disableIntroOffer && header === freeTrailHeader) ||
        header !== freeTrailHeader) && (
        <S.Container>
          <S.IconContainer paddingTop={alignTop}>
            <Icon color={theme.subscriptionMfe?.text.color.tertiary} />
          </S.IconContainer>
          <S.ContainerWrapper>
            <S.InfoHeader>{header}</S.InfoHeader>
            <S.InfoContent>{content}</S.InfoContent>
          </S.ContainerWrapper>
        </S.Container>
      )
    );
  },
);

export default MembershipOnboard;
