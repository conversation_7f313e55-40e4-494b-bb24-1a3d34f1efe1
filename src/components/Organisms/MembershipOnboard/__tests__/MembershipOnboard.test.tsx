import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import {
  SubscriptionScreenNames,
  UserTypeEnum,
} from '../../../../common/enums';
import Theme from '../../../../themes/Theme';
import TRANSLATIONS from '../../../../translations/messages.json';
import { updateTranslations } from '../../../../utils/updateTranslations';
import MembershipOnboard from '../MembershipOnboard';

const mockTranslations = TRANSLATIONS;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'bp' }),
  user: { userId: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
  isInternetReachable: true,
  featureFlags: {
    introOfferCredit: 0,
    introOfferCreditDuration: 0,
    introPercentageDiscount: 20,
    subsChargingRatesPromo: 0.44,
    disableIntroOffer: false,
    savingRate: 55,
    subscriptionAmount: 7.49,
    extraTariffDiscountPercentage: 10,
  },
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockUserInfoObj = {
  userInfo: {
    type: UserTypeEnum.NEW,
    gocardless: {},
    country: 'DE',
  },
  isLoading: false,
  refetchUserInfo: jest.fn(),
};
const mockUserInfo = jest.fn();
jest.mock('../../../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfo(),
}));

const mockUseWalletObj = {
  selectedCard: undefined,
  triggerAddPaymentCardFlow: jest.fn(),
};

const mockUseWallet = jest.fn();

jest.mock('@bp/bppay-wallet-feature', () => ({
  useWallet: () => mockUseWallet(),
}));

const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

jest.mock('../../../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

const renderComponent = () =>
  render(
    <Theme>
      <MembershipOnboard />
    </Theme>,
  );

describe('MembershipOnboard component', () => {
  beforeEach(() => {
    mockUseWallet.mockReturnValue(mockUseWalletObj);
    mockUserInfo.mockReturnValue(mockUserInfoObj);
  });

  it('should render correctly ', () => {
    mockUserInfo.mockReturnValue(mockUserInfoObj);
    const { toJSON } = renderComponent();
    expect(toJSON()).toMatchSnapshot();
  });

  it('PAYG-wallet user should be navigated to the add payment screen when a payment card is not available', async () => {
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: {
        ...mockUserInfoObj.userInfo,
        type: UserTypeEnum.PAYG_WALLET,
        resubscribed: false,
      },
    });

    mockUseWallet.mockReturnValue({
      ...mockUseWalletObj,
      selectedCard: undefined,
    });

    mockHostNavigationObj.navigate.mockClear();
    const { getByText } = renderComponent();
    const button = getByText(
      mockTranslations.membershipOnboard.buttons.comfortMember.label,
    );
    fireEvent.press(button);
    expect(mockUseWalletObj.triggerAddPaymentCardFlow).toHaveBeenCalledWith({
      customNavigationKey: {
        name: SubscriptionScreenNames.MyComfortMembershipActivation,
        key: SubscriptionScreenNames.MyComfortMembershipActivation,
      },
      isSubsDefault: true,
      onAddCardUnsuccessfulAnalytics: expect.any(Function),
    });
  });
});
