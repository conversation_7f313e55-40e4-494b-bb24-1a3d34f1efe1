// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MembershipOnboard component should render correctly  1`] = `
[
  <RCTSafeAreaView
    style={
      {
        "backgroundColor": "#ffffff",
        "display": "flex",
        "flexBasis": 0,
        "flexDirection": "column",
        "flexGrow": 1,
        "flexShrink": 0,
        "paddingTop": 0,
      }
    }
  >
    <RCTScrollView
      bounces={false}
      contentContainerStyle={
        {
          "flexGrow": 1,
          "paddingTop": 0,
        }
      }
      keyboardShouldPersistTaps="always"
      style={
        {
          "display": "flex",
          "flexBasis": 0,
          "flexGrow": 1,
          "flexShrink": 1,
          "paddingTop": 0,
        }
      }
    >
      <View>
        <View
          accessibilityIgnoresInvertColors={true}
          style={
            {
              "justifyContent": "center",
            }
          }
        >
          <Image
            resizeMode="cover"
            source={
              {
                "testUri": "../../../assets/images/bgImage.png",
              }
            }
            style={
              [
                {
                  "bottom": 0,
                  "left": 0,
                  "position": "absolute",
                  "right": 0,
                  "top": 0,
                },
                {
                  "height": undefined,
                  "width": undefined,
                },
                undefined,
              ]
            }
          />
          <View
            style={
              {
                "alignItems": "center",
                "fontFamily": "Roboto-Medium",
                "justifyContent": "flex-start",
                "marginTop": 25,
                "paddingBottom": 0,
                "paddingLeft": 24,
                "paddingRight": 24,
                "paddingTop": 0,
                "width": "100%",
              }
            }
          >
            <Text
              style={
                {
                  "color": "#FFFFFF",
                  "fontSize": 38,
                  "letterSpacing": 0.4,
                  "lineHeight": 55,
                  "maxWidth": "unset",
                  "textAlign": "center",
                }
              }
              type="secondary"
            >
              Save at least 10% compared to Classic Tariff
            </Text>
            <Text
              style={
                {
                  "color": "#FFFFFF",
                  "fontFamily": "Roboto-Light",
                  "fontSize": 26,
                  "lineHeight": 39,
                  "marginBottom": 16,
                  "textAlign": "center",
                }
              }
            >
              only €7.49 a month
            </Text>
          </View>
        </View>
        <RCTSafeAreaView
          style={
            {
              "flexBasis": 0,
              "flexGrow": 1,
              "flexShrink": 0,
              "marginBottom": 24,
              "marginLeft": 24,
              "marginRight": 30,
              "marginTop": 10,
            }
          }
        >
          <RCTSafeAreaView
            style={
              {
                "flexBasis": 0,
                "flexDirection": "row",
                "flexGrow": 1,
                "flexShrink": 1,
                "marginRight": 16,
                "marginTop": 10,
              }
            }
          >
            <View
              paddingTop={true}
              style={
                {
                  "paddingRight": 6,
                  "paddingTop": 7,
                }
              }
            >
              <RNSVGSvgView
                bbHeight="24"
                bbWidth="24"
                focusable={false}
                height="24"
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": 24,
                      "width": 24,
                    },
                  ]
                }
                width="24"
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGGroup
                    fill={null}
                    propList={
                      [
                        "fill",
                        "stroke",
                      ]
                    }
                    stroke={null}
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          -592,
                          -1126,
                        ]
                      }
                    >
                      <RNSVGGroup
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        matrix={
                          [
                            1,
                            0,
                            0,
                            1,
                            588,
                            1122,
                          ]
                        }
                      >
                        <RNSVGPath
                          d="M12,4 C16.418278,4 20,7.581722 20,12 C20,16.418278 16.418278,20 12,20 C7.581722,20 4,16.418278 4,12 C4,7.581722 7.581722,4 12,4 Z M12,5 C8.13400675,5 5,8.13400675 5,12 C5,15.8659932 8.13400675,19 12,19 C15.8659932,19 19,15.8659932 19,12 C19,8.13400675 15.8659932,5 12,5 Z M16,12 C16.2761424,12 16.5,12.2238576 16.5,12.5 C16.5,14.9852814 14.4852814,17 12,17 C9.51471863,17 7.5,14.9852814 7.5,12.5 C7.5,12.2238576 7.72385763,12 8,12 L8,12 Z M15.463,13 L8.536,13 L8.54580909,13.0677182 C8.80701426,14.6691343 10.1550039,15.9054397 11.8079648,15.9948211 L11.8079648,15.9948211 L12,16 C13.739697,16 15.1829394,14.7307273 15.4541909,13.0677182 L15.4541909,13.0677182 L15.463,13 Z M9,8 C9.55228475,8 10,8.44771525 10,9 C10,9.55228475 9.55228475,10 9,10 C8.44771525,10 8,9.55228475 8,9 C8,8.44771525 8.44771525,8 9,8 Z M15,8 C15.5522847,8 16,8.44771525 16,9 C16,9.55228475 15.5522847,10 15,10 C14.4477153,10 14,9.55228475 14,9 C14,8.44771525 14.4477153,8 15,8 Z"
                          fill={
                            {
                              "payload": 4278190230,
                              "type": 0,
                            }
                          }
                          propList={
                            [
                              "fill",
                            ]
                          }
                        />
                      </RNSVGGroup>
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
            <View
              style={{}}
            >
              <Text
                style={
                  {
                    "color": "rgb(17, 17, 17)",
                    "fontFamily": "Roboto-Bold",
                    "fontSize": 16,
                    "fontWeight": "bold",
                    "lineHeight": 26,
                  }
                }
              >
                Free trial month
              </Text>
              <Text
                style={
                  {
                    "color": "rgb(17, 17, 17)",
                    "flexDirection": "column",
                    "fontFamily": "Roboto-Regular",
                    "fontSize": 13,
                    "letterSpacing": 0.2,
                    "lineHeight": 23,
                  }
                }
              >
                Enjoy your first month on us - then it's just €7.49 a month and you can cancel anytime.
              </Text>
            </View>
          </RCTSafeAreaView>
          <RCTSafeAreaView
            style={
              {
                "flexBasis": 0,
                "flexDirection": "row",
                "flexGrow": 1,
                "flexShrink": 1,
                "marginRight": 16,
                "marginTop": 10,
              }
            }
          >
            <View
              paddingTop={false}
              style={
                {
                  "paddingRight": 6,
                  "paddingTop": 0,
                }
              }
            >
              <RNSVGSvgView
                align="xMidYMid"
                bbHeight="24"
                bbWidth="24"
                focusable={false}
                height="24"
                meetOrSlice={0}
                minX={0}
                minY={0}
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": 24,
                      "width": 24,
                    },
                  ]
                }
                vbHeight={24}
                vbWidth={24}
                width="24"
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGGroup
                    fill={null}
                    propList={
                      [
                        "fill",
                      ]
                    }
                  >
                    <RNSVGPath
                      d="M0 0h24v24H0z"
                      fill={null}
                      propList={
                        [
                          "fill",
                        ]
                      }
                    />
                    <RNSVGPath
                      d="M14.777 15.076H9.05m7.51 1.348a1.856 1.856 0 1 1 0-3.712 1.856 1.856 0 0 1 0 3.712zm-9.302 0a1.856 1.856 0 1 1 0-3.712 1.856 1.856 0 0 1 0 3.712zM5.43 14.883l-1.429-.829v-3.197l.8-1.875-.4-.937c2.08-.245 4.144-.469 5.772-.469 2.582 0 4.739 1.132 6.626 2.813 2.628.528 3.2 1.814 3.2 2.343v2.344h-1.655"
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "stroke",
                        ]
                      }
                      stroke={
                        {
                          "payload": 4278190230,
                          "type": 0,
                        }
                      }
                    />
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
            <View
              style={{}}
            >
              <Text
                style={
                  {
                    "color": "rgb(17, 17, 17)",
                    "fontFamily": "Roboto-Bold",
                    "fontSize": 16,
                    "fontWeight": "bold",
                    "lineHeight": 26,
                  }
                }
              >
                Ideal if you drive undefined+ km a month¹
              </Text>
              <Text
                style={
                  {
                    "color": "rgb(17, 17, 17)",
                    "flexDirection": "column",
                    "fontFamily": "Roboto-Regular",
                    "fontSize": 13,
                    "letterSpacing": 0.2,
                    "lineHeight": 23,
                  }
                }
              >
                Charge just undefined kWh and your savings cover the monthly fee – more charging means more savings!​
              </Text>
            </View>
          </RCTSafeAreaView>
        </RCTSafeAreaView>
        <View
          style={
            {
              "marginBottom": 10,
              "marginLeft": 24,
              "marginRight": 10,
              "marginTop": 0,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Regular",
                "fontSize": 16,
                "letterSpacing": 0.25,
                "lineHeight": 23,
                "paddingLeft": 10,
              }
            }
          >
            My Extra Tariff
          </Text>
        </View>
        <View
          style={
            {
              "borderColor": "#ededed",
              "borderStyle": "solid",
              "borderWidth": 0.6,
              "marginBottom": 5,
              "marginLeft": 0,
              "marginRight": 0,
              "marginTop": 0,
            }
          }
        />
        <View
          style={
            {
              "flexDirection": "row",
              "justifyContent": "space-between",
              "marginBottom": 0,
              "marginLeft": 12,
              "marginRight": 12,
              "marginTop": 0,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "flexBasis": 0,
                "flexGrow": 1,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
                "letterSpacing": 0.15,
                "lineHeight": 28,
                "marginBottom": 12,
                "marginLeft": 12,
                "marginRight": 12,
                "marginTop": 12,
                "paddingLeft": 10,
                "textAlign": "left",
                "width": "70%",
              }
            }
          >
            Monthly fee
          </Text>
          <Text
            style={
              {
                "alignSelf": "center",
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Medium",
                "fontSize": 13,
                "letterSpacing": 0.15,
                "lineHeight": 28,
                "marginBottom": 12,
                "marginLeft": 12,
                "marginRight": 12,
                "marginTop": 12,
                "textAlign": "right",
                "width": "40%",
              }
            }
          >
            €7.49
          </Text>
        </View>
        <View
          style={
            {
              "borderColor": "#ededed",
              "borderStyle": "solid",
              "borderWidth": 0.8,
              "marginBottom": 0,
              "marginLeft": 30,
              "marginRight": 20,
              "marginTop": 0,
            }
          }
        />
        <View
          style={
            {
              "flexDirection": "row",
              "justifyContent": "space-between",
              "marginBottom": 0,
              "marginLeft": 12,
              "marginRight": 12,
              "marginTop": 0,
            }
          }
        >
          <Text
            fontFamily="Roboto-Medium"
            fontSize="13px"
            style={
              {
                "color": "rgb(17, 17, 17)",
                "flexBasis": 0,
                "flexGrow": 1,
                "flexShrink": 1,
                "fontFamily": "Roboto-Medium",
                "fontSize": 13,
                "letterSpacing": 0.15,
                "lineHeight": 28,
                "marginBottom": 12,
                "marginLeft": 12,
                "marginRight": 12,
                "marginTop": 12,
                "paddingLeft": 10,
                "textAlign": "left",
                "width": "70%",
              }
            }
          >
            Current charging rates in Extra Tariff at Aral pulse in Germany ² ³
          </Text>
        </View>
        <View
          style={
            {
              "borderColor": "#ededed",
              "borderStyle": "solid",
              "borderWidth": 0.8,
              "marginBottom": 0,
              "marginLeft": 30,
              "marginRight": 20,
              "marginTop": 0,
            }
          }
        />
        <View
          style={
            {
              "flexDirection": "row",
              "justifyContent": "space-between",
              "marginBottom": 0,
              "marginLeft": 12,
              "marginRight": 12,
              "marginTop": 0,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "flexBasis": 0,
                "flexGrow": 1,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
                "letterSpacing": 0.15,
                "lineHeight": 28,
                "marginBottom": 12,
                "marginLeft": 12,
                "marginRight": 12,
                "marginTop": 12,
                "paddingLeft": 10,
                "textAlign": "left",
                "width": "70%",
              }
            }
          >
            DC (more than 50kW)
          </Text>
          <Text
            style={
              {
                "alignSelf": "center",
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Medium",
                "fontSize": 13,
                "letterSpacing": 0.15,
                "lineHeight": 28,
                "marginBottom": 12,
                "marginLeft": 12,
                "marginRight": 12,
                "marginTop": 12,
                "textAlign": "right",
                "width": "40%",
              }
            }
          >
            €NaN/kWh
          </Text>
        </View>
        <View
          style={
            {
              "flexDirection": "row",
              "justifyContent": "space-between",
              "marginBottom": 0,
              "marginLeft": 12,
              "marginRight": 12,
              "marginTop": 0,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "flexBasis": 0,
                "flexGrow": 1,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
                "letterSpacing": 0.15,
                "lineHeight": 28,
                "marginBottom": 12,
                "marginLeft": 12,
                "marginRight": 12,
                "marginTop": 12,
                "paddingLeft": 10,
                "textAlign": "left",
                "width": "70%",
              }
            }
          >
            DC (50kW or less)
          </Text>
          <Text
            style={
              {
                "alignSelf": "center",
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Medium",
                "fontSize": 13,
                "letterSpacing": 0.15,
                "lineHeight": 28,
                "marginBottom": 12,
                "marginLeft": 12,
                "marginRight": 12,
                "marginTop": 12,
                "textAlign": "right",
                "width": "40%",
              }
            }
          >
            €NaN/kWh
          </Text>
        </View>
        <View
          style={
            {
              "flexDirection": "row",
              "justifyContent": "space-between",
              "marginBottom": 0,
              "marginLeft": 12,
              "marginRight": 12,
              "marginTop": 0,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "flexBasis": 0,
                "flexGrow": 1,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
                "letterSpacing": 0.15,
                "lineHeight": 28,
                "marginBottom": 12,
                "marginLeft": 12,
                "marginRight": 12,
                "marginTop": 12,
                "paddingLeft": 10,
                "textAlign": "left",
                "width": "70%",
              }
            }
          >
            AC (22kW or less)
          </Text>
          <Text
            style={
              {
                "alignSelf": "center",
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Medium",
                "fontSize": 13,
                "letterSpacing": 0.15,
                "lineHeight": 28,
                "marginBottom": 12,
                "marginLeft": 12,
                "marginRight": 12,
                "marginTop": 12,
                "textAlign": "right",
                "width": "40%",
              }
            }
          >
            €NaN/kWh
          </Text>
        </View>
        <View
          style={
            {
              "borderColor": "#ededed",
              "borderStyle": "solid",
              "borderWidth": 0.6,
              "marginBottom": 5,
              "marginLeft": 0,
              "marginRight": 0,
              "marginTop": 0,
            }
          }
        />
        <View
          style={
            {
              "flexDirection": "row",
              "marginBottom": 2,
              "marginLeft": 24,
              "marginRight": 24,
              "marginTop": 2,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            ¹
          </Text>
          <Text
            accessibilityHint=""
            accessibilityRole="text"
            accessible={true}
            importantForAccessibility="yes"
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            Based on an average consumption of 20kWh/100km and charging at DC (more than 50kW) Aral pulse chargers in Germany compared to Classic Tariff.
          </Text>
        </View>
        <View
          style={
            {
              "flexDirection": "row",
              "marginBottom": 2,
              "marginLeft": 24,
              "marginRight": 24,
              "marginTop": 2,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            ²
          </Text>
          <Text
            accessibilityHint=""
            accessibilityRole="text"
            accessible={true}
            importantForAccessibility="yes"
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            Your energy price is always at least 10% cheaper than our Classic Tariff.
          </Text>
        </View>
        <View
          style={
            {
              "flexDirection": "row",
              "marginBottom": 2,
              "marginLeft": 24,
              "marginRight": 24,
              "marginTop": 2,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            ³
          </Text>
          <Text
            accessibilityHint=" Double tap to activate the embedded link"
            accessibilityRole="link"
            accessible={true}
            importantForAccessibility="yes"
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            Different conditions apply abroad and at third-party providers. 
            <Text
              accessibilityLabel=" Get more information about all tariffs"
              accessibilityRole="button"
              onPress={[Function]}
              style={
                {
                  "alignItems": "flex-start",
                  "color": "#000096",
                  "fontSize": 12,
                  "textAlign": "center",
                  "textDecorationColor": "black",
                  "textDecorationLine": "underline",
                  "textDecorationStyle": "solid",
                }
              }
            >
               Get more information about all tariffs
            </Text>
             on our website
          </Text>
        </View>
        <View
          style={
            {
              "flexDirection": "row",
              "marginBottom": 2,
              "marginLeft": 24,
              "marginRight": 24,
              "marginTop": 2,
            }
          }
        >
          <Text
            accessibilityHint=""
            accessibilityRole="text"
            accessible={true}
            importantForAccessibility="yes"
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            Prices include VAT. The Extra Tariff cannot be combined with other special tariffs (e.g. ADAC e-Charge Tariff).
          </Text>
        </View>
      </View>
    </RCTScrollView>
  </RCTSafeAreaView>,
  <View
    style={
      {
        "backgroundColor": "white",
        "marginBottom": 0,
        "paddingBottom": 30,
        "paddingLeft": 24,
        "paddingRight": 24,
        "paddingTop": 20,
        "shadowColor": "#000096",
        "shadowOffset": {
          "height": 50,
          "width": 0,
        },
        "shadowOpacity": 1,
        "shadowRadius": 40,
        "width": "100%",
      }
    }
  >
    <View
      accessibilityHint="Navigates to the Activation page, showing a summary of the membership details."
      accessibilityLabel="Become an Extra Tariff now"
      accessibilityRole="button"
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": false,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "backgroundColor": "#000096",
          "borderBottomLeftRadius": 28,
          "borderBottomRightRadius": 28,
          "borderColor": "transparent",
          "borderStyle": "solid",
          "borderTopLeftRadius": 28,
          "borderTopRightRadius": 28,
          "borderWidth": 0,
          "justifyContent": "center",
          "minHeight": 56,
          "opacity": 1,
          "paddingHorizontal": 29.5,
          "paddingVertical": 15,
        }
      }
      testID="membershipOnboardBtn"
    >
      <View>
        <Text
          disabled={false}
          inverted={false}
          size="xlarge"
          style={
            {
              "color": "#ffffff",
              "fontFamily": "Roboto-Regular",
              "fontSize": 16,
              "letterSpacing": 0.7,
              "textAlign": "center",
            }
          }
          type="primary"
        >
          Book the Extra Tariff now
        </Text>
      </View>
    </View>
  </View>,
]
`;
