import { SupportedLocales } from '@bp/pulse-shared-types/lib/types/SupportedLocales';

import { UserCountry } from '../../../common/enums';
import { featureFlags, IUserInfo } from '../../../common/interfaces';
import { ITranslation } from '../../../translations/schema';
import { formatCurrency, getCurrency } from '../../../utils/helpers';
import { CarSideNormal, PolarPayAsUGoIcon, PolarPomIcon } from '../../Atoms';

interface Section {
  Icon: any;
  header: string;
  content: string;
  alignTop: boolean;
}

export const sections = (
  t: ITranslation,
  subscriptionAmount: string,
  userInfo: IUserInfo | undefined,
  featureFlags: featureFlags,
): Section[] => {
  return [
    {
      Icon: PolarPomIcon,
      header: t.membershipOnboard.points.title1,
      content: t.membershipOnboard.points.text1.replace(
        '{{amount}}',
        subscriptionAmount,
      ),
      alignTop: true,
    },
    ...(userInfo?.country !== UserCountry.DE
      ? [
          {
            Icon: PolarPayAsUGoIcon,
            header: t.membershipOnboard.points.title2,
            content: t.membershipOnboard.points.text2,
            alignTop: true,
          },
        ]
      : []),
    {
      Icon: CarSideNormal,
      header: t.membershipOnboard.points.title3?.replace(
        '{{subsDrivingDistancekm}}',
        featureFlags.subsDrivingDistancekm,
      ),
      content: t.membershipOnboard.points.text3?.replace(
        '{{subsChargekWh}}',
        featureFlags.subsChargekWh,
      ),
      alignTop: false,
    },
  ];
};

export const tariffs = (
  subscriptionAmount: string,
  country: UserCountry,
  locale: SupportedLocales,
  subsChargeRateDC50kWmore: number,
  subsChargeRateDC50kWless: number,
  subsChargeRateAC22kW: number,
  translationTableObj: any,
) => {
  return [
    {
      left: {
        title: translationTableObj.line1,
      },
      right: subscriptionAmount,
    },
    {
      left: {
        title: translationTableObj.line2,
        style: {
          fontSize: '13px',
          fontFamily: 'Roboto-Medium',
        },
      },
      right: '',
      hasEndLine: true,
    },
    {
      left: { title: translationTableObj['rate>50kWTitle'] },
      right: translationTableObj.ratekWValue.replace(
        '{{rate}}',
        formatCurrency({
          amount: subsChargeRateDC50kWmore,
          currency: getCurrency(country),
          locale,
        }),
      ),
    },
    {
      left: { title: translationTableObj['rate<50kWTitle'] },
      right: translationTableObj.ratekWValue.replace(
        '{{rate}}',
        formatCurrency({
          amount: subsChargeRateDC50kWless,
          currency: getCurrency(country),
          locale,
        }),
      ),
    },
    {
      left: { title: translationTableObj['rate<22kWTitle'] },
      right: translationTableObj.ratekWValue.replace(
        '{{rate}}',
        formatCurrency({
          amount: subsChargeRateAC22kW,
          currency: getCurrency(country),
          locale,
        }),
      ),
    },
  ];
};

export const details = (t: ITranslation) => {
  return [
    {
      left: t.membershipOnboard.smallText.key1,
      right: t.membershipOnboard.smallText.line1,
    },
  ];
};
