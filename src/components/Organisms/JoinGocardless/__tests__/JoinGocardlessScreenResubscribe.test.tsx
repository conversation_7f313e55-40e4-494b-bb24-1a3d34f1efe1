import { theme } from '@bp/ui-components/mobile/core';
import {
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react-native';
import React from 'react';

import {
  MandateStatus,
  SubscriptionScreenNames,
  UserTypeEnum,
} from '../../../../common/enums';
import Theme from '../../../../themes/Theme';
import TRANSLATIONS from '../../../../translations/messages.json';
import { updateTranslations } from '../../../../utils/updateTranslations';
import JoinGocardlessScreen from '../JoinGocardless';

const mockTranslations = TRANSLATIONS;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'bp' }),
  user: { id: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
  isInternetReachable: true,
  featureFlags: {
    introOfferCredit: 0,
    introOfferCreditDuration: 0,
    introPercentageDiscount: 20,
    disableIntroOffer: false,
    savingRate: 55,
  },
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockUserInfoObj = {
  userInfo: {
    type: UserTypeEnum.PAYG,
    gocardless: { mandateStatus: MandateStatus.INACTIVE, mandateId: 'mock' },
  },
  resubscribed: true,
  isLoading: false,
};
const mockUserInfo = jest.fn();
jest.mock('../../../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfo(),
}));

const mockUseWalletObj = {
  selectedCard: undefined,
  triggerAddPaymentCardFlow: jest.fn(),
};

const mockUseWallet = jest.fn();

jest.mock('@bp/bppay-wallet-feature', () => ({
  useWallet: () => mockUseWallet(),
}));

const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

jest.mock('../../../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

const params = { from: 'account' };

const renderComponent = () =>
  render(
    <Theme customTheme={theme}>
      <JoinGocardlessScreen route={params} />
    </Theme>,
  );

describe('JoinGocardlessScreen component', () => {
  beforeAll(() => {
    mockUseWallet.mockReturnValue(mockUseWalletObj);
    mockUserInfo.mockReturnValue(mockUserInfoObj);
  });
  it('should render correctly from account', () => {
    const { toJSON } = renderComponent();

    expect(toJSON()).toMatchSnapshot();
  });

  it('should render correctly for resubscriber', async () => {
    renderComponent();
    await waitFor(() => {
      expect(
        screen.getByText(
          mockUseSettingsObj.t.gocardless.buttons.RESTARTSUBSCRIPTION,
        ),
      ).toBeTruthy();
    });
  });

  it('PAYG-wallet user should be navigated to confirm subscription screen when payment method is active', async () => {
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: {
        ...mockUserInfoObj.userInfo,
        type: UserTypeEnum.PAYG_WALLET,
        resubscribed: true,
      },
    });
    mockUseWallet.mockReturnValue({
      ...mockUseWalletObj,
      selectedCard: '1234',
    });
    mockHostNavigationObj.navigate.mockClear();
    const { getByText } = renderComponent();
    await waitFor(() => {
      const button = getByText(
        mockTranslations.gocardless.buttons.RESTARTSUBSCRIPTION,
      );

      fireEvent(button, 'onPress');

      expect(mockHostNavigationObj.navigate).toHaveBeenCalledWith(
        SubscriptionScreenNames.ConfirmSubscription,
      );
    });
  });

  it('PAYG-wallet user should not be navigated to confirm subscription screen when payment method is null', async () => {
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: {
        ...mockUserInfoObj.userInfo,
        type: UserTypeEnum.PAYG_WALLET,
        resubscribed: true,
      },
    });

    mockUseWallet.mockReturnValue({
      ...mockUseWalletObj,
      selectedCard: undefined,
    });

    mockHostNavigationObj.navigate.mockClear();
    const { getByText } = renderComponent();

    await waitFor(() => {
      const button = getByText(
        mockTranslations.gocardless.buttons.RESTARTSUBSCRIPTION,
      );

      fireEvent(button, 'onPress');

      expect(mockHostNavigationObj.navigate).not.toHaveBeenCalled();
    });
  });
});
