import {
  act,
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react-native';
import React from 'react';
import { Linking } from 'react-native';

import {
  SubscriptionScreenNames,
  UserTypeEnum,
} from '../../../../common/enums';
import Theme from '../../../../themes/Theme';
import TRANSLATIONS from '../../../../translations/messages.json';
import { updateTranslations } from '../../../../utils/updateTranslations';
import JoinGocardlessScreen from '../JoinGocardless';
const mockTranslations = TRANSLATIONS;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'bp' }),
  user: { userId: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
  isInternetReachable: true,
  featureFlags: {
    introOfferCredit: 0,
    introOfferCreditDuration: 0,
    introPercentageDiscount: 20,
    subsChargingRatesPromo: 0.44,
    disableIntroOffer: false,
    savingRate: 55,
  },
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockUserInfoObj = {
  userInfo: {
    type: UserTypeEnum.NEW,
    gocardless: {},
    country: 'UK',
  },
  isLoading: false,
  refetchUserInfo: jest.fn(),
};
const mockUserInfo = jest.fn();
jest.mock('../../../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfo(),
}));

const mockUseWalletObj = {
  selectedCard: undefined,
  triggerAddPaymentCardFlow: jest.fn(),
};

const mockUseWallet = jest.fn();

jest.mock('@bp/bppay-wallet-feature', () => ({
  useWallet: () => mockUseWallet(),
}));

const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

jest.mock('../../../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

function mockSuccessLinking() {
  const canOpenURL = jest
    .spyOn(Linking, 'canOpenURL')
    .mockImplementation(() => Promise.resolve(true));
  const openURL = jest
    .spyOn(Linking, 'openURL')
    .mockImplementation(() => Promise.resolve(true));

  return { canOpenURL, openURL };
}

const params = { from: 'registration' };

const renderComponent = () =>
  render(
    <Theme>
      <JoinGocardlessScreen route={params} />
    </Theme>,
  );

describe('JoinGocardlessScreen component', () => {
  beforeAll(() => {
    mockUseWallet.mockReturnValue(mockUseWalletObj);
    mockUserInfo.mockReturnValue(mockUserInfoObj);
  });
  it('should render correctly from registration', () => {
    mockUserInfo.mockReturnValue(mockUserInfoObj);
    const { toJSON } = renderComponent();
    expect(toJSON()).toMatchSnapshot();
  });

  it('should render correctly for initial subscriber', async () => {
    mockUserInfo.mockReturnValue(mockUserInfoObj);
    renderComponent();

    await waitFor(() => {
      expect(
        screen.getByText(
          mockUseSettingsObj.t.gocardless.text.SUBSCRIBE_TITLE.replace(
            '{{percentageDiscount}}',
            mockUseSettingsObj.featureFlags.introPercentageDiscount.toString(),
          ),
        ),
      ).toBeTruthy();

      expect(
        screen.getByText(
          mockUseSettingsObj.t.gocardless.text.SUBSCRIBE_SUBBANNER,
        ),
      ).toBeTruthy();

      expect(
        screen.getByText(mockUseSettingsObj.t.gocardless.text.JOIN_TANDC),
      ).toBeTruthy();

      expect(
        screen.getByText(mockUseSettingsObj.t.gocardless.buttons.SETUPPAYMENT),
      ).toBeTruthy();
    });
  });

  it('should navigate to RFID form', async () => {
    renderComponent();

    await waitFor(() => {
      const setupButton = screen.getByText(
        mockUseSettingsObj.t.gocardless.buttons.SETUPPAYMENT,
      );

      fireEvent(setupButton, 'onPress');

      expect(mockHostNavigationObj.navigate).toHaveBeenCalled();
    });
  });

  it('should navigate to terms and conditions', async () => {
    const { canOpenURL, openURL } = mockSuccessLinking();

    renderComponent();

    await waitFor(() => {
      const tAndCLink = screen.getByText(
        mockUseSettingsObj.t.gocardless.text.JOIN_TANDC,
      );

      fireEvent(tAndCLink, 'onPress');

      expect(canOpenURL).toHaveBeenCalled();
      expect(openURL).toHaveBeenCalled();
    });
  });

  it('should navigate to compare rates', async () => {
    const { canOpenURL, openURL } = mockSuccessLinking();

    renderComponent();

    await waitFor(() => {
      const compareRatesLink = screen.getByText('Compare rates.');

      fireEvent(compareRatesLink, 'onPress');

      expect(canOpenURL).toHaveBeenCalled();
      expect(openURL).toHaveBeenCalled();
    });
  });

  it('should have the right texts for payg-wallet first time subscription', async () => {
    mockUserInfo.mockReturnValueOnce({
      ...mockUserInfoObj,
      userInfo: {
        ...mockUserInfoObj.userInfo,
        type: UserTypeEnum.PAYG_WALLET,
        resubscribed: false,
      },
    });
    const { queryByText } = renderComponent();
    act(() => {
      jest.useFakeTimers();
    });

    await waitFor(() => {
      expect(
        queryByText(mockUseSettingsObj.t.gocardless.text.SUBSCRIBE_SUBBANNER),
      ).toBeNull();

      expect(
        queryByText(
          mockUseSettingsObj.t.gocardless.paygWallet.SUBSCRIBE_TITLE_2,
        ),
      ).toBeDefined();
    });
  });

  it('PAYG-wallet user should be navigated to confirm subscription screen when payment method is active', async () => {
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: {
        ...mockUserInfoObj.userInfo,
        type: UserTypeEnum.PAYG_WALLET,
        resubscribed: false,
      },
    });

    mockUseWallet.mockReturnValue({
      ...mockUseWalletObj,
      selectedCard: '1234',
    });

    mockHostNavigationObj.navigate.mockClear();
    const { getByText } = renderComponent();

    await waitFor(() => {
      const button = getByText(
        mockTranslations.gocardless.buttons.SETUPPAYMENT,
      );
      fireEvent(button, 'onPress');
      expect(mockHostNavigationObj.navigate).toHaveBeenCalledWith(
        SubscriptionScreenNames.ConfirmSubscription,
      );
    });
  });

  it('PAYG-wallet user should not be navigated to confirm subscription screen when payment method is null', async () => {
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: {
        ...mockUserInfoObj.userInfo,
        type: UserTypeEnum.PAYG_WALLET,
        resubscribed: false,
      },
    });
    mockUseWallet.mockReturnValue({
      ...mockUseWalletObj,
      selectedCard: undefined,
    });

    mockHostNavigationObj.navigate.mockClear();
    const { getByText } = renderComponent();

    await waitFor(() => {
      const button = getByText(
        mockTranslations.gocardless.buttons.SETUPPAYMENT,
      );

      fireEvent(button, 'onPress');

      expect(mockHostNavigationObj.navigate).not.toHaveBeenCalled();
    });
  });
});
