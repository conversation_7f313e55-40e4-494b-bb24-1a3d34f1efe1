import { Dimensions } from 'react-native';
import { styled } from 'styled-components/native';

const phoneWidth = Dimensions.get('screen').width > 320 ? 24 : 8;

export const JoinGoCardlessContainer = styled.ScrollView`
  flex: 1;
  flex-direction: column;
  top: 0;
`;

export const HeaderPosition = styled.SafeAreaView`
  margin-top: 16px;
`;

export const MiddleViewContainer = styled.View`
  justify-content: center;
  align-items: center;
  padding-right: ${phoneWidth}px;
  padding-left: ${phoneWidth}px;
`;

export const SubBannerText = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.black};
  font-size: 20px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const TableContainer = styled.View`
  margin-bottom: 25%;
`;

export const BottomViewContainer = styled.View`
  background-color: white;
  width: 100%;
  padding: 16px ${phoneWidth}px;
  margin-bottom: 0;
  position: absolute;
  bottom: 0;
  box-shadow: 0px 50px 40px ${(p: any) => p.theme.subscriptionMfe.color.primary};
`;

export const BottomTandC = styled.TouchableOpacity`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 3px;
`;

export const LinkText = styled.Text`
  text-align: center;
  font-size: 12px;
  align-items: flex-start;
  color: ${(p: any) => p.theme.subscriptionMfe.buttons.secondary.text};
`;

export const TableRow = styled.View`
  flex-direction: row;
  align-items: flex-start;
  padding: 0 16px;
`;

export const LeftImageView = styled.View<{ type?: string }>`
  width: 20px;
  padding-top: 8px;
  margin-right: 8px;
`;

export const TableText = styled.View`
  padding: 0 24px 12px 0;
`;

export const TableTitle = styled.Text`
  text-align: left;
  font-size: 16px;
  font-weight: bold;
  line-height: 28px;
  color: ${(p: any) => p.theme.subscriptionMfe.color.black2};
`;

export const TableSubTitle = styled.Text`
  text-align: left;
  font-size: 13px;
  line-height: 24px;
  color: ${(p: any) => p.theme.subscriptionMfe.color.black2};
`;

export const Underline = styled.Text`
  text-align: center;
  font-size: 13px;
  align-items: flex-start;
  color: ${(p: any) => p.theme.subscriptionMfe.color.primary};
  text-decoration: underline;
  text-decoration-color: ${(p: any) => p.theme.subscriptionMfe.color.primary};
`;

export const ErrorText = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.black};
  font-size: 18px;
  letter-spacing: 0;
  line-height: 19px;
  text-align: center;
  margin-top: 16px;
`;

export const HighlightTitleText = styled.Text`
  color: ${(props) => props.theme.subscriptionMfe.color.white};
  font-size: 38px;
  line-height: 55px;
  text-align: center;
`;
export const LinkViewContainer = styled.View`
  justify-content: center;
`;
