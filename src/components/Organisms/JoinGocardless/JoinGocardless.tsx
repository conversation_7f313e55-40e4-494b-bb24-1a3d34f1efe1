import { useWallet } from '@bp/bppay-wallet-feature';
import {
  <PERSON><PERSON>,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import React, { useCallback, useEffect, useState } from 'react';
import { Linking } from 'react-native';

import {
  SubsAnalyticsEventIntroOfferScreenOpen,
  SubsAnalyticsEventReactivateSubsScreenOpen,
  SubsAnalyticsEventSubsSubscribeAndSaveScreenCTAClick,
  SubsAnalyticsEventSubsSubscribeAndSaveScreenOpen,
  SubsAnalyticsEventSubsWalletAddCardFailure,
  SubsAnalyticsSubsWalletAddCardSuccess,
} from '../../../analytics/events';
import {
  AppRoute,
  PartnerTypeEnum,
  PulseDataItemIds,
  SubscriptionScreenNames,
  UserTypeEnum,
} from '../../../common/enums';
import * as I from '../../../common/interfaces';
import { LinkParagraphComponent, Spacer } from '../../../components';
import { useHostNavigation } from '../../../providers/HostNavigationProvider';
import { useSettings } from '../../../providers/Settings';
import { useSubscriptionContext } from '../../../providers/SubscriptionProvider';
import { useUserInfo } from '../../../providers/UserInfoProvider';
import { ITranslation } from '../../../translations/schema';
import {
  formatCurrency,
  getAnalyticsPayload,
  getAnalyticsPayloadPaygWallet,
  getAnalyticsPayloadPaygWalletClick,
  getCurrency,
} from '../../../utils/helpers';
import { HeaderWithBackground } from '../../Atoms/HeaderWithBackground/HeaderWithBackground';
import { LoadingPage } from '../LoadingPage/LoadingPage';
import { goCardlessInput } from './JoinGocardless.functions';
import * as S from './JoinGocardless.styles';

interface GoCardlessData {
  id: string;
  title: string;
  icon: any;
  underlineText: string;
  subTitle: string;
  tariffPricing: string;
}
const getSubscriptionButtonLabel = (
  resubscribed: boolean | undefined,
  t: ITranslation,
) => {
  return resubscribed
    ? t.gocardless.buttons.RESTARTSUBSCRIPTION
    : t.gocardless.buttons.SETUPPAYMENT;
};
const getHeaderTitle = (
  userInfoType: string,
  t: ITranslation,
  featureFlags: I.featureFlags,
  resubscribed: boolean | undefined,
) => {
  if (!resubscribed && userInfoType === UserTypeEnum.PAYG_WALLET) {
    return `${t.gocardless.paygWallet.SUBSCRIBE_TITLE_1} ${featureFlags.introPercentageDiscount}${t.gocardless.paygWallet.SUBSCRIBE_TITLE_TEXT}`;
  } else if (resubscribed && userInfoType === UserTypeEnum.PAYG_WALLET) {
    return `${t.gocardless.paygWallet.SUBSCRIBE_TITLE_1} ${featureFlags.introPercentageDiscount}${t.gocardless.paygWallet.RESUBSCRIBE_TITLE_TEXT}`;
  }

  return t.gocardless.text.SUBSCRIBE_TITLE.replace(
    '{{percentageDiscount}}',
    featureFlags.introPercentageDiscount.toString(),
  );
};
const openUrlOnPress = async (url: string) => {
  const supported = await Linking.canOpenURL(url);
  if (supported) {
    await Linking.openURL(url);
  } else {
    console.error('error');
  }
};

const handleCompareRatesPress = (tariffPricing: string) =>
  openUrlOnPress(tariffPricing);
const renderItem = ({
  id,
  title,
  icon,
  underlineText,
  subTitle,
  tariffPricing,
}: GoCardlessData) => {
  return (
    <S.TableRow key={id} accessible={id === PulseDataItemIds.LOWEST_RATES}>
      <S.LeftImageView>{icon}</S.LeftImageView>
      <S.TableText>
        <S.TableTitle>{title}</S.TableTitle>
        {subTitle && (
          <S.TableSubTitle>
            {`${subTitle} `}
            {underlineText !== '' && (
              <S.Underline
                onPress={
                  id === PulseDataItemIds.LOWEST_RATES
                    ? () => handleCompareRatesPress(tariffPricing)
                    : undefined
                }
              >
                {underlineText}
              </S.Underline>
            )}
          </S.TableSubTitle>
        )}
      </S.TableText>
    </S.TableRow>
  );
};

const getHeaderDescription = (
  userInfoType: string,
  t: ITranslation,
  billingItems: any[],
  userCountry: string,
  resubscribed: boolean | undefined,
) => {
  if (!resubscribed && userInfoType === UserTypeEnum.PAYG_WALLET) {
    const amount = billingItems[0]?.billingAmount?.amount;
    const currency = getCurrency(userCountry);

    return t.gocardless.paygWallet.SUBSCRIBE_SUBTITLE.replace(
      '{{amount}}',
      formatCurrency({ amount, currency }),
    );
  }
  if (resubscribed && userInfoType === UserTypeEnum.PAYG_WALLET) {
    const amount = billingItems[0]?.billingAmount?.amount;
    const currency = getCurrency(userCountry);

    return t.gocardless.paygWallet.RESUBSCRIBE_SUBTITLE.replace(
      '{{amount}}',
      formatCurrency({ amount, currency }),
    );
  }

  return '';
};

export default ({ route }: any) => {
  const { params } = route;
  const {
    t,
    onAnalyticsEvent,
    emspTermsAndConditions,
    tariffPricing,
    isInternetReachable,
    featureFlags,
    onNavigateToADACUnlink,
  } = useSettings();
  const { userInfo, resubscribed } = useUserInfo();
  const { getSubsPlan } = useSubscriptionContext();

  const { selectedCard, triggerAddPaymentCardFlow } = useWallet();
  const { navigate } = useHostNavigation();
  const from = params?.from || AppRoute.account;
  const hasActiveCard = selectedCard != null;
  const [billingItems, setBillingItems] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSubsPlan = async () => {
      try {
        const result = await getSubsPlan();
        const plans = result?.data?.getSubsPlan?.plans || [];
        plans.length && setBillingItems(plans);
      } catch (error) {
        console.error('Error fetching subscription plan:', error);
      }
      setIsLoading(false);
    };

    fetchSubsPlan();
    onAnalyticsEvent(
      SubsAnalyticsEventSubsSubscribeAndSaveScreenOpen(
        getAnalyticsPayloadPaygWallet(userInfo, resubscribed),
      ),
    );
    resubscribed
      ? onAnalyticsEvent(
          SubsAnalyticsEventReactivateSubsScreenOpen(
            getAnalyticsPayload(userInfo),
          ),
        )
      : onAnalyticsEvent(
          SubsAnalyticsEventIntroOfferScreenOpen(getAnalyticsPayload(userInfo)),
        );
  }, [
    userInfo,
    onAnalyticsEvent,
    resubscribed,
    isInternetReachable,
    getSubsPlan,
  ]);

  const handleTermsAndConditionsPress = () =>
    openUrlOnPress(emspTermsAndConditions);

  const onPressSetupSubscription = useCallback(() => {
    if (userInfo?.partnerType === PartnerTypeEnum.ADAC) {
      onNavigateToADACUnlink();
      return;
    }
    if (userInfo?.type === UserTypeEnum.PAYG_WALLET) {
      getSubsPlan();

      onAnalyticsEvent(
        SubsAnalyticsEventSubsSubscribeAndSaveScreenCTAClick(
          getAnalyticsPayloadPaygWalletClick(
            userInfo,
            resubscribed,
            !!selectedCard,
          ),
        ),
      );
      if (!selectedCard) {
        triggerAddPaymentCardFlow({
          isSubsDefault: true,
          customNavigationKey: {
            name: SubscriptionScreenNames.ConfirmSubscription,
            key: SubscriptionScreenNames.ConfirmSubscription,
          },
          onAddCardSuccessfulAnalytics: () => {
            !hasActiveCard &&
              onAnalyticsEvent(
                SubsAnalyticsSubsWalletAddCardSuccess({
                  first_time_subscribing: !resubscribed,
                }),
              );
          },
          onAddCardUnsuccessfulAnalytics: () => {
            !hasActiveCard &&
              onAnalyticsEvent(
                SubsAnalyticsEventSubsWalletAddCardFailure({
                  errorMessage: 'error',
                }),
              );
          },
        });
        return;
      } else {
        navigate(SubscriptionScreenNames.ConfirmSubscription);
        return;
      }
    }

    navigate(
      (userInfo?.balance ?? 0) >= 0
        ? SubscriptionScreenNames.RFIDForm
        : SubscriptionScreenNames.PaymentRequired,
      {
        fromScreen: from,
        logSubsFlow: true,
      },
    );
  }, [
    from,
    getSubsPlan,
    hasActiveCard,
    navigate,
    onAnalyticsEvent,
    resubscribed,
    selectedCard,
    triggerAddPaymentCardFlow,
    userInfo,
    onNavigateToADACUnlink,
  ]);
  return (
    <>
      {isLoading && <LoadingPage text={t.gocardless.text.LOADING} />}
      {!isLoading && userInfo?.type && (
        <>
          <S.JoinGoCardlessContainer>
            <>
              <HeaderWithBackground
                title={getHeaderTitle(
                  userInfo.type,
                  t,
                  featureFlags,
                  resubscribed,
                )}
                description={getHeaderDescription(
                  userInfo?.type,
                  t,
                  billingItems,
                  userInfo?.country,
                  resubscribed,
                )}
                type={'secondary'}
              />
              {!(userInfo?.type === UserTypeEnum.PAYG_WALLET) && (
                <S.MiddleViewContainer>
                  <Spacer vSpace={40} />
                  <S.SubBannerText>
                    {t.gocardless.text.SUBSCRIBE_SUBBANNER}
                  </S.SubBannerText>
                  <Spacer vSpace={24} />
                </S.MiddleViewContainer>
              )}
              {userInfo?.type === UserTypeEnum.PAYG_WALLET && (
                <Spacer vSpace={24} />
              )}
              <S.TableContainer>
                <>
                  {goCardlessInput(
                    tariffPricing,
                    billingItems,
                    t,
                    featureFlags.subsChargingRatesPromo,
                    resubscribed,
                    userInfo?.type,
                    userInfo?.country,
                  ).map(renderItem)}
                  <S.LinkViewContainer>
                    <LinkParagraphComponent />
                  </S.LinkViewContainer>
                  <Spacer vSpace={16} />
                </>
              </S.TableContainer>
              <Spacer vSpace={16} />
            </>
          </S.JoinGoCardlessContainer>

          <S.BottomViewContainer>
            <S.BottomTandC onPress={handleTermsAndConditionsPress}>
              <S.LinkText>{t.gocardless.text.JOIN_TANDC}</S.LinkText>
            </S.BottomTandC>
            <Spacer vSpace={16} />
            <Button
              type={ButtonAction.PRIMARY}
              onPress={onPressSetupSubscription}
              size={ButtonSize.XLARGE}
              accessibilityLabel="Set up gocard account"
              accessibilityHint="Process your go card payment"
            >
              {getSubscriptionButtonLabel(resubscribed, t)}
            </Button>
            <Spacer vSpace={16} />
          </S.BottomViewContainer>
        </>
      )}
    </>
  );
};
