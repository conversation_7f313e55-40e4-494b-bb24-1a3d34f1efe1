import React from 'react';

import { PulseDataItemIds, UserTypeEnum } from '../../../common/enums';
import {
  PolarDiamondIcon,
  PolarOffer,
  PolarPayAsUGoIcon,
  PolarRFIDIcon,
  PulseAccountMemberShip,
  QuickSetup,
} from '../../../components';
import theme from '../../../themes/Theme.default';
import { ITranslation } from '../../../translations/schema';
import { formatCurrency, getCurrency } from '../../../utils/helpers';

const { subscriptionMfe } = theme;

export const goCardlessInput = (
  tariffPricing: string,
  billingItems: any[],
  translations: ITranslation,
  subsChargingRatesPromo: number,
  resub?: boolean,
  userType?: string,
  userCountry?: string,
) => {
  let chargeRateInPence = subsChargingRatesPromo * 100;
  const amount = billingItems[0]?.billingAmount?.amount;
  const pulseData = [
    {
      id: PulseDataItemIds.NEW_SUBS,
      icon: <PolarOffer color={subscriptionMfe?.text.color.tertiary} />,
      title: translations.gocardless.paygWallet.STEP_5.TITLE,
      subTitle: translations.gocardless.paygWallet.STEP_5.SUBTITLE.replace(
        '{{amount}}',
        formatCurrency({
          amount: amount,
          currency: getCurrency(userCountry),
        }),
      ),
      underlineText: '',
      tariffPricing,
    },
    {
      id: PulseDataItemIds.LOWEST_RATES,
      icon: <PolarPayAsUGoIcon color={subscriptionMfe?.text.color.tertiary} />,
      title:
        userType === UserTypeEnum.PAYG_WALLET
          ? translations.gocardless.paygWallet.STEP_3.TITLE
          : translations.gocardless.text.LOWEST_RATES,
      subTitle:
        userType === UserTypeEnum.PAYG_WALLET
          ? translations.gocardless.paygWallet.STEP_3.SUBTITLE
          : translations.gocardless.text.BEST_RATES,
      underlineText: translations.gocardless.text.COMPARE_RATES,
      tariffPricing:
        'https://www.bppulse.com/en-gb/public-ev-charging#1g3yg1IElAUnZJBFA3ZW79',
    },
    {
      id: PulseDataItemIds.DIAMOND,
      icon: <PolarDiamondIcon color={subscriptionMfe?.text.color.tertiary} />,
      title: translations.gocardless.text.RESUBSCRIBE_FALSE.replace(
        '{{rate}}',
        chargeRateInPence.toString(),
      ),
      underlineText: '',
      subTitle: '',
      tariffPricing,
    },
    {
      id: PulseDataItemIds.QUICKSETUP,
      icon: <QuickSetup color={subscriptionMfe?.text.color.tertiary} />,
      title: translations.gocardless.paygWallet.STEP_2.TITLE,
      subTitle: translations.gocardless.paygWallet.STEP_2.SUBTITLE,
      underlineText: '',
      tariffPricing,
    },
  ];

  return pulseData
    .filter((item) => {
      if (item.id === PulseDataItemIds.QUICKSETUP) {
        return userType !== UserTypeEnum.PAYG_WALLET;
      }
      if (item.id === PulseDataItemIds.NEW_SUBS) {
        return !resub && userType === UserTypeEnum.PAYG_WALLET;
      }
      return true;
    })
    .filter(
      (item) =>
        userType !== UserTypeEnum.PAYG ||
        item.id !== PulseDataItemIds.QUICKSETUP,
    );
};

export const membershipData = (translations: ITranslation) => {
  return [
    {
      id: PulseDataItemIds.OFFER_CODE,
      icon: (
        <PulseAccountMemberShip color={subscriptionMfe?.text.color.tertiary} />
      ),
      title: translations.gocardless.text.APPLY_OFFER_CODE,
    },
    {
      id: PulseDataItemIds.REPLACE_CARD,
      icon: <PolarRFIDIcon color={subscriptionMfe?.text.color.tertiary} />,
      title: translations.gocardless.text.REPLACE_CARD,
    },
  ];
};
