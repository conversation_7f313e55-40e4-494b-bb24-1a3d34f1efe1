import { addMonths, format } from 'date-fns';
import React from 'react';

import { Currency } from '../../../common/enums';
import { useSettings } from '../../../providers/Settings';
import { formatCurrency } from '../../../utils/helpers';
import * as S from '../../Organisms/SubscriptionDetails/SubscriptionDetails.styles';

interface PaymentScheduleProps {
  subscription: number;
  totalAmount: number;
  currency?: Currency;
  isFirstTimeSubscriber: boolean;
  hasActiveOffer: boolean;
  offerDuration?: number;
}

export const PaymentSchedule: React.FC<PaymentScheduleProps> = ({
  subscription,
  totalAmount = 0,
  currency = Currency.GBP,
  isFirstTimeSubscriber,
  hasActiveOffer,
  offerDuration = 0,
}) => {
  const { t } = useSettings();
  const today = new Date();
  const oneMonthFromNow = addMonths(today, 1);
  const safeOfferDuration = offerDuration ?? 0;
  const offerEndDate = addMonths(today, safeOfferDuration);

  const formatLongDate = (date: Date) => format(date, 'd MMMM yyyy');

  if (isFirstTimeSubscriber) {
    return (
      <>
        {/* First month free */}
        <S.TextWrapper>
          <S.SmallText>
            {t.mySubscription.SUBSCRIPTION_DETAILS.FIRST_MONTH_PAYMENT}
          </S.SmallText>
          <S.SmallText>
            <S.SmallText strikethrough align="right">
              {formatCurrency({ amount: subscription, currency })}
            </S.SmallText>{' '}
            {formatCurrency({ amount: 0, currency })}
          </S.SmallText>
        </S.TextWrapper>

        {/* Payment after first month */}
        <S.TextWrapper>
          <S.SmallText>
            {t.mySubscription.SUBSCRIPTION_DETAILS.TO_PAY_FROM?.replace(
              '{{date}}',
              formatLongDate(oneMonthFromNow),
            )}
          </S.SmallText>
          <S.SmallText>
            {hasActiveOffer &&
            offerDuration > 1 &&
            totalAmount < subscription ? (
              <>
                <S.SmallText strikethrough align="right">
                  {formatCurrency({ amount: subscription, currency })}
                </S.SmallText>{' '}
                {formatCurrency({ amount: totalAmount, currency })}
              </>
            ) : (
              formatCurrency({ amount: subscription, currency })
            )}
          </S.SmallText>
        </S.TextWrapper>

        {/* First month free + Offer more than one month */}
        {hasActiveOffer && offerDuration > 1 && (
          <S.TextWrapper>
            <S.SmallText>
              {t.mySubscription.SUBSCRIPTION_DETAILS.TO_PAY_FROM?.replace(
                '{{date}}',
                formatLongDate(offerEndDate),
              )}
            </S.SmallText>
            <S.SmallText>
              {formatCurrency({ amount: subscription, currency })}
            </S.SmallText>
          </S.TextWrapper>
        )}
      </>
    );
  }

  // Returning subscriber WITH offer
  if (hasActiveOffer) {
    return (
      <>
        {/* Start date label */}
        <S.TextWrapper>
          <S.SmallText>
            {t.mySubscription.SUBSCRIPTION_DETAILS.START_DATE}
          </S.SmallText>
          <S.SmallText>{format(today, 'dd MMMM yyyy')}</S.SmallText>
        </S.TextWrapper>

        {/* Discount applied label */}
        <S.TextWrapper>
          <S.SmallText>
            {t.mySubscription.SUBSCRIPTION_DETAILS.DISCOUNT_APPLIED}
          </S.SmallText>

          <S.SmallText flex align="right">
            <S.SmallText strikethrough align="right">
              {formatCurrency({ amount: subscription, currency })}
            </S.SmallText>{' '}
            {formatCurrency({ amount: totalAmount, currency })}{' '}
            {t.mySubscription.SUBSCRIPTION_DETAILS.DISCOUNT_DURATION?.replace(
              '{{duration}}',
              `${offerDuration}`,
            )}
          </S.SmallText>
        </S.TextWrapper>

        {/* Monthly fee label*/}
        <S.TextWrapper>
          <S.SmallText>
            {t.mySubscription.SUBSCRIPTION_DETAILS.MONTHLY_FEE}
          </S.SmallText>
          <S.SmallText>
            {formatCurrency({ amount: subscription, currency })}{' '}
            {t.mySubscription.SUBSCRIPTION_DETAILS.PAYABLE_FROM?.replace(
              '{{date}}',
              format(offerEndDate, 'dd/MM'),
            )}
          </S.SmallText>
        </S.TextWrapper>
      </>
    );
  }

  // Returning subscriber WITHOUT offer
  return (
    <>
      <S.TextWrapper>
        <S.SmallText>
          {t.mySubscription.SUBSCRIPTION_DETAILS.TOTAL_TO_PAY}
        </S.SmallText>
        <S.SmallText>
          {formatCurrency({ amount: subscription, currency })}
        </S.SmallText>
      </S.TextWrapper>
    </>
  );
};
