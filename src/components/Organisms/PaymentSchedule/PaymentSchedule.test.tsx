import { render } from '@testing-library/react-native';
import React from 'react';

import { Currency } from '../../../common/enums';
import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import { PaymentSchedule } from './PaymentSchedule';

const mockTranslations = TRANSLATIONS.en_GB;

jest.mock('../../../providers/Settings', () => ({
  useSettings: () => ({ t: mockTranslations }),
}));

const mockFormatCurrency = jest
  .fn()
  .mockImplementation(({ amount, currency }) => {
    const symbol = currency === 'GBP' ? '£' : '€';
    return `${symbol}${amount.toFixed(2)}`;
  });

jest.mock('../../../utils/helpers', () => ({
  formatCurrency: (params: any) => mockFormatCurrency(params),
}));

jest.mock('date-fns', () => ({
  addMonths: jest.fn(() => new Date(2024, 5, 22)),
  format: jest.fn((_, formatStr) => {
    const mockDate = '22 June 2024';
    if (formatStr === 'd MMMM yyyy') {
      return mockDate;
    }
    if (formatStr === 'dd MMMM yyyy') {
      return mockDate;
    }
    if (formatStr === 'dd/MM') {
      return '22/06';
    }
    return mockDate;
  }),
}));

describe('PaymentSchedule', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('First-time subscriber', () => {
    it('Should show first month free without offer', () => {
      const { getByText, getAllByText } = render(
        <Theme>
          <PaymentSchedule
            subscription={7.85}
            totalAmount={7.85}
            currency={Currency.GBP}
            isFirstTimeSubscriber={true}
            hasActiveOffer={false}
          />
        </Theme>,
      );

      expect(
        getByText(
          mockTranslations.mySubscription.SUBSCRIPTION_DETAILS
            .FIRST_MONTH_PAYMENT,
        ),
      ).toBeDefined();

      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 0,
        currency: 'GBP',
      });

      const currencyElements = getAllByText(/£/);
      expect(currencyElements.length).toBeGreaterThan(0);
    });

    it('Should show first month free with multi-month offer', () => {
      const { getAllByText } = render(
        <Theme>
          <PaymentSchedule
            subscription={7.85}
            totalAmount={5.0}
            currency={Currency.GBP}
            isFirstTimeSubscriber={true}
            hasActiveOffer={true}
            offerDuration={3}
          />
        </Theme>,
      );

      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 0,
        currency: 'GBP',
      });
      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 5.0,
        currency: 'GBP',
      });

      const toPayFromElements = getAllByText(/To pay from/);
      expect(toPayFromElements.length).toBe(2);
    });
  });

  describe('Returning subscriber with offer', () => {
    it('Should show discount details', () => {
      const { getByText, getAllByText } = render(
        <Theme>
          <PaymentSchedule
            subscription={7.85}
            totalAmount={5.0}
            currency={Currency.GBP}
            isFirstTimeSubscriber={false}
            hasActiveOffer={true}
            offerDuration={2}
          />
        </Theme>,
      );

      expect(
        getByText(
          mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.START_DATE,
        ),
      ).toBeDefined();
      expect(
        getByText(
          mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.DISCOUNT_APPLIED,
        ),
      ).toBeDefined();

      expect(mockFormatCurrency).toHaveBeenCalledWith({
        amount: 5.0,
        currency: 'GBP',
      });

      const currencyElements = getAllByText(/£/);
      expect(currencyElements.length).toBeGreaterThan(0);
    });
  });

  describe('Returning subscriber without offer', () => {
    it('Should show only total to pay', () => {
      const { getByText, queryByText } = render(
        <Theme>
          <PaymentSchedule
            subscription={7.85}
            totalAmount={7.85}
            currency={Currency.GBP}
            isFirstTimeSubscriber={false}
            hasActiveOffer={false}
          />
        </Theme>,
      );

      expect(
        getByText(
          mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.TOTAL_TO_PAY,
        ),
      ).toBeDefined();
      expect(getByText('£7.85')).toBeDefined();
      expect(
        queryByText(
          mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.DISCOUNT_APPLIED,
        ),
      ).toBeNull();
    });
  });

  it('Should format EUR currency correctly', () => {
    render(
      <Theme>
        <PaymentSchedule
          subscription={10.0}
          totalAmount={10.0}
          currency={Currency.EUR}
          isFirstTimeSubscriber={false}
          hasActiveOffer={false}
        />
      </Theme>,
    );

    expect(mockFormatCurrency).toHaveBeenCalledWith({
      amount: 10.0,
      currency: 'EUR',
    });
  });
});
