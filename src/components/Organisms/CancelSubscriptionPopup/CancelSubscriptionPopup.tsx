import { ActionConfirmationModal } from '@bp/ui-components/mobile/core';
import React from 'react';

import { SubscriptionScreenNames } from '../../../common/enums';
import { useHostNavigation } from '../../../providers/HostNavigationProvider';
import { useSettings } from '../../../providers/Settings';

type Props = {
  isVisible: boolean;
  toggleModal: () => void;
};

const CancelSubscriptionPopup: React.FC<Props> = ({
  isVisible,
  toggleModal,
}) => {
  const { t } = useSettings();
  const { navigate } = useHostNavigation();

  const handleModalCancelPress = () => {
    toggleModal();
    navigate(SubscriptionScreenNames.ConfirmCancelComfortMembership);
  };

  return (
    <ActionConfirmationModal
      isVisible={isVisible}
      titleText={t.modals.cancelSubscriptionPopup.title}
      titleMessage={t.modals.cancelSubscriptionPopup.subtitle}
      secondaryButtonText={t.modals.cancelSubscriptionPopup.primary.label}
      secondaryAccessibilityLabel={
        t.modals.cancelSubscriptionPopup.primary.accessibility.label
      }
      secondaryAccessibilityHint={
        t.modals.cancelSubscriptionPopup.primary.accessibility.hint
      }
      secondaryButtonOnPress={handleModalCancelPress}
      primaryButtonText={t.confirmCancelComfortMembership.buttons.keep.label}
      accessibilityLabel={
        t.modals.cancelSubscriptionPopup.secondary.accessibility.label
      }
      accessibilityHint={
        t.modals.cancelSubscriptionPopup.secondary.accessibility.hint
      }
      primaryButtonOnPress={toggleModal}
    />
  );
};

export default CancelSubscriptionPopup;
