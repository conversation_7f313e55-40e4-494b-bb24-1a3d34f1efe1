import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import CancelSubscriptionPopup from './CancelSubscriptionPopup';

const mockTranslations = TRANSLATIONS.en_GB;

const mockUseSettingsObj = {
  t: mockTranslations,
};

const mockToggleModal = jest.fn();
const mockNavigate = jest.fn();

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);

jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockHostNavigationObj = {
  navigate: mockNavigate,
};

const mockHostNavigation = jest.fn().mockReturnValue(mockHostNavigationObj);

jest.mock('../../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigation(),
}));

const PRIMARY_BUTTON = 'SecondaryButton.ActionConfirmationModalButton';
const SECONDARY_BUTTON = 'PrimaryButton.ActionConfirmationModalButton';

const renderComponent = () =>
  render(
    <Theme>
      <CancelSubscriptionPopup isVisible toggleModal={mockToggleModal} />
    </Theme>,
  );

describe('<CancelSubscriptionPopup />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should correctly render component', () => {
    const { toJSON } = renderComponent();

    expect(toJSON()).toMatchSnapshot();
  });
  it('should navigate to ConfirmCancelComfortMembership screen when clicked on primary button', () => {
    const { getByTestId } = renderComponent();

    expect(getByTestId(PRIMARY_BUTTON)).toBeDefined();

    fireEvent.press(getByTestId(PRIMARY_BUTTON));

    expect(mockNavigate).toHaveBeenCalledTimes(1);
    expect(mockNavigate).toHaveBeenNthCalledWith(
      1,
      'ConfirmCancelComfortMembership',
    );
  });
  it('should trigger toggleModal when clicked on secondary button', () => {
    const { getByTestId } = renderComponent();

    expect(getByTestId(SECONDARY_BUTTON)).toBeDefined();

    fireEvent.press(getByTestId(SECONDARY_BUTTON));

    expect(mockToggleModal).toHaveBeenCalledTimes(1);
  });
});
