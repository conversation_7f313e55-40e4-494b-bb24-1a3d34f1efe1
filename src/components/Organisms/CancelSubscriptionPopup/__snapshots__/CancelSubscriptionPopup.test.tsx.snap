// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<CancelSubscriptionPopup /> should correctly render component 1`] = `
<Modal
  animationType="fade"
  hardwareAccelerated={false}
  onRequestClose={[Function]}
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 0,
      "width": "100%",
    }
  }
  transparent={true}
  visible={true}
>
  <View
    accessibilityState={
      {
        "busy": undefined,
        "checked": undefined,
        "disabled": undefined,
        "expanded": undefined,
        "selected": undefined,
      }
    }
    accessible={false}
    backgroundColor="#111111B3"
    customPadding="0 24px 0 24px"
    focusable={true}
    onClick={[Function]}
    onResponderGrant={[Function]}
    onResponderMove={[Function]}
    onResponderRelease={[Function]}
    onResponderTerminate={[Function]}
    onResponderTerminationRequest={[Function]}
    onStartShouldSetResponder={[Function]}
    style={
      {
        "backgroundColor": "#111111B3",
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 0,
        "justifyContent": "center",
        "paddingBottom": 0,
        "paddingLeft": 24,
        "paddingRight": 24,
        "paddingTop": 0,
        "width": "100%",
      }
    }
    testID="TouchableWithoutFeedback"
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessible={false}
      focusable={false}
      fullWidthVisible={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      shadow={true}
      style={
        {
          "backgroundColor": "#FFFFFF",
          "borderBottomLeftRadius": 9,
          "borderBottomRightRadius": 9,
          "borderTopLeftRadius": 9,
          "borderTopRightRadius": 9,
          "elevation": 10,
          "shadowColor": "#000000",
          "shadowOffset": {
            "height": 0,
            "width": 0,
          },
          "shadowOpacity": 0.11,
          "shadowRadius": 6,
          "width": "100%",
        }
      }
    >
      <View
        style={
          {
            "paddingBottom": 8,
            "paddingLeft": 0,
            "paddingRight": 0,
            "paddingTop": 8,
          }
        }
      >
        <Text
          style={
            {
              "color": "#111111",
              "fontSize": 16,
              "letterSpacing": 0.7,
              "lineHeight": 24,
              "paddingBottom": 8,
              "paddingLeft": 0,
              "paddingRight": 0,
              "paddingTop": 8,
              "textAlign": "center",
            }
          }
        >
          Cancel Extra Tariff?​
        </Text>
        <Text
          style={
            {
              "color": "rgba(17, 17, 17, 0.8)",
              "fontSize": 14,
              "letterSpacing": 0.7,
              "lineHeight": 24,
              "paddingBottom": 8,
              "paddingLeft": 16,
              "paddingRight": 16,
              "paddingTop": 0,
              "textAlign": "center",
            }
          }
        >
          You will lose all Extra Tariff benefits and be switched to our Classic Tariff.​
        </Text>
      </View>
      <View
        style={
          {
            "flexDirection": "row",
          }
        }
        verticalButtons={false}
      >
        <View
          accessibilityHint="Navigates to the cancellation page"
          accessibilityLabel="Cancel Extra Tariff"
          accessibilityRole="button"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": false,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "borderLeftColor": "#ededed",
              "borderLeftWidth": 0,
              "borderStyle": "solid",
              "borderTopColor": "#ededed",
              "borderTopWidth": 1,
              "flexBasis": 0,
              "flexGrow": 1,
              "flexShrink": 1,
              "justifyContent": "center",
              "opacity": 1,
              "paddingBottom": 18,
              "paddingTop": 18,
            }
          }
          testID="SecondaryButton.ActionConfirmationModalButton"
        >
          <Text
            design={1}
            style={
              {
                "color": "rgba(17, 17, 17, 0.64)",
                "fontSize": 15,
                "letterSpacing": 0.7,
                "lineHeight": 20,
                "textAlign": "center",
              }
            }
          >
            Cancel Extra Tariff​
          </Text>
        </View>
        <View
          accessibilityHint="Navigates back to your Extra Tariff details screen"
          accessibilityLabel="Cancel"
          accessibilityRole="button"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": false,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "borderLeftColor": "#ededed",
              "borderLeftWidth": 1,
              "borderStyle": "solid",
              "borderTopColor": "#ededed",
              "borderTopWidth": 1,
              "flexBasis": 0,
              "flexGrow": 1,
              "flexShrink": 1,
              "justifyContent": "center",
              "opacity": 1,
              "paddingBottom": 18,
              "paddingTop": 18,
            }
          }
          testID="PrimaryButton.ActionConfirmationModalButton"
        >
          <Text
            design={0}
            style={
              {
                "color": "#111111",
                "fontSize": 15,
                "letterSpacing": 0.7,
                "lineHeight": 20,
                "textAlign": "center",
              }
            }
          >
             Keep Extra Tariff
          </Text>
        </View>
      </View>
    </View>
  </View>
</Modal>
`;
