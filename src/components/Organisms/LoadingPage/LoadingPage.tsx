import { LoadingIndicator } from '@bp/ui-components/mobile/core';
import React from 'react';

import * as S from './LoadingPage.styles';

interface ILoadingPageProps {
  text?: string;
}

export const LoadingPage = ({ text }: ILoadingPageProps) => {
  return (
    <S.Container>
      {text ? <S.LoadText>{text}</S.LoadText> : null}
      <LoadingIndicator color="black" size={36} thickness={3} />
    </S.Container>
  );
};
