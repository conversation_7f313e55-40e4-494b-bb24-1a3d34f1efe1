import { render } from '@testing-library/react-native';
import React from 'react';

import { TRANSLATIONS } from '../../../translations';
import { LoadingPage } from './LoadingPage';

require('setimmediate');
const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: {
    id: '123',
    balance: '1.99',
    gocardless: { mandateStatus: 'applied' },
    tagIds: [],
  },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  featureFlags: {
    enableInvoicesList: true,
    enableOfferDetails: true,
    enableRFID: true,
  },
};
jest.mock('react-native-device-info', () => {
  return {
    hasNotch: jest.fn(),
    mockRNDeviceInfo: jest.fn(),
  };
});

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

jest.mock('@react-navigation/native');

jest.mock('../../../providers/SubsPreferencesProvider', () => ({
  useSubsPreferences: () => ({ subPreferences: {} }),
}));

describe('LoadingPage rendering', () => {
  it('Should render correctly', () => {
    const result = render(
      <LoadingPage text={mockUseSettingsObj.t.gocardless.text.LOADING} />,
    );
    expect(result.toJSON()).toMatchSnapshot();
  });

  it('renders loading page with loading text', () => {
    const { getByText } = render(
      <LoadingPage text={mockUseSettingsObj.t.gocardless.text.LOADING} />,
    );
    expect(
      getByText(mockUseSettingsObj.t.gocardless.text.LOADING),
    ).toBeDefined();
  });
});
