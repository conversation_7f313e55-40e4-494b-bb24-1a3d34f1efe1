const { PaymentSchedule } = require('../PaymentSchedule/PaymentSchedule');
const { ContractDetails } = require('../ContractDetails/ContractDetails');
const { PaymentSummary } = require('../PaymentSummary/PaymentSummary');
import { render } from '@testing-library/react-native';
import React from 'react';

import { Currency } from '../../../common/enums';
import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import { SubscriptionDetails } from './SubscriptionDetails';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  isInternetReachable: true,
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockFormatCurrency = jest
  .fn()
  .mockImplementation(({ amount, currency }) => {
    const symbol = currency === 'GBP' ? '£' : '€';
    return `${symbol}${amount.toFixed(2)}`;
  });

const mockGetAccessibilityLabelForCurrency = jest
  .fn()
  .mockImplementation((amount, currency) => {
    return `${amount} ${currency === 'GBP' ? 'pounds' : 'euros'}`;
  });

jest.mock('../../../utils/helpers', () => ({
  formatCurrency: (params: any) => mockFormatCurrency(params),
  getAccessibilityLabelForCurrency: (...args: Array<any>) =>
    mockGetAccessibilityLabelForCurrency(...args),
}));

jest.mock('../PaymentSchedule/PaymentSchedule', () => ({
  PaymentSchedule: jest.fn(() => null),
}));

jest.mock('../ContractDetails/ContractDetails', () => ({
  ContractDetails: jest.fn(() => null),
}));

jest.mock('../PaymentSummary/PaymentSummary', () => ({
  PaymentSummary: jest.fn(() => null),
}));

jest.mock('date-fns', () => ({
  addMonths: jest.fn(() => new Date(2024, 5, 22)),
  format: jest.fn(() => '22 June 2024'),
}));

const dateSpy = jest.spyOn(global.Date, 'now');

describe('SubscriptionDetails rendering', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('Should render correctly for My Subscription screen', () => {
    const result = render(
      <Theme>
        <SubscriptionDetails
          isMySubscription
          currency={Currency.GBP}
          subscription={7.85}
        />
      </Theme>,
    );
    expect(result.toJSON()).toMatchSnapshot();
  });

  it('Should render correctly for Confirm Subscription screen', () => {
    const result = render(
      <Theme>
        <SubscriptionDetails
          currency={Currency.GBP}
          subscription={7.85}
          totalAmount={7.85}
        />
      </Theme>,
    );
    // Just verify it renders without errors
    expect(result.UNSAFE_root).toBeTruthy();
  });

  describe('Confirm Subscription screen scenarios', () => {
    it('Should render header for confirm subscription', () => {
      const { getByText } = render(
        <Theme>
          <SubscriptionDetails
            currency={Currency.GBP}
            subscription={7.85}
            totalAmount={7.85}
          />
        </Theme>,
      );
      expect(getByText(mockTranslations.mySubscription.HEADER)).toBeDefined();
    });

    it('Should render PaymentSchedule component with correct props', () => {
      const { UNSAFE_getByType } = render(
        <Theme>
          <SubscriptionDetails
            currency={Currency.GBP}
            subscription={7.85}
            totalAmount={5.0}
            isFirstTimeSubscriber={true}
            hasValidOffer={true}
            hasActiveDiscount={true}
            offerDuration={3}
          />
        </Theme>,
      );
      const paymentSchedule = UNSAFE_getByType(PaymentSchedule);
      expect(paymentSchedule).toBeDefined();
    });

    it('Should render ContractDetails component with current billing day', () => {
      const mockDate = new Date(2024, 5, 15);
      dateSpy.mockImplementationOnce(() => mockDate.valueOf());

      const { UNSAFE_getByType } = render(
        <Theme>
          <SubscriptionDetails
            currency={Currency.GBP}
            subscription={7.85}
            totalAmount={7.85}
          />
        </Theme>,
      );
      const contractDetails = UNSAFE_getByType(ContractDetails);
      expect(contractDetails).toBeDefined();

      jest.restoreAllMocks();
    });

    it('Should render PaymentSummary component with correct props', () => {
      const { UNSAFE_getByType } = render(
        <Theme>
          <SubscriptionDetails
            currency={Currency.GBP}
            subscription={7.85}
            totalAmount={5.0}
            isFirstTimeSubscriber={false}
            hasValidOffer={true}
            hasActiveDiscount={true}
          />
        </Theme>,
      );
      const paymentSummary = UNSAFE_getByType(PaymentSummary);
      expect(paymentSummary).toBeDefined();
    });
  });

  describe('My Subscription screen', () => {
    it('Should render correct title', () => {
      const { getByText } = render(
        <Theme>
          <SubscriptionDetails
            isMySubscription
            currency={Currency.GBP}
            subscription={7.85}
          />
        </Theme>,
      );
      expect(
        getByText(mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.TITLE),
      ).toBeDefined();
    });

    it('Should display billing date as 12th', () => {
      const { getByText } = render(
        <Theme>
          <SubscriptionDetails
            isMySubscription
            currency={Currency.GBP}
            subscription={7.85}
          />
        </Theme>,
      );
      expect(getByText('12th')).toBeDefined();
    });

    it('Should display billing schedule', () => {
      const { getByText } = render(
        <Theme>
          <SubscriptionDetails
            isMySubscription
            currency={Currency.GBP}
            subscription={7.85}
          />
        </Theme>,
      );
      expect(
        getByText(
          mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.BILLING_SCHEDULE,
        ),
      ).toBeDefined();
      expect(
        getByText(mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.MONTHLY),
      ).toBeDefined();
    });

    it('Should display subscription excluding discount', () => {
      const { getAllByText } = render(
        <Theme>
          <SubscriptionDetails
            isMySubscription
            currency={Currency.GBP}
            subscription={7.85}
          />
        </Theme>,
      );
      expect(
        getAllByText(
          mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.EXCLUDE_DISCOUNT,
        )[0],
      ).toBeDefined();
      expect(getAllByText('£7.85')[0]).toBeDefined();
    });

    it('Should display discount when active', () => {
      const { getByText } = render(
        <Theme>
          <SubscriptionDetails
            isMySubscription
            hasActiveDiscount
            currency={Currency.GBP}
            subscription={7.85}
            subsDiscount={2.0}
          />
        </Theme>,
      );
      expect(
        getByText(
          mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.DISCOUNT,
        ),
      ).toBeDefined();
      expect(getByText('-£2.00')).toBeDefined();
    });

    it('Should display total', () => {
      const { getByText } = render(
        <Theme>
          <SubscriptionDetails
            isMySubscription
            currency={Currency.GBP}
            subscription={7.85}
          />
        </Theme>,
      );
      const totalElements = getByText(
        mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.TOTAL,
      );
      expect(totalElements).toBeDefined();
    });

    it('Should not display discount when hasActiveDiscount is false', () => {
      const { queryByText } = render(
        <Theme>
          <SubscriptionDetails
            isMySubscription
            hasActiveDiscount={false}
            currency={Currency.GBP}
            subscription={7.85}
            subsDiscount={2.0}
          />
        </Theme>,
      );
      expect(
        queryByText(
          mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.DISCOUNT,
        ),
      ).toBeNull();
    });

    it('Should not display credit offer when hasActiveCreditOffer is false', () => {
      const { queryByText } = render(
        <Theme>
          <SubscriptionDetails
            isMySubscription
            hasActiveCreditOffer={false}
            creditAmount={5}
            currency={Currency.GBP}
            subscription={7.85}
          />
        </Theme>,
      );
      expect(
        queryByText(mockTranslations.mySubscription.SUBSCRIPTION_DETAILS.FREE),
      ).toBeNull();
    });
  });

  describe('Props handling', () => {
    it('Should handle default props correctly', () => {
      const { UNSAFE_root } = render(
        <Theme>
          <SubscriptionDetails currency={Currency.GBP} />
        </Theme>,
      );
      expect(UNSAFE_root).toBeTruthy();
    });

    it('Should handle zero amounts', () => {
      const { UNSAFE_root } = render(
        <Theme>
          <SubscriptionDetails
            currency={Currency.GBP}
            subscription={0}
            totalAmount={0}
            subsDiscount={0}
            creditAmount={0}
          />
        </Theme>,
      );
      expect(UNSAFE_root).toBeTruthy();
    });

    it('Should calculate hasActiveOffer correctly for discount', () => {
      const { UNSAFE_getByType } = render(
        <Theme>
          <SubscriptionDetails
            currency={Currency.GBP}
            subscription={7.85}
            totalAmount={5.0}
            hasValidOffer={true}
            hasActiveDiscount={true}
          />
        </Theme>,
      );
      const paymentSchedule = UNSAFE_getByType(PaymentSchedule);
      expect(paymentSchedule).toBeDefined();
    });

    it('Should calculate hasActiveOffer correctly for credit', () => {
      const { UNSAFE_getByType } = render(
        <Theme>
          <SubscriptionDetails
            currency={Currency.GBP}
            subscription={7.85}
            totalAmount={7.85}
            hasValidOffer={true}
            hasActiveCreditOffer={true}
          />
        </Theme>,
      );
      const paymentSchedule = UNSAFE_getByType(PaymentSchedule);
      expect(paymentSchedule).toBeDefined();
    });

    it('Should set hasActiveOffer to false when hasValidOffer is false', () => {
      const { UNSAFE_getByType } = render(
        <Theme>
          <SubscriptionDetails
            currency={Currency.GBP}
            subscription={7.85}
            totalAmount={5.0}
            hasValidOffer={false}
            hasActiveDiscount={true}
            hasActiveCreditOffer={true}
          />
        </Theme>,
      );
      const paymentSchedule = UNSAFE_getByType(PaymentSchedule);
      expect(paymentSchedule).toBeDefined();
    });
  });
});
