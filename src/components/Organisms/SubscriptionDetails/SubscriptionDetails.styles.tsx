import { styled } from 'styled-components/native';

interface NormalTextProps {
  needsPadding?: boolean;
  needsPaddingBottom?: boolean;
  bold?: boolean;
  flex?: boolean;
  align?: 'left' | 'center' | 'right' | 'justify';
  strikethrough?: boolean;
}

export const Wrapper = styled.View``;

export const NormalText = styled.Text<NormalTextProps>`
  padding-left: ${({ needsPadding }) => (needsPadding ? '24px' : '0')};
  font-size: 16px;
  line-height: 28px;
  font-weight: ${({ bold }) => (bold ? '700' : '400')};
  text-align: ${({ align }) => align || 'left'};
  color: ${({ theme }) => theme.subscriptionMfe.gocardless.text.banner};
  padding-bottom: ${({ needsPadding, needsPaddingBottom }) =>
    needsPadding || needsPaddingBottom ? '8px' : '0'};
  ${({ flex }) => (flex ? 'flex: 1;' : '')}
  ${({ strikethrough }) =>
    strikethrough
      ? `
    text-decoration-line: line-through;
    text-decoration-style: solid;
  `
      : ''}
`;

export const InformationsContainer = styled.View`
  padding: 16px 24px;
  border: 1px solid #dedede;
  border-left-width: 0;
  border-right-width: 0;
`;

export const TextWrapper = styled.View`
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  padding-bottom: 8px;
`;

export const SmallText = styled.Text<{
  bold?: boolean;
  flex?: boolean;
  align?: 'left' | 'center' | 'right' | 'justify';
  strikethrough?: boolean;
}>`
  font-size: 13px;
  line-height: 23px;
  font-weight: ${({ bold }) => (bold ? '700' : '400')};
  text-align: ${({ align }) => align || 'left'};
  color: ${({ theme }) => theme.subscriptionMfe.gocardless.text.banner};
  ${({ flex }) => (flex ? 'flex: 1;' : '')}
  ${({ strikethrough }) =>
    strikethrough
      ? `
    text-decoration-line: line-through;
    text-decoration-style: solid;
  `
      : ''}
`;

export const HorizontalLine = styled.View`
  width: 100%;
  height: 1px;
  background-color: #dedede;
  margin: 8px 0 16px 0;
`;
