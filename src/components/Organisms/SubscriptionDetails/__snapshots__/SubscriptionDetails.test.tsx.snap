// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SubscriptionDetails rendering Should render correctly for My Subscription screen 1`] = `
<View
  style={{}}
>
  <Text
    needsPadding={true}
    style={
      {
        "color": "#212121",
        "fontSize": 16,
        "fontWeight": "400",
        "lineHeight": 28,
        "paddingBottom": 8,
        "paddingLeft": 24,
        "textAlign": "left",
      }
    }
  >
    Subscription details
  </Text>
  <View
    style={
      {
        "borderColor": "#dedede",
        "borderLeftWidth": 0,
        "borderRightWidth": 0,
        "borderStyle": "solid",
        "borderWidth": 1,
        "paddingBottom": 16,
        "paddingLeft": 24,
        "paddingRight": 24,
        "paddingTop": 16,
      }
    }
  >
    <View
      style={
        {
          "display": "flex",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingBottom": 8,
        }
      }
    >
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
            "textAlign": "left",
          }
        }
      >
        Billing date
      </Text>
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
            "textAlign": "left",
          }
        }
      >
        12th
      </Text>
    </View>
    <View
      style={
        {
          "display": "flex",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingBottom": 8,
        }
      }
    >
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
            "textAlign": "left",
          }
        }
      >
        Billing schedule
      </Text>
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
            "textAlign": "left",
          }
        }
      >
        Monthly
      </Text>
    </View>
    <View
      style={
        {
          "display": "flex",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingBottom": 8,
        }
      }
    >
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
            "textAlign": "left",
          }
        }
      >
        Subscription (Excl. discount)
      </Text>
      <Text
        accessibilityLabel="7.85 pounds"
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "400",
            "lineHeight": 23,
            "textAlign": "left",
          }
        }
      >
        £7.85
      </Text>
    </View>
    <View
      style={
        {
          "display": "flex",
          "flexDirection": "row",
          "justifyContent": "space-between",
          "paddingBottom": 8,
        }
      }
    >
      <Text
        bold={true}
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "700",
            "lineHeight": 23,
            "textAlign": "left",
          }
        }
      >
        Total
      </Text>
      <Text
        accessibilityLabel="7.85 pounds"
        bold={true}
        style={
          {
            "color": "#212121",
            "fontSize": 13,
            "fontWeight": "700",
            "lineHeight": 23,
            "textAlign": "left",
          }
        }
      >
        £7.85
      </Text>
    </View>
  </View>
</View>
`;
