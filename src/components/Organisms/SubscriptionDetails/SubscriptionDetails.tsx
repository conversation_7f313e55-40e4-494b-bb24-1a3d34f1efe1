import React from 'react';

import { ISubscriptionDetails } from '../../../common/interfaces';
import { useSettings } from '../../../providers/Settings';
import {
  formatCurrency,
  getAccessibilityLabelForCurrency,
} from '../../../utils/helpers';
import { ContractDetails } from '../ContractDetails/ContractDetails';
import { PaymentSchedule } from '../PaymentSchedule/PaymentSchedule';
import { PaymentSummary } from '../PaymentSummary/PaymentSummary';
import * as S from './SubscriptionDetails.styles';

interface ExtendedSubscriptionDetails extends ISubscriptionDetails {
  isFirstTimeSubscriber?: boolean;
  hasValidOffer?: boolean;
  offerDuration?: number;
}

export const SubscriptionDetails = ({
  isMySubscription = false,
  hasActiveDiscount = false,
  hasActiveCreditOffer = false,
  subsDiscount = 0,
  creditAmount = 0,
  subscription = 0,
  currency,
  totalAmount = 0,
  isFirstTimeSubscriber = false,
  hasValidOffer = false,
  offerDuration = 0,
}: ExtendedSubscriptionDetails) => {
  const { t } = useSettings();

  const billingDay = new Date(Date.now()).getDate();
  const hasActiveOffer =
    hasValidOffer && (hasActiveDiscount || hasActiveCreditOffer);

  if (!isMySubscription) {
    return (
      <S.Wrapper>
        <S.NormalText needsPadding>{t.mySubscription.HEADER}</S.NormalText>
        <S.InformationsContainer>
          <PaymentSchedule
            subscription={subscription}
            totalAmount={totalAmount}
            currency={currency}
            isFirstTimeSubscriber={isFirstTimeSubscriber}
            hasActiveOffer={hasActiveOffer}
            offerDuration={offerDuration}
          />

          <ContractDetails billingDay={billingDay} />

          <PaymentSummary
            subscription={subscription}
            totalAmount={totalAmount}
            currency={currency}
            isFirstTimeSubscriber={isFirstTimeSubscriber}
            hasActiveOffer={hasActiveOffer}
          />
        </S.InformationsContainer>
      </S.Wrapper>
    );
  }

  return (
    <S.Wrapper>
      <S.NormalText needsPadding>
        {t.mySubscription.SUBSCRIPTION_DETAILS.TITLE}
      </S.NormalText>
      <S.InformationsContainer>
        <S.TextWrapper>
          <S.SmallText>
            {t.mySubscription.SUBSCRIPTION_DETAILS.BILLING_DATE}
          </S.SmallText>
          <S.SmallText>{'12th'}</S.SmallText>
        </S.TextWrapper>
        <S.TextWrapper>
          <S.SmallText>
            {t.mySubscription.SUBSCRIPTION_DETAILS.BILLING_SCHEDULE}
          </S.SmallText>
          <S.SmallText>
            {t.mySubscription.SUBSCRIPTION_DETAILS.MONTHLY}
          </S.SmallText>
        </S.TextWrapper>
        <S.TextWrapper>
          <S.SmallText>
            {t.mySubscription.SUBSCRIPTION_DETAILS.EXCLUDE_DISCOUNT}
          </S.SmallText>
          <S.SmallText
            accessibilityLabel={getAccessibilityLabelForCurrency(
              subscription,
              currency,
            )}
          >
            {formatCurrency({ amount: subscription, currency })}
          </S.SmallText>
        </S.TextWrapper>
        {hasActiveDiscount && (
          <S.TextWrapper>
            <S.SmallText>
              {t.mySubscription.SUBSCRIPTION_DETAILS.DISCOUNT}
            </S.SmallText>
            <S.SmallText
              accessibilityLabel={getAccessibilityLabelForCurrency(
                Number(subsDiscount.toFixed(2)),
                currency,
              )}
            >
              -
              {formatCurrency({
                amount: Number(subsDiscount.toFixed(2)),
                currency,
              })}
            </S.SmallText>
          </S.TextWrapper>
        )}
        {hasActiveCreditOffer && (
          <S.TextWrapper>
            <S.SmallText
              accessibilityLabel={getAccessibilityLabelForCurrency(
                creditAmount,
                currency,
              )}
            >
              {formatCurrency({ amount: creditAmount, currency })}{' '}
              {t.mySubscription.SUBSCRIPTION_DETAILS.CHARGING_CREDIT}
            </S.SmallText>
            <S.SmallText>
              {t.mySubscription.SUBSCRIPTION_DETAILS.FREE}
            </S.SmallText>
          </S.TextWrapper>
        )}
        <S.TextWrapper>
          <S.SmallText bold>
            {t.mySubscription.SUBSCRIPTION_DETAILS.TOTAL}
          </S.SmallText>
          <S.SmallText
            bold
            accessibilityLabel={getAccessibilityLabelForCurrency(
              subscription,
              currency,
            )}
          >
            {formatCurrency({ amount: subscription, currency })}
          </S.SmallText>
        </S.TextWrapper>
      </S.InformationsContainer>
    </S.Wrapper>
  );
};
