import AsyncStorage from '@react-native-async-storage/async-storage';
import { act, fireEvent, render, screen } from '@testing-library/react-native';
import moment from 'moment';
import React from 'react';

import { UserTypeEnum } from '../../../common/enums';
import Theme from '../../../themes/Theme';
import { TRANSLATIONS } from '../../../translations';
import GocardlessMembership from './GocardlessMembership';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: {
    id: '123',
    balance: '1.99',
    gocardless: { mandateStatus: 'applied' },
    tagIds: [],
  },
  onAnalyticsEvent: () => jest.fn(),
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  featureFlags: {
    enableInvoicesList: true,
    enableOfferDetails: true,
    enableRFID: true,
  },
  isInternetReachable: true,
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockUserInfoObj = {
  userInfo: {
    type: UserTypeEnum.NEW,
    gocardless: {},
  },
  isLoading: false,
};
const mockUseInfo = jest.fn().mockReturnValue(mockUserInfoObj);
jest.mock('../../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUseInfo(),
}));

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

jest.mock('@react-navigation/native', () => ({
  useIsFocused: () => {
    return true;
  },
}));

const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

jest.mock('../../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

jest.mock('../../../providers/SubsPreferencesProvider', () => ({
  useSubsPreferences: () => ({ subPreferences: {} }),
}));

const renderComponent = () =>
  render(
    <Theme>
      <GocardlessMembership />
    </Theme>,
  );

describe('Gocardless membership component', () => {
  it('should render correctly', () => {
    const { toJSON } = renderComponent();
    expect(toJSON()).toMatchSnapshot();
  });

  it('checks if Async Storage is used for replacecard and date difference is below 7days', async () => {
    const mockCardRequestDate = moment()
      .subtract(1, 'days')
      .format('YYYY-MM-DD');
    await AsyncStorage.setItem('replacecard', mockCardRequestDate);
    let cardRequestDate = await AsyncStorage.getItem('replacecard');

    expect(cardRequestDate).toBe(mockCardRequestDate);
    expect(AsyncStorage.getItem).toBeCalledWith('replacecard');
  });

  it('checks if Async Storage is used for replacecard and date difference is above 7days', async () => {
    const mockCardRequestDate = moment()
      .subtract(10, 'days')
      .format('YYYY-MM-DD');
    await AsyncStorage.setItem('replacecard', mockCardRequestDate);
    let cardRequestDate = await AsyncStorage.getItem('replacecard');

    expect(cardRequestDate).toBe(mockCardRequestDate);
    expect(AsyncStorage.getItem).toBeCalledWith('replacecard');
  });

  it('should navigate to VoucherScreen when user clicks on offercode', () => {
    renderComponent();

    const tableRow = screen.getByTestId(
      `${mockUseSettingsObj.t.gocardless.accessibility.OFFERCODE.LABEL}, ${mockUseSettingsObj.t.gocardless.accessibility.OFFERCODE.HINT}`,
    );

    fireEvent(tableRow, 'onPress');

    expect(mockHostNavigationObj.navigate).toHaveBeenCalled();
  });

  it('should navigate to RFIDForm when user clicks on replacecard', () => {
    renderComponent();

    const tableRow = screen.getByTestId(
      `${mockUseSettingsObj.t.gocardless.accessibility.REPLACECARD.LABEL}, ${mockUseSettingsObj.t.gocardless.accessibility.REPLACECARD.HINT}`,
    );

    fireEvent(tableRow, 'onPress');

    expect(mockHostNavigationObj.navigate).toHaveBeenCalled();
  });

  it('should return null view for Offer code Apply if enableOfferDetails is false', () => {
    mockUseSettings.mockReturnValueOnce({
      ...mockUseSettingsObj,
      featureFlags: {
        ...mockUseSettingsObj.featureFlags,
        enableOfferDetails: false,
      },
    });
    const { getByText } = renderComponent();
    act(() => {
      jest.useFakeTimers();
    });

    expect(
      getByText(
        mockUseSettingsObj.t.gocardless.accessibility.REPLACECARD.LABEL,
      ),
    ).toBeDefined();
  });

  it('should return null view for replace card if enableRFID flag is false', () => {
    mockUseSettings.mockReturnValueOnce({
      ...mockUseSettingsObj,
      featureFlags: {
        ...mockUseSettingsObj.featureFlags,
        enableRFID: false,
      },
    });
    renderComponent();
    act(() => {
      jest.useFakeTimers();
    });

    expect(
      screen.getByTestId(
        `${mockUseSettingsObj.t.gocardless.accessibility.OFFERCODE.LABEL}, ${mockUseSettingsObj.t.gocardless.accessibility.OFFERCODE.HINT}`,
      ),
    ).toBeDefined();
  });
});
