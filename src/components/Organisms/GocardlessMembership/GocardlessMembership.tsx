import AsyncStorage from '@react-native-async-storage/async-storage';
import { useIsFocused } from '@react-navigation/native';
import moment from 'moment';
import React, { useEffect, useState } from 'react';

import Bppulsecard from '../../../../assets/images/bppulse_card.png';
import BppulseLogo from '../../../../assets/images/bppulse_logo.svg';
import {
  SubsAnalyticsEventCancelSubscriptionClick,
  SubsAnalyticsEventMembershipApplyAnOfferCodeClick,
} from '../../../analytics/events';
import { SubscriptionScreenNames } from '../../../common/enums';
import { useHostNavigation } from '../../../providers/HostNavigationProvider';
import { useSettings } from '../../../providers/Settings';
import { useSubsPreferences } from '../../../providers/SubsPreferencesProvider';
import { useUserInfo } from '../../../providers/UserInfoProvider';
import { getAnalyticsPayload } from '../../../utils/helpers';
import { DownArrow, SpacerDynamic, Tablerow } from '../..';
import { membershipData } from '../JoinGocardless/JoinGocardless.functions';
import * as S from './GocardlessMembership.styles';

interface MembershipData {
  id: string;
  title: string;
  subtitle?: string;
  icon: any;
  status?: boolean;
}
interface Row {
  item: MembershipData;
  index: number;
}

export default () => {
  const { t, featureFlags, onAnalyticsEvent } = useSettings();
  const { userInfo } = useUserInfo();
  const { navigate } = useHostNavigation();
  const { enableInvoicesList, enableOfferDetails, enableRFID } = featureFlags;
  const { subPreferences } = useSubsPreferences();

  const expDate = subPreferences?.nextBillingDate;
  const cancelFlag = JSON.parse(subPreferences?.toBeCancelled ?? 'false');
  const expiryDate = expDate && moment(expDate).format('DD/MM/YY');

  const [cardRequestDate, setcardRequestDate] = useState<string | null>(null);
  const [cardStatus, setcardStatus] = useState('');

  const screenFocused = useIsFocused();

  useEffect(() => {
    if (screenFocused) {
      const getrequestedcardStatus = async () => {
        setcardRequestDate(await AsyncStorage.getItem('replacecard'));
      };
      getrequestedcardStatus();
    }
  }, [screenFocused]);

  useEffect(() => {
    if (cardRequestDate && cardRequestDate !== null) {
      const oldDate = moment(cardRequestDate, 'YYYY-MM-DD');
      const currentDate = moment().format('YYYY-MM-DD');
      const dateDifference = Math.abs(
        moment.duration(oldDate.diff(currentDate)).asDays(),
      );
      if (dateDifference <= 7) {
        setcardStatus('Requested');
      } else {
        setcardStatus('');
      }
    }
  }, [cardRequestDate]);

  const onPress = (item: MembershipData) => {
    if (item.id === 'offercode') {
      onAnalyticsEvent(
        SubsAnalyticsEventMembershipApplyAnOfferCodeClick(
          getAnalyticsPayload(userInfo),
        ),
      );
      return navigate(SubscriptionScreenNames.VoucherScreen, {
        from: SubscriptionScreenNames.GoCardlessMembership,
      });
    }
    if (item.id === 'replacecard' && cardStatus === '') {
      // Condition to check if already user has applied for replacing the card
      // If the replacement request is more than 7 days enable it else disable this section with a 'requested' pill
      return navigate(SubscriptionScreenNames.RFIDForm, {
        fromScreen: 'replaceCard',
      });
    }
    return null;
  };

  const renderItem = (row: Row) => {
    const { item } = row;
    const accessibilityText =
      t.gocardless.accessibility[
        item.id.toUpperCase() as keyof typeof t.gocardless.accessibility
      ];

    if (item.id === 'invoices' && !enableInvoicesList) {
      return null;
    }
    if (item.id === 'offercode' && !enableOfferDetails) {
      return null;
    }
    if (item.id === 'replacecard' && !enableRFID) {
      return null;
    }
    return (
      <Tablerow
        key={`gocardlessMembershipTableRow-${row.index}`}
        text={item.title}
        icon={item.icon}
        righticon={<DownArrow color="#000" />}
        onPress={() => onPress(item)}
        testID={`${accessibilityText.LABEL}, ${accessibilityText.HINT}`}
        accessibilityLabel={accessibilityText.LABEL}
        accessibilityHint={accessibilityText.HINT}
        statustext={item.id === 'replacecard' ? cardStatus : ''}
      />
    );
  };
  return (
    <S.ScreenWrapper contentContainerStyle={{ flex: 1 }}>
      <S.GoCardlessContainer>
        <SpacerDynamic vSpace={1} />
        <S.UpperViewContainer
          imageStyle={{ borderRadius: 8.4 }}
          source={Bppulsecard}
        >
          <SpacerDynamic vSpace={4} />
          <S.InnerLogoImageView>
            {/* <Image href={BppulseLogo} /> */}
            <BppulseLogo />
            <S.AccountContainer>
              {/* Hiding account holder UI until we can display the name correctly */}
              {/* <S.AccountHolderText>
                {t.myAccount.text.ACCOUNT_HOLDER}
              </S.AccountHolderText>
              <S.AccountHolderNameText>
                {user && `${user.given_name} ${user.family_name}`}
                NAME
              </S.AccountHolderNameText> */}
            </S.AccountContainer>
          </S.InnerLogoImageView>
        </S.UpperViewContainer>
        <SpacerDynamic vSpace={4} />
        <S.StatusOuterContainer>
          {!!expiryDate && cancelFlag && (
            <S.StatusContainer>
              <S.StatusText>{`Expiring on ${expiryDate}`}</S.StatusText>
            </S.StatusContainer>
          )}
          {!expiryDate && cancelFlag && (
            <S.StatusContainer>
              <S.StatusText>{t.gocardless.text.EXPIRING}</S.StatusText>
            </S.StatusContainer>
          )}
        </S.StatusOuterContainer>
        <SpacerDynamic vSpace={4} />
        <S.MiddleViewContainer>
          {membershipData(t).map((item, index) => {
            return renderItem({ item, index });
          })}
        </S.MiddleViewContainer>
        <SpacerDynamic vSpace={4} />
      </S.GoCardlessContainer>
      <S.BottomViewContainer>
        {!cancelFlag && (
          <S.UrlText
            onPress={() => {
              onAnalyticsEvent(
                SubsAnalyticsEventCancelSubscriptionClick(
                  getAnalyticsPayload(userInfo),
                ),
              );
              navigate(SubscriptionScreenNames.CancelMembership, {
                params: expiryDate,
              });
            }}
          >
            {t.gocardless.text.CANCEL_SUBSCRIPTION}
          </S.UrlText>
        )}
      </S.BottomViewContainer>
    </S.ScreenWrapper>
  );
};
