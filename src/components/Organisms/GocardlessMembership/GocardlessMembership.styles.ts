import { styled } from 'styled-components/native';

export const GoCardlessContainer = styled.View`
  flex: 0.85;
`;

export const ScreenWrapper = styled.ScrollView`
  flex: 1;
  background-color: #f5f5f5;
  overflow: scroll;
`;
export const UpperViewContainer = styled.ImageBackground`
  height: 202px;
  border-radius: 8.4px;
  max-width: 90%;
  margin-left: 16px;
  margin-right: 16px;
  box-shadow: 0 15px 10px ${(p: any) => p.theme.subscriptionMfe.color.primary};
  shadow-opacity: 0.5;
  display: flex;
  align-items: center;
`;

export const MiddleViewContainer = styled.View`
  background-color: ${(p: any) => p.theme.subscriptionMfe.color.white};
  border-top-width: 1px;
  border-color: ${(p: any) => p.theme.subscriptionMfe.table.border};
`;

export const PolarPulseLogo = styled.Image`
  height: 74px;
  width: 159px;
`;

export const PolarPulseBanner = styled.ImageBackground`
  width: 100%;
  height: 100%;
`;

export const InnerLogoImageView = styled.View`
  height: 101px;
  width: 100%;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
`;

export const AccountContainer = styled.View`
  width: 100%;
  justify-content: space-around;
  align-items: flex-start;
  margin-left: 24px;
  margin-top: 32px;
`;

export const AccountHolderText = styled.Text`
  height: 24px;
  color: ${(p) => p.theme.subscriptionMfe.gocardless.text.cardtext};
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 24px;
`;

export const AccountHolderNameText = styled.Text`
  height: 24px;
  width: 147px;
  color: ${(p: any) => p.theme.subscriptionMfe.color.white};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
`;

export const UrlText = styled.Text`
  color: ${(p) => p.theme.subscriptionMfe.color.state.error};
  height: 24px;
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
  text-decoration: underline;
  text-decoration-color: ${(p) => p.theme.subscriptionMfe.color.state.error};
`;

export const UrlBottomText = styled.Text`
  height: 100px;
  color: ${(p: any) => p.theme.subscriptionMfe.gocardless.text.header};
  font-size: 14px;
  letter-spacing: 0;
  line-height: 20px;
  text-align: center;
`;

export const BottomViewContainer = styled.View`
  padding-left: 16px;
  padding-right: 16px;
  justify-content: center;
  align-items: center;
  flex: 0.15;
`;

export const StatusOuterContainer = styled.View`
  padding-left: 16px;
  padding-right: 16px;
  justify-content: center;
  align-items: center;
`;
export const StatusContainer = styled.View`
  justify-content: center;
  align-items: center;
  border-radius: 13.5px;
  background-color: ${(props: any) =>
    props.theme.subscriptionMfe.voucher.color.status};
  padding-left: 4px;
  padding-right: 4px;
`;

export const StatusText = styled.Text`
  height: 24px;
  color: ${(props: any) => props.theme.subscriptionMfe.color.primary};
  font-size: 14px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
  padding: 0 4px;
`;
