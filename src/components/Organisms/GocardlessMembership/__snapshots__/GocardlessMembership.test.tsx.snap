// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Gocardless membership component should render correctly 1`] = `
<RCTScrollView
  contentContainerStyle={
    {
      "flex": 1,
    }
  }
  style={
    {
      "backgroundColor": "#f5f5f5",
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 1,
      "overflow": "scroll",
    }
  }
>
  <View>
    <View
      style={
        {
          "flexBasis": 0,
          "flexGrow": 0.85,
          "flexShrink": 1,
        }
      }
    >
      <View
        style={
          {
            "height": 13.34,
            "width": 0,
          }
        }
      />
      <View
        accessibilityIgnoresInvertColors={true}
        style={
          {
            "alignItems": "center",
            "borderBottomLeftRadius": 8.4,
            "borderBottomRightRadius": 8.4,
            "borderTopLeftRadius": 8.4,
            "borderTopRightRadius": 8.4,
            "display": "flex",
            "height": 202,
            "marginLeft": 16,
            "marginRight": 16,
            "maxWidth": "90%",
            "shadowColor": "#000096",
            "shadowOffset": {
              "height": 15,
              "width": 0,
            },
            "shadowOpacity": 0.5,
            "shadowRadius": 10,
          }
        }
      >
        <Image
          source={
            {
              "testUri": "../../../assets/images/bppulse_card.png",
            }
          }
          style={
            [
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              },
              {
                "height": 202,
                "width": undefined,
              },
              {
                "borderRadius": 8.4,
              },
            ]
          }
        />
        <View
          style={
            {
              "height": 53.36,
              "width": 0,
            }
          }
        />
        <View
          style={
            {
              "alignItems": "center",
              "height": 101,
              "justifyContent": "center",
              "marginTop": 24,
              "width": "100%",
            }
          }
        >
          <SvgMock />
          <View
            style={
              {
                "alignItems": "flex-start",
                "justifyContent": "space-around",
                "marginLeft": 24,
                "marginTop": 32,
                "width": "100%",
              }
            }
          />
        </View>
      </View>
      <View
        style={
          {
            "height": 53.36,
            "width": 0,
          }
        }
      />
      <View
        style={
          {
            "alignItems": "center",
            "justifyContent": "center",
            "paddingLeft": 16,
            "paddingRight": 16,
          }
        }
      />
      <View
        style={
          {
            "height": 53.36,
            "width": 0,
          }
        }
      />
      <View
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#D8D8D8",
            "borderTopWidth": 1,
          }
        }
      >
        <View
          accessibilityHint="Navigates to MembershipInvoices page"
          accessibilityLabel="Add offer code"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "borderBottomWidth": 1,
              "borderColor": "#D8D8D8",
              "borderTopWidth": 0,
              "flexDirection": "row",
              "justifyContent": "space-between",
              "opacity": 1,
              "paddingBottom": 16,
              "paddingTop": 16,
            }
          }
          testID="Add offer code, Navigates to MembershipInvoices page"
        >
          <View
            style={
              {
                "alignItems": "center",
                "flexDirection": "row",
                "flexShrink": 1,
                "justifyContent": "flex-start",
                "marginBottom": 0,
              }
            }
            type="primary"
          >
            <View
              style={
                {
                  "alignItems": "center",
                  "justifyContent": "center",
                  "paddingLeft": 32,
                  "paddingTop": 0,
                  "width": 20,
                }
              }
              type="primary"
            >
              <RNSVGSvgView
                align="xMidYMid"
                bbHeight="24"
                bbWidth="24"
                focusable={false}
                height="24"
                meetOrSlice={0}
                minX={0}
                minY={0}
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": 24,
                      "width": 24,
                    },
                  ]
                }
                vbHeight={24}
                vbWidth={24}
                width="24"
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGGroup
                    fill={null}
                    propList={
                      [
                        "fill",
                        "stroke",
                      ]
                    }
                    stroke={null}
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          -16,
                          -506,
                        ]
                      }
                    >
                      <RNSVGGroup
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        matrix={
                          [
                            1,
                            0,
                            0,
                            1,
                            0,
                            477,
                          ]
                        }
                      >
                        <RNSVGGroup
                          fill={
                            {
                              "payload": 4278190080,
                              "type": 0,
                            }
                          }
                          matrix={
                            [
                              1,
                              0,
                              0,
                              1,
                              16,
                              29,
                            ]
                          }
                        >
                          <RNSVGPath
                            d="M20,6 C21.1045695,6 22,6.8954305 22,8 L22,16 C22,17.1045695 21.1045695,18 20,18 L4,18 C2.8954305,18 2,17.1045695 2,16 L2,8 C2,6.8954305 2.8954305,6 4,6 L20,6 Z M20,7 L4,7 C3.48716416,7 3.06449284,7.38604019 3.00672773,7.88337887 L3,8 L3,16 C3,16.5128358 3.38604019,16.9355072 3.88337887,16.9932723 L4,17 L20,17 C20.5128358,17 20.9355072,16.6139598 20.9932723,16.1166211 L21,16 L21,8 C21,7.48716416 20.6139598,7.06449284 20.1166211,7.00672773 L20,7 Z M7.05163196,8.77871714 C7.23503881,8.40709429 7.76496119,8.40709429 7.94836804,8.77871714 L7.94836804,8.77871714 L8.566,10.032 L9.94954045,10.232654 C10.330357,10.2879899 10.4987552,10.7265019 10.2832676,11.0204684 L10.2832676,11.0204684 L10.2266471,11.0855007 L9.226,12.061 L9.46226729,13.43802 C9.52765213,13.8192428 9.15855166,14.1159918 8.81081455,13.9970736 L8.81081455,13.9970736 L8.73679257,13.9651082 L7.5,13.315 L6.26320743,13.9651082 C5.92084799,14.1450972 5.52456457,13.8857623 5.53020583,13.5182969 L5.53020583,13.5182969 L5.53773271,13.43802 L5.773,12.061 L4.77335286,11.0855007 C4.49779139,10.8168946 4.61930536,10.363149 4.96642759,10.2519856 L4.96642759,10.2519856 L5.05045955,10.232654 L6.433,10.032 Z M18.5,12 C18.7761424,12 19,12.2238576 19,12.5 C19,12.7761424 18.7761424,13 18.5,13 L17.5,13 C17.2238576,13 17,12.7761424 17,12.5 C17,12.2238576 17.2238576,12 17.5,12 L18.5,12 Z M18.5,9 C18.7761424,9 19,9.22385763 19,9.5 C19,9.77614237 18.7761424,10 18.5,10 L13.5,10 C13.2238576,10 13,9.77614237 13,9.5 C13,9.22385763 13.2238576,9 13.5,9 L18.5,9 Z"
                            fill={
                              {
                                "payload": 4278190230,
                                "type": 0,
                              }
                            }
                            propList={
                              [
                                "fill",
                              ]
                            }
                          />
                        </RNSVGGroup>
                      </RNSVGGroup>
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
            <View
              style={
                {
                  "flexDirection": "column",
                  "flexShrink": 1,
                  "justifyContent": "center",
                  "paddingLeft": 28,
                  "paddingRight": 8,
                }
              }
            >
              <Text
                ellipsizeMode="tail"
                style={
                  {
                    "alignItems": "flex-start",
                    "color": "#1D1D26",
                    "fontSize": 16,
                    "textAlign": "left",
                  }
                }
                textColor="#1D1D26"
              >
                Apply an offer code
              </Text>
            </View>
          </View>
          <View
            style={
              {
                "alignItems": "center",
                "height": 30,
                "justifyContent": "center",
                "paddingBottom": 32,
                "transform": [
                  {
                    "rotate": "-90deg",
                  },
                ],
                "width": 30,
              }
            }
          >
            <RNSVGSvgView
              align="xMidYMid"
              bbHeight="8"
              bbWidth="14"
              focusable={false}
              height="8"
              meetOrSlice={0}
              minX={0}
              minY={0}
              style={
                [
                  {
                    "backgroundColor": "transparent",
                    "borderWidth": 0,
                  },
                  {
                    "flex": 0,
                    "height": 8,
                    "width": 14,
                  },
                ]
              }
              vbHeight={8}
              vbWidth={14}
              width="14"
            >
              <RNSVGGroup
                fill={
                  {
                    "payload": 4278190080,
                    "type": 0,
                  }
                }
              >
                <RNSVGGroup
                  fill={null}
                  name="illustarations-and-Icons"
                  propList={
                    [
                      "fill",
                      "stroke",
                    ]
                  }
                  stroke={null}
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        -593,
                        -1706,
                      ]
                    }
                    name="Icons"
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          588,
                          1698,
                        ]
                      }
                      name="atom-/-icon-/-line-/-custom-/-dropdown"
                    >
                      <RNSVGPath
                        d="M5.64644661,8.64644661 C5.82001296,8.47288026 6.08943736,8.45359511 6.2843055,8.58859116 L6.35355339,8.64644661 L12,14.293 L17.6464466,8.64644661 C17.820013,8.47288026 18.0894374,8.45359511 18.2843055,8.58859116 L18.3535534,8.64644661 C18.5271197,8.82001296 18.5464049,9.08943736 18.4114088,9.2843055 L18.3535534,9.35355339 L12.3535534,15.3535534 C12.179987,15.5271197 11.9105626,15.5464049 11.7156945,15.4114088 L11.6464466,15.3535534 L5.64644661,9.35355339 C5.45118446,9.15829124 5.45118446,8.84170876 5.64644661,8.64644661 Z"
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        name="Path-8"
                        propList={
                          [
                            "fill",
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGSvgView>
          </View>
        </View>
        <View
          accessibilityHint="Navigates to RFID form screen"
          accessibilityLabel="Replace card or key fob"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "borderBottomWidth": 1,
              "borderColor": "#D8D8D8",
              "borderTopWidth": 0,
              "flexDirection": "row",
              "justifyContent": "space-between",
              "opacity": 1,
              "paddingBottom": 16,
              "paddingTop": 16,
            }
          }
          testID="Replace card or key fob, Navigates to RFID form screen"
        >
          <View
            style={
              {
                "alignItems": "center",
                "flexDirection": "row",
                "flexShrink": 1,
                "justifyContent": "flex-start",
                "marginBottom": 0,
              }
            }
            type="primary"
          >
            <View
              style={
                {
                  "alignItems": "center",
                  "justifyContent": "center",
                  "paddingLeft": 32,
                  "paddingTop": 0,
                  "width": 20,
                }
              }
              type="primary"
            >
              <RNSVGSvgView
                align="xMidYMid"
                bbHeight="20"
                bbWidth="20"
                focusable={false}
                height="20"
                meetOrSlice={0}
                minX={0}
                minY={0}
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": 20,
                      "width": 20,
                    },
                  ]
                }
                vbHeight={20}
                vbWidth={20}
                width="20"
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGGroup
                    fill={null}
                    propList={
                      [
                        "fill",
                        "stroke",
                      ]
                    }
                    stroke={null}
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          -590,
                          -1556,
                        ]
                      }
                    >
                      <RNSVGGroup
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        matrix={
                          [
                            1,
                            0,
                            0,
                            1,
                            588,
                            1554,
                          ]
                        }
                      >
                        <RNSVGPath
                          d="M14,6 C14.2761424,6 14.5,6.22385763 14.5,6.5 C14.5,6.74545989 14.3231248,6.94960837 14.0898756,6.99194433 L14,7 L4.5,7 C3.72030388,7 3.07955132,7.59488808 3.00686658,8.35553999 L3,8.5 L3,19.5 C3,20.2796961 3.59488808,20.9204487 4.35553999,20.9931334 L4.5,21 L15.5,21 C16.2796961,21 16.9204487,20.4051119 16.9931334,19.64446 L17,19.5 L17,10 C17,9.72385763 17.2238576,9.5 17.5,9.5 C17.7454599,9.5 17.9496084,9.67687516 17.9919443,9.91012437 L18,10 L18,19.5 C18,20.8254834 16.9684641,21.9100387 15.6643757,21.9946823 L15.5,22 L4.5,22 C3.1745166,22 2.08996133,20.9684641 2.00531768,19.6643757 L2,19.5 L2,8.5 C2,7.1745166 3.03153594,6.08996133 4.33562431,6.00531768 L4.5,6 L14,6 Z M7.5,10.5 C8.88071187,10.5 10,11.6192881 10,13 C10,14.0880385 9.30493675,15.0137363 8.33461011,15.3572936 L9.9472136,17.7763932 C10.0707082,18.0233825 9.97059605,18.323719 9.7236068,18.4472136 C9.5040608,18.5569866 9.24236387,18.4900826 9.10018522,18.3003914 L9.0527864,18.2236068 L7.0527864,15.2236068 C6.92929178,14.9766175 7.02940395,14.676281 7.2763932,14.5527864 C7.32447207,14.528747 7.3745724,14.5131805 7.42499629,14.5055211 L7.5,14.5 C8.32842712,14.5 9,13.8284271 9,13 C9,12.2203039 8.40511192,11.5795513 7.64446001,11.5068666 L7.5,11.5 L6.5,11.5 C6.25454011,11.5 6.05039163,11.6768752 6.00805567,11.9101244 L6,12 L6,18 C6,18.2761424 5.77614237,18.5 5.5,18.5 C5.25454011,18.5 5.05039163,18.3231248 5.00805567,18.0898756 L5,18 L5,12 C5,11.2203039 5.59488808,10.5795513 6.35553999,10.5068666 L6.5,10.5 L7.5,10.5 Z M15,10.5 C15.2761424,10.5 15.5,10.7238576 15.5,11 C15.5,11.2454599 15.3231248,11.4496084 15.0898756,11.4919443 L15,11.5 L13,11.5 C12.7545401,11.5 12.5503916,11.6768752 12.5080557,11.9101244 L12.5,12 L12.5,13.5 L14,13.5 C14.2761424,13.5 14.5,13.7238576 14.5,14 C14.5,14.2454599 14.3231248,14.4496084 14.0898756,14.4919443 L14,14.5 L12.5,14.5 L12.5,18 C12.5,18.2761424 12.2761424,18.5 12,18.5 C11.7545401,18.5 11.5503916,18.3231248 11.5080557,18.0898756 L11.5,18 L11.5,12 C11.5,11.2203039 12.0948881,10.5795513 12.85554,10.5068666 L13,10.5 L15,10.5 Z M20.6819805,3.31801948 C22.4393398,5.0753788 22.4393398,7.9246212 20.6819805,9.68198052 C20.4867184,9.87724266 20.1701359,9.87724266 19.9748737,9.68198052 C19.7796116,9.48671837 19.7796116,9.17013588 19.9748737,8.97487373 C21.3417088,7.60803871 21.3417088,5.39196129 19.9748737,4.02512627 C18.6080387,2.65829124 16.3919613,2.65829124 15.0251263,4.02512627 C14.8298641,4.22038841 14.5132816,4.22038841 14.3180195,4.02512627 C14.1227573,3.82986412 14.1227573,3.51328163 14.3180195,3.31801948 C16.0753788,1.56066017 18.9246212,1.56066017 20.6819805,3.31801948 Z M17.5,5 C18.3284271,5 19,5.67157288 19,6.5 C19,7.32842712 18.3284271,8 17.5,8 C16.6715729,8 16,7.32842712 16,6.5 C16,5.67157288 16.6715729,5 17.5,5 Z M17.5,6 C17.2238576,6 17,6.22385763 17,6.5 C17,6.77614237 17.2238576,7 17.5,7 C17.7761424,7 18,6.77614237 18,6.5 C18,6.22385763 17.7761424,6 17.5,6 Z"
                          fill={
                            {
                              "payload": 4278190230,
                              "type": 0,
                            }
                          }
                          propList={
                            [
                              "fill",
                            ]
                          }
                        />
                      </RNSVGGroup>
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
            <View
              style={
                {
                  "flexDirection": "column",
                  "flexShrink": 1,
                  "justifyContent": "center",
                  "paddingLeft": 28,
                  "paddingRight": 8,
                }
              }
            >
              <Text
                ellipsizeMode="tail"
                style={
                  {
                    "alignItems": "flex-start",
                    "color": "#1D1D26",
                    "fontSize": 16,
                    "textAlign": "left",
                  }
                }
                textColor="#1D1D26"
              >
                Replace card or key fob
              </Text>
            </View>
          </View>
          <View
            style={
              {
                "alignItems": "center",
                "height": 30,
                "justifyContent": "center",
                "paddingBottom": 32,
                "transform": [
                  {
                    "rotate": "-90deg",
                  },
                ],
                "width": 30,
              }
            }
          >
            <RNSVGSvgView
              align="xMidYMid"
              bbHeight="8"
              bbWidth="14"
              focusable={false}
              height="8"
              meetOrSlice={0}
              minX={0}
              minY={0}
              style={
                [
                  {
                    "backgroundColor": "transparent",
                    "borderWidth": 0,
                  },
                  {
                    "flex": 0,
                    "height": 8,
                    "width": 14,
                  },
                ]
              }
              vbHeight={8}
              vbWidth={14}
              width="14"
            >
              <RNSVGGroup
                fill={
                  {
                    "payload": 4278190080,
                    "type": 0,
                  }
                }
              >
                <RNSVGGroup
                  fill={null}
                  name="illustarations-and-Icons"
                  propList={
                    [
                      "fill",
                      "stroke",
                    ]
                  }
                  stroke={null}
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        -593,
                        -1706,
                      ]
                    }
                    name="Icons"
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          588,
                          1698,
                        ]
                      }
                      name="atom-/-icon-/-line-/-custom-/-dropdown"
                    >
                      <RNSVGPath
                        d="M5.64644661,8.64644661 C5.82001296,8.47288026 6.08943736,8.45359511 6.2843055,8.58859116 L6.35355339,8.64644661 L12,14.293 L17.6464466,8.64644661 C17.820013,8.47288026 18.0894374,8.45359511 18.2843055,8.58859116 L18.3535534,8.64644661 C18.5271197,8.82001296 18.5464049,9.08943736 18.4114088,9.2843055 L18.3535534,9.35355339 L12.3535534,15.3535534 C12.179987,15.5271197 11.9105626,15.5464049 11.7156945,15.4114088 L11.6464466,15.3535534 L5.64644661,9.35355339 C5.45118446,9.15829124 5.45118446,8.84170876 5.64644661,8.64644661 Z"
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        name="Path-8"
                        propList={
                          [
                            "fill",
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGSvgView>
          </View>
        </View>
      </View>
      <View
        style={
          {
            "height": 53.36,
            "width": 0,
          }
        }
      />
    </View>
    <View
      style={
        {
          "alignItems": "center",
          "flexBasis": 0,
          "flexGrow": 0.15,
          "flexShrink": 1,
          "justifyContent": "center",
          "paddingLeft": 16,
          "paddingRight": 16,
        }
      }
    >
      <Text
        onPress={[Function]}
        style={
          {
            "color": "#CF0001",
            "fontSize": 16,
            "height": 24,
            "letterSpacing": 0,
            "lineHeight": 24,
            "textAlign": "center",
            "textDecorationColor": "#CF0001",
            "textDecorationLine": "underline",
            "textDecorationStyle": "solid",
          }
        }
      >
        Cancel subscription
      </Text>
    </View>
  </View>
</RCTScrollView>
`;
