import { MandateStatus } from '../common/enums';
import { Tags } from '../common/interfaces';

export type EventPayloadType = {
  account_balance: number;
  gocardless_mandate_status: MandateStatus | string;
  tag_ids: Array<Tags>;
  first_time_subscribing?: boolean;
  offer_code?: string;
  currency?: string;
  value?: number;
  subscription_id?: string;
  discount?: number;
};

export type EventPayloadFirstTimeSubscribingType = {
  first_time_subscribing: boolean | undefined;
};

export type EventPayloadCVVRecacheFlowType = {
  user_journey: string;
};

export type EventErrorPayloadType = {
  errorMessage: string;
};
