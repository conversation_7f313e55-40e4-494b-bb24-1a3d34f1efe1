import type {
  SubsAnalyticsEventAddOfferCodeApplyErrorType,
  SubsAnalyticsEventAddOfferCodeApplySuccessType,
  SubsAnalyticsEventAddOfferCodeScreenOpenType,
  SubsAnalyticsEventCancelSubscriptionClickType,
  SubsAnalyticsEventCancelSubscriptionCompleteType,
  SubsAnalyticsEventEnterAddressScreenOpenType,
  SubsAnalyticsEventIntroOfferScreenOpenType,
  SubsAnalyticsEventMembershipApplyAnOfferCodeClickType,
  SubsAnalyticsEventPendingScreenOpenType,
  SubsAnalyticsEventReactivateSubsScreenOpenType,
  SubsAnalyticsEventRFIDPreferenceScreenOpenType,
  SubsAnalyticsEventSetupCompleteType,
  SubsAnalyticsEventSetupDirectDebitScreenOpenType,
  SubsAnalyticsEventSubsCancelSubscriptionFailedType,
  SubsAnalyticsEventSubsConfirmAndAuthoriseClickType,
  SubsAnalyticsEventSubsConfirmSubscriptionOpenType,
  SubsAnalyticsEventSubsMySubscriptionScreenOpenType,
  SubsAnalyticsEventSubsOfferCodeCheckClickType,
  SubsAnalyticsEventSubsOfferCodeCheckErrorType,
  SubsAnalyticsEventSubsOfferCodeCheckSuccessType,
  SubsAnalyticsEventSubsSetupFailureOpenType,
  SubsAnalyticsEventSubsSetupFailureRetryClickType,
  SubsAnalyticsEventSubsSubscribeAndSaveScreenCTAClickType,
  SubsAnalyticsEventSubsSubscribeAndSaveScreenOpenType,
  SubsAnalyticsEventSubsSuccessScreenOrderRFIDClickType,
  SubsAnalyticsEventSubsSuccessScreenStartChargingClickType,
  SubsAnalyticsEventSubsUnpaidSubscriptionScreenExitClickType,
  SubsAnalyticsEventSubsUnpaidSubscriptionScreenOpenType,
  SubsAnalyticsEventSubsUnpaidSubscriptionScreenRetryClickType,
  SubsAnalyticsEventSubsUnpaidSubsDowngradeFailureType,
  SubsAnalyticsEventSubsUnpaidSubsDowngradeSuccessType,
  SubsAnalyticsEventSubsUnpaidSubsRetryPaymentFailureType,
  SubsAnalyticsEventSubsUnpaidSubsRetryPaymentSuccessType,
  SubsAnalyticsEventSubsWalletAddCardFailureType,
  SubsAnalyticsEventUpgradePaymentRequiredOpenType,
  SubsAnalyticsSubsWalletAddCardSuccessType,
} from './events';

export enum SubsAnalyticsEvent {
  SUBS_INTRO_OFFER_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS_INTRO_OFFER_SCREEN_OPEN',
  SUBS_REACTIVATE_SUBS_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS_REACTIVATE_SUBS_SCREEN_OPEN',
  SUBS_RFID_PREFERENCE_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS_RFID_PREFERENCE_SCREEN_OPEN',
  SUBS_ENTER_ADDRESS_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS_ENTER_ADDRESS_SCREEN_OPEN',
  SUBS_ADD_OFFER_CODE_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_SCREEN_OPEN',
  SUBS_ADD_OFFER_CODE_APPLY_SUCCESS = 'SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_SUCCESS',
  SUBS_ADD_OFFER_CODE_APPLY_ERROR = 'SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_ERROR',
  SUBS_SETUP_DIRECT_DEBIT_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS__SETUP_DIRECT_DEBIT_SCREEN_OPEN',
  SUBS_SETUP_COMPLETE = 'SubsAnalyticsEvent.SUBS_SETUP_COMPLETE',
  SUBS_PENDING_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS_PENDING_SCREEN_OPEN',
  SUBS_CANCEL_SUBSCRIPTION_CLICK = 'SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_CLICK',
  SUBS_CANCEL_SUBSCRIPTION_COMPLETE = 'SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_COMPLETE',
  SUBS_MEMBERSHIP_APPLY_AN_OFFER_CODE_CLICK = 'SubsAnalyticsEvent.SUBS_MEMBERSHIP_APPLY_AN_OFFER_CODE_CLICK',
  SUBS_UPGRADE_PAYMENTREQUIRED_OPEN = 'SubsAnalyticsEvent.SUBS_UPGRADE_PAYMENTREQUIRED_OPEN',
  SUBS_CANCEL_SUBSCRIPTION_FAILED = 'SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_FAILED',
  SUBS_WALLET_ADD_CARD_SUCCESS = 'SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_SUCCESS',
  SUBS_WALLET_ADD_CARD_FAILURE = 'SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_FAILURE',
  SUBS_SUBSCRIBE_AND_SAVE_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_OPEN',
  SUBS_MY_SUBSCRIPTION_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS_MY_SUBSCRIPTION_SCREEN_OPEN',
  SUBS_SUBSCRIBE_AND_SAVE_SCREEN_CTA_CLICK = 'SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_CTA_CLICK',
  SUBS_CONFIRM_SUBSCRIPTION_OPEN = 'SubsAnalyticsEvent.SUBS_CONFIRM_SUBSCRIPTION_OPEN',
  SUBS_OFFER_CODE_CHECK_CLICK = 'SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_CLICK',
  SUBS_OFFER_CODE_CHECK_SUCCESS = 'SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_SUCCESS',
  SUBS_OFFER_CODE_CHECK_ERROR = 'SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_ERROR',
  SUBS_CONFIRM_AND_AUTHORISE_CLICK = 'SubsAnalyticsEvent.SUBS_CONFIRM_AND_AUTHORISE_CLICK',
  SUBS_SUCCESS_SCREEN_START_CHARGING_CLICK = 'SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_START_CHARGING_CLICK',
  SUBS_SUCCESS_SCREEN_ORDER_RFID_CLICK = 'SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_ORDER_RFID_CLICK',
  SUBS_ORDER_RFID_CARD_SUCCESS = 'SubsAnalyticsEvent.SUBS_ORDER_RFID_CARD_SUCCESS',
  SUBS_ORDER_RFID_CARD_FAILURE = 'SubsAnalyticsEvent.SUBS_ORDER_RFID_CARD_FAILURE',
  SUBS_SETUP_FAILURE_OPEN = 'SubsAnalyticsEvent.SUBS_SETUP_FAILURE_OPEN',
  SUBS_SETUP_FAILURE_RETRY_CLICK = 'SubsAnalyticsEvent.SUBS_SETUP_FAILURE_RETRY_CLICK',
  SUBS_UNPAID_SUBSCRIPTION_SCREEN_OPEN = 'SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_OPEN',
  SUBS_UNPAID_SUBSCRIPTION_SCREEN_RETRY_CLICK = 'SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_RETRY_CLICK',
  SUBS_UNPAID_SUBS_RETRY_PAYMENT_SUCCESS = 'SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_SUCCESS',
  SUBS_UNPAID_SUBS_RETRY_PAYMENT_FAILURE = 'SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_FAILURE',
  SUBS_UNPAID_SUBSCRIPTION_EXIT_CLICK = 'SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_EXIT_CLICK',
  SUBS_UNPAID_SUBS_DOWNGRADE_SUCCESS = 'SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_SUCCESS',
  SUBS_UNPAID_SUBS_DOWNGRADE_FAILURE = 'SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_FAILURE',
}

export type SubsAnalyticsEventType =
  | SubsAnalyticsEventIntroOfferScreenOpenType
  | SubsAnalyticsEventReactivateSubsScreenOpenType
  | SubsAnalyticsEventRFIDPreferenceScreenOpenType
  | SubsAnalyticsEventEnterAddressScreenOpenType
  | SubsAnalyticsEventAddOfferCodeScreenOpenType
  | SubsAnalyticsEventAddOfferCodeApplySuccessType
  | SubsAnalyticsEventAddOfferCodeApplyErrorType
  | SubsAnalyticsEventSetupDirectDebitScreenOpenType
  | SubsAnalyticsEventSetupCompleteType
  | SubsAnalyticsEventPendingScreenOpenType
  | SubsAnalyticsEventCancelSubscriptionClickType
  | SubsAnalyticsEventCancelSubscriptionCompleteType
  | SubsAnalyticsEventMembershipApplyAnOfferCodeClickType
  | SubsAnalyticsEventUpgradePaymentRequiredOpenType
  | SubsAnalyticsEventSubsCancelSubscriptionFailedType
  | SubsAnalyticsSubsWalletAddCardSuccessType
  | SubsAnalyticsEventSubsWalletAddCardFailureType
  | SubsAnalyticsEventSubsSubscribeAndSaveScreenOpenType
  | SubsAnalyticsEventSubsMySubscriptionScreenOpenType
  | SubsAnalyticsEventSubsSubscribeAndSaveScreenCTAClickType
  | SubsAnalyticsEventSubsConfirmSubscriptionOpenType
  | SubsAnalyticsEventSubsOfferCodeCheckClickType
  | SubsAnalyticsEventSubsOfferCodeCheckSuccessType
  | SubsAnalyticsEventSubsOfferCodeCheckErrorType
  | SubsAnalyticsEventSubsConfirmAndAuthoriseClickType
  | SubsAnalyticsEventSubsSuccessScreenStartChargingClickType
  | SubsAnalyticsEventSubsSuccessScreenOrderRFIDClickType
  | SubsAnalyticsEventSubsSetupFailureOpenType
  | SubsAnalyticsEventSubsSetupFailureRetryClickType
  | SubsAnalyticsEventSubsUnpaidSubscriptionScreenOpenType
  | SubsAnalyticsEventSubsUnpaidSubscriptionScreenRetryClickType
  | SubsAnalyticsEventSubsUnpaidSubsRetryPaymentSuccessType
  | SubsAnalyticsEventSubsUnpaidSubsRetryPaymentFailureType
  | SubsAnalyticsEventSubsUnpaidSubscriptionScreenExitClickType
  | SubsAnalyticsEventSubsUnpaidSubsDowngradeSuccessType
  | SubsAnalyticsEventSubsUnpaidSubsDowngradeFailureType;
