import { SubsAnalyticsEvent } from '../index';
import { EventPayloadFirstTimeSubscribingType } from '../types';
/* Event type */
export type SubsAnalyticsSubsWalletAddCardSuccessType = {
  type: SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_SUCCESS;
  payload: EventPayloadFirstTimeSubscribingType;
};

/* Event generator */
export const SubsAnalyticsSubsWalletAddCardSuccess = (
  payload: EventPayloadFirstTimeSubscribingType,
): SubsAnalyticsSubsWalletAddCardSuccessType => ({
  type: SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_SUCCESS,
  payload,
});
