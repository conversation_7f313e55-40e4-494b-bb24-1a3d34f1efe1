import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  first_time_subscribing: boolean;
  entered_value: string;
};

/* Event type */
export type SubsAnalyticsEventSubsOfferCodeCheckClickType = {
  type: SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_CLICK;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsOfferCodeCheckClick = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsOfferCodeCheckClickType => ({
  type: SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_CLICK,
  payload,
});
