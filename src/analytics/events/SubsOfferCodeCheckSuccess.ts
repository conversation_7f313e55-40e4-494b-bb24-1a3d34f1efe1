import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  first_time_subscribing: boolean;
  entered_value: string;
};
/* Event type */
export type SubsAnalyticsEventSubsOfferCodeCheckSuccessType = {
  type: SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_SUCCESS;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsOfferCodeCheckSuccess = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsOfferCodeCheckSuccessType => ({
  type: SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_SUCCESS,
  payload,
});
