import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventIntroOfferScreenOpenType = {
  type: SubsAnalyticsEvent.SUBS_INTRO_OFFER_SCREEN_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventIntroOfferScreenOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventIntroOfferScreenOpenType => ({
  type: SubsAnalyticsEvent.SUBS_INTRO_OFFER_SCREEN_OPEN,
  payload,
});
