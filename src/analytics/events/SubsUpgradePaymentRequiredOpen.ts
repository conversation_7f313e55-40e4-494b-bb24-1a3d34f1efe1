import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventUpgradePaymentRequiredOpenType = {
  type: SubsAnalyticsEvent.SUBS_UPGRADE_PAYMENTREQUIRED_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventUpgradePaymentRequiredOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventUpgradePaymentRequiredOpenType => ({
  type: SubsAnalyticsEvent.SUBS_UPGRADE_PAYMENTREQUIRED_OPEN,
  payload,
});
