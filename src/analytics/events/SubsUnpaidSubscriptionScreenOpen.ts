import { Tags } from '../../common/interfaces';
import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  tag_ids: Array<Tags>;
};

/* Event type */
export type SubsAnalyticsEventSubsUnpaidSubscriptionScreenOpenType = {
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsUnpaidSubscriptionScreenOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsUnpaidSubscriptionScreenOpenType => ({
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_OPEN,
  payload,
});
