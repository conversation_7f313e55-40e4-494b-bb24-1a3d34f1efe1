import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  first_time_subscribing: boolean;
  entered_value: string;
};

/* Event type */
export type SubsAnalyticsEventSubsSetupFailureRetryClickType = {
  type: SubsAnalyticsEvent.SUBS_SETUP_FAILURE_RETRY_CLICK;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsSetupFailureRetryClick = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsSetupFailureRetryClickType => ({
  type: SubsAnalyticsEvent.SUBS_SETUP_FAILURE_RETRY_CLICK,
  payload,
});
