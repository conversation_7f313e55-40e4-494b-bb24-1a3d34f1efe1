import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  error_message: string;
};

/* Event type */
export type SubsAnalyticsEventSubsCancelSubscriptionFailedType = {
  type: SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_FAILED;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsCancelSubscriptionFailed = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsCancelSubscriptionFailedType => ({
  type: SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_FAILED,
  payload,
});
