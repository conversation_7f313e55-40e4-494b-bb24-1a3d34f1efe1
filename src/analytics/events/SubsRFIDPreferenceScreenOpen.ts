import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventRFIDPreferenceScreenOpenType = {
  type: SubsAnalyticsEvent.SUBS_RFID_PREFERENCE_SCREEN_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventRFIDPreferenceScreenOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventRFIDPreferenceScreenOpenType => ({
  type: SubsAnalyticsEvent.SUBS_RFID_PREFERENCE_SCREEN_OPEN,
  payload,
});
