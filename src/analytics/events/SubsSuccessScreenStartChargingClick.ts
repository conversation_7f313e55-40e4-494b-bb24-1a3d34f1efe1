import { Tags } from '../../common/interfaces';
import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  tag_ids: Array<Tags>;
  first_time_subscribing: boolean;
  offer_code: string;
};
/* Event type */
export type SubsAnalyticsEventSubsSuccessScreenStartChargingClickType = {
  type: SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_START_CHARGING_CLICK;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsSuccessScreenStartChargingClick = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsSuccessScreenStartChargingClickType => ({
  type: SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_START_CHARGING_CLICK,
  payload,
});
