import { Tags } from '../../common/interfaces';
import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  tag_ids: Array<Tags>;
  first_time_subscribing: boolean;
  offer_code: string | null;
};
/* Event type */
export type SubsAnalyticsEventSubsConfirmAndAuthoriseClickType = {
  type: SubsAnalyticsEvent.SUBS_CONFIRM_AND_AUTHORISE_CLICK;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsConfirmAndAuthoriseClick = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsConfirmAndAuthoriseClickType => ({
  type: SubsAnalyticsEvent.SUBS_CONFIRM_AND_AUTHORISE_CLICK,
  payload,
});
