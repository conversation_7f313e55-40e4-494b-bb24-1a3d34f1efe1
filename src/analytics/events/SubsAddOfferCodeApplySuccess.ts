import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';
/* Event type */
export type SubsAnalyticsEventAddOfferCodeApplySuccessType = {
  type: SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_SUCCESS;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventAddOfferCodeApplySuccess = (
  payload: EventPayloadType,
): SubsAnalyticsEventAddOfferCodeApplySuccessType => ({
  type: SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_SUCCESS,
  payload,
});
