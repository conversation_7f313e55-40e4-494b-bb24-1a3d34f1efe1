import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventEnterAddressScreenOpenType = {
  type: SubsAnalyticsEvent.SUBS_ENTER_ADDRESS_SCREEN_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventEnterAddressScreenOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventEnterAddressScreenOpenType => ({
  type: SubsAnalyticsEvent.SUBS_ENTER_ADDRESS_SCREEN_OPEN,
  payload,
});
