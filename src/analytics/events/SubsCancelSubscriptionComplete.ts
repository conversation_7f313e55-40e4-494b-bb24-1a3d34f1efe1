import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventCancelSubscriptionCompleteType = {
  type: SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_COMPLETE;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventCancelSubscriptionComplete = (
  payload: EventPayloadType,
): SubsAnalyticsEventCancelSubscriptionCompleteType => ({
  type: SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_COMPLETE,
  payload,
});
