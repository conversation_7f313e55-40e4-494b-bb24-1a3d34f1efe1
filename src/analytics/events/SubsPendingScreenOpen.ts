import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventPendingScreenOpenType = {
  type: SubsAnalyticsEvent.SUBS_PENDING_SCREEN_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventPendingScreenOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventPendingScreenOpenType => ({
  type: SubsAnalyticsEvent.SUBS_PENDING_SCREEN_OPEN,
  payload,
});
