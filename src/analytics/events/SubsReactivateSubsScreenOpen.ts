import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventReactivateSubsScreenOpenType = {
  type: SubsAnalyticsEvent.SUBS_REACTIVATE_SUBS_SCREEN_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventReactivateSubsScreenOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventReactivateSubsScreenOpenType => ({
  type: SubsAnalyticsEvent.SUBS_REACTIVATE_SUBS_SCREEN_OPEN,
  payload,
});
