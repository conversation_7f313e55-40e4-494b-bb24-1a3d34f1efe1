import { Tags } from '../../common/interfaces';
import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  tag_ids: Array<Tags>;
};

/* Event type */
export type SubsAnalyticsEventSubsUnpaidSubscriptionScreenExitClickType = {
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_EXIT_CLICK;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsUnpaidSubscriptionScreenExitClick = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsUnpaidSubscriptionScreenExitClickType => ({
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_EXIT_CLICK,
  payload,
});
