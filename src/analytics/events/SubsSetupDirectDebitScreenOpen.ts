import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventSetupDirectDebitScreenOpenType = {
  type: SubsAnalyticsEvent.SUBS_SETUP_DIRECT_DEBIT_SCREEN_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSetupDirectDebitScreenOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventSetupDirectDebitScreenOpenType => ({
  type: SubsAnalyticsEvent.SUBS_SETUP_DIRECT_DEBIT_SCREEN_OPEN,
  payload,
});
