import { Tags } from '../../common/interfaces';
import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  tag_ids: Array<Tags>;
};

/* Event type */
export type SubsAnalyticsEventSubsUnpaidSubscriptionScreenRetryClickType = {
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_RETRY_CLICK;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsUnpaidSubscriptionScreenRetryClick = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsUnpaidSubscriptionScreenRetryClickType => ({
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBSCRIPTION_SCREEN_RETRY_CLICK,
  payload,
});
