import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  first_time_subscribing: boolean;
  offer_code: string;
};
/* Event type */
export type SubsAnalyticsEventSubsSuccessScreenOrderRFIDClickType = {
  type: SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_ORDER_RFID_CLICK;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsSuccessScreenOrderRFIDClick = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsSuccessScreenOrderRFIDClickType => ({
  type: SubsAnalyticsEvent.SUBS_SUCCESS_SCREEN_ORDER_RFID_CLICK,
  payload,
});
