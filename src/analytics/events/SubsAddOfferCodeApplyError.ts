import { SubsAnalyticsEvent } from '../index';
import { EventErrorPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventAddOfferCodeApplyErrorType = {
  type: SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_ERROR;
  payload: EventErrorPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventAddOfferCodeApplyError = (
  payload: EventErrorPayloadType,
): SubsAnalyticsEventAddOfferCodeApplyErrorType => ({
  type: SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_ERROR,
  payload,
});
