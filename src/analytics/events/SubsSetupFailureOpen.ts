import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  first_time_subscribing: boolean;
  entered_value: string;
  error_message: string;
};

/* Event type */
export type SubsAnalyticsEventSubsSetupFailureOpenType = {
  type: SubsAnalyticsEvent.SUBS_SETUP_FAILURE_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsSetupFailureOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsSetupFailureOpenType => ({
  type: SubsAnalyticsEvent.SUBS_SETUP_FAILURE_OPEN,
  payload,
});
