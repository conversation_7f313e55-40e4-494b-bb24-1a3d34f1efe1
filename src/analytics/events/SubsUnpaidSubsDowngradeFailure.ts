import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  error_message: string;
};

/* Event type */
export type SubsAnalyticsEventSubsUnpaidSubsDowngradeFailureType = {
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_FAILURE;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsUnpaidSubsDowngradeFailure = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsUnpaidSubsDowngradeFailureType => ({
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_FAILURE,
  payload,
});
