import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  entered_value: string;
  error_message: string;
};
/* Event type */
export type SubsAnalyticsEventSubsOfferCodeCheckErrorType = {
  type: SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_ERROR;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsOfferCodeCheckError = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsOfferCodeCheckErrorType => ({
  type: SubsAnalyticsEvent.SUBS_OFFER_CODE_CHECK_ERROR,
  payload,
});
