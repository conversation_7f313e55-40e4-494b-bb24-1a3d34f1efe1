import { Tags } from '../../common/interfaces';
import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  tag_ids: Array<Tags>;
  first_time_subscribing: boolean;
};

/* Event type */
export type SubsAnalyticsEventSubsSubscribeAndSaveScreenOpenType = {
  type: SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsSubscribeAndSaveScreenOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsSubscribeAndSaveScreenOpenType => ({
  type: SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_OPEN,
  payload,
});
