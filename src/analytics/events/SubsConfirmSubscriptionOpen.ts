import { SubsAnalyticsEvent } from '../index';

/* Event type */
export type SubsAnalyticsEventSubsConfirmSubscriptionOpenType = {
  type: SubsAnalyticsEvent.SUBS_CONFIRM_SUBSCRIPTION_OPEN;
  payload: { first_time_subscribing: boolean };
};

/* Event generator */
export const SubsAnalyticsEventSubsConfirmSubscriptionOpen = (payload: {
  first_time_subscribing: boolean;
}): SubsAnalyticsEventSubsConfirmSubscriptionOpenType => ({
  type: SubsAnalyticsEvent.SUBS_CONFIRM_SUBSCRIPTION_OPEN,
  payload,
});
