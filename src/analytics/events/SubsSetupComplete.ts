import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventSetupCompleteType = {
  type: SubsAnalyticsEvent.SUBS_SETUP_COMPLETE;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSetupComplete = (
  payload: EventPayloadType,
): SubsAnalyticsEventSetupCompleteType => ({
  type: SubsAnalyticsEvent.SUBS_SETUP_COMPLETE,
  payload,
});
