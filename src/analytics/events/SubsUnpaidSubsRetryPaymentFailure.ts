import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  error_message: string;
};

/* Event type */
export type SubsAnalyticsEventSubsUnpaidSubsRetryPaymentFailureType = {
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_FAILURE;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsUnpaidSubsRetryPaymentFailure = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsUnpaidSubsRetryPaymentFailureType => ({
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_FAILURE,
  payload,
});
