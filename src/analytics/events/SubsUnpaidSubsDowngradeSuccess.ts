import { Tags } from '../../common/interfaces';
import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  tag_ids: Array<Tags>;
};

/* Event type */
export type SubsAnalyticsEventSubsUnpaidSubsDowngradeSuccessType = {
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_SUCCESS;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsUnpaidSubsDowngradeSuccess = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsUnpaidSubsDowngradeSuccessType => ({
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBS_DOWNGRADE_SUCCESS,
  payload,
});
