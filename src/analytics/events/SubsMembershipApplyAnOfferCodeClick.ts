import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventMembershipApplyAnOfferCodeClickType = {
  type: SubsAnalyticsEvent.SUBS_MEMBERSHIP_APPLY_AN_OFFER_CODE_CLICK;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventMembershipApplyAnOfferCodeClick = (
  payload: EventPayloadType,
): SubsAnalyticsEventMembershipApplyAnOfferCodeClickType => ({
  type: SubsAnalyticsEvent.SUBS_MEMBERSHIP_APPLY_AN_OFFER_CODE_CLICK,
  payload,
});
