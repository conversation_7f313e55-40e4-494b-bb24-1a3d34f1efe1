import { Tags } from '../../common/interfaces';
import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  tag_ids: Array<Tags>;
};

/* Event type */
export type SubsAnalyticsEventSubsUnpaidSubsRetryPaymentSuccessType = {
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_SUCCESS;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsUnpaidSubsRetryPaymentSuccess = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsUnpaidSubsRetryPaymentSuccessType => ({
  type: SubsAnalyticsEvent.SUBS_UNPAID_SUBS_RETRY_PAYMENT_SUCCESS,
  payload,
});
