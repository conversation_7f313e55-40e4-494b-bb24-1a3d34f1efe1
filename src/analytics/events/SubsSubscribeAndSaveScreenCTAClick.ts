import { Tags } from '../../common/interfaces';
import { SubsAnalyticsEvent } from '../index';

type EventPayloadType = {
  tag_ids: Array<Tags>;
  first_time_subscribing: boolean;
  payment_methods: boolean;
};
/* Event type */
export type SubsAnalyticsEventSubsSubscribeAndSaveScreenCTAClickType = {
  type: SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_CTA_CLICK;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventSubsSubscribeAndSaveScreenCTAClick = (
  payload: EventPayloadType,
): SubsAnalyticsEventSubsSubscribeAndSaveScreenCTAClickType => ({
  type: SubsAnalyticsEvent.SUBS_SUBSCRIBE_AND_SAVE_SCREEN_CTA_CLICK,
  payload,
});
