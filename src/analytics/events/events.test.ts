import { SubsAnalyticsEvent } from '..';
import { SubsAnalyticsEventAddOfferCodeApplyError } from './SubsAddOfferCodeApplyError';
import { SubsAnalyticsEventAddOfferCodeApplySuccess } from './SubsAddOfferCodeApplySuccess';
import { SubsAnalyticsEventAddOfferCodeScreenOpen } from './SubsAddOfferCodeScreenOpen';
import { SubsAnalyticsEventCancelSubscriptionClick } from './SubsCancelSubscriptionClick';
import { SubsAnalyticsEventCancelSubscriptionComplete } from './SubsCancelSubscriptionComplete';
import { SubsAnalyticsEventEnterAddressScreenOpen } from './SubsEnterAddressScreenOpen';
import { SubsAnalyticsEventIntroOfferScreenOpen } from './SubsIntroOfferScreenOpen';
import { SubsAnalyticsEventMembershipApplyAnOfferCodeClick } from './SubsMembershipApplyAnOfferCodeClick';
import { SubsAnalyticsEventPendingScreenOpen } from './SubsPendingScreenOpen';
import { SubsAnalyticsEventReactivateSubsScreenOpen } from './SubsReactivateSubsScreenOpen';
import { SubsAnalyticsEventRFIDPreferenceScreenOpen } from './SubsRFIDPreferenceScreenOpen';
import { SubsAnalyticsEventSetupComplete } from './SubsSetupComplete';
import { SubsAnalyticsEventSetupDirectDebitScreenOpen } from './SubsSetupDirectDebitScreenOpen';
import { SubsAnalyticsEventUpgradePaymentRequiredOpen } from './SubsUpgradePaymentRequiredOpen';

const mockEventPayloadType = {
  account_balance: 116,
  gocardless_mandate_status: 'Active',
  tag_ids: [{ tagId: '123', tagStatus: 'ACTIVE', tagNotes: 'physical-RFID' }],
};

describe('Analytic events', () => {
  test('SubsAnalyticsEventAddOfferCodeApplyError', () => {
    const x = SubsAnalyticsEventAddOfferCodeApplyError({
      errorMessage: 'error test.',
    });
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_ERROR,
      payload: { errorMessage: 'error test.' },
    });
  });
  test('SubsAnalyticsEventAddOfferCodeApplySuccess', () => {
    const x = SubsAnalyticsEventAddOfferCodeApplySuccess(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_APPLY_SUCCESS,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventAddOfferCodeScreenOpen', () => {
    const x = SubsAnalyticsEventAddOfferCodeScreenOpen(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_SCREEN_OPEN,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventCancelSubscriptionClick', () => {
    const x = SubsAnalyticsEventCancelSubscriptionClick(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_CLICK,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventCancelSubscriptionComplete', () => {
    const x =
      SubsAnalyticsEventCancelSubscriptionComplete(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_CANCEL_SUBSCRIPTION_COMPLETE,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventEnterAddressScreenOpen', () => {
    const x = SubsAnalyticsEventEnterAddressScreenOpen(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_ENTER_ADDRESS_SCREEN_OPEN,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventIntroOfferScreenOpen', () => {
    const x = SubsAnalyticsEventIntroOfferScreenOpen(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_INTRO_OFFER_SCREEN_OPEN,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventMembershipApplyAnOfferCodeClick', () => {
    const x =
      SubsAnalyticsEventMembershipApplyAnOfferCodeClick(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_MEMBERSHIP_APPLY_AN_OFFER_CODE_CLICK,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventPendingScreenOpen', () => {
    const x = SubsAnalyticsEventPendingScreenOpen(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_PENDING_SCREEN_OPEN,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventReactivateSubsScreenOpen', () => {
    const x = SubsAnalyticsEventReactivateSubsScreenOpen(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_REACTIVATE_SUBS_SCREEN_OPEN,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventRFIDPreferenceScreenOpen', () => {
    const x = SubsAnalyticsEventRFIDPreferenceScreenOpen(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_RFID_PREFERENCE_SCREEN_OPEN,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventSetupComplete', () => {
    const x = SubsAnalyticsEventSetupComplete(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_SETUP_COMPLETE,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventSetupDirectDebitScreenOpen', () => {
    const x =
      SubsAnalyticsEventSetupDirectDebitScreenOpen(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_SETUP_DIRECT_DEBIT_SCREEN_OPEN,
      payload: mockEventPayloadType,
    });
  });
  test('SubsAnalyticsEventUpgradePaymentRequiredOpen', () => {
    const x =
      SubsAnalyticsEventUpgradePaymentRequiredOpen(mockEventPayloadType);
    expect(x).toEqual({
      type: SubsAnalyticsEvent.SUBS_UPGRADE_PAYMENTREQUIRED_OPEN,
      payload: mockEventPayloadType,
    });
  });
});
