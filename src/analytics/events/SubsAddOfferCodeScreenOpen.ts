import { SubsAnalyticsEvent } from '../index';
import { EventPayloadType } from '../types';

/* Event type */
export type SubsAnalyticsEventAddOfferCodeScreenOpenType = {
  type: SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_SCREEN_OPEN;
  payload: EventPayloadType;
};

/* Event generator */
export const SubsAnalyticsEventAddOfferCodeScreenOpen = (
  payload: EventPayloadType,
): SubsAnalyticsEventAddOfferCodeScreenOpenType => ({
  type: SubsAnalyticsEvent.SUBS_ADD_OFFER_CODE_SCREEN_OPEN,
  payload,
});
