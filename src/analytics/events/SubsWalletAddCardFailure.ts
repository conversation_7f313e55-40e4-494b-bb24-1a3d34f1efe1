import { SubsAnalyticsEvent } from '../index';
import { EventErrorPayloadType } from '../types';

export type SubsAnalyticsEventSubsWalletAddCardFailureType = {
  type: SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_FAILURE;
  payload: EventErrorPayloadType;
};

export const SubsAnalyticsEventSubsWalletAddCardFailure = (
  payload: EventErrorPayloadType,
): SubsAnalyticsEventSubsWalletAddCardFailureType => ({
  type: SubsAnalyticsEvent.SUBS_WALLET_ADD_CARD_FAILURE,
  payload,
});
