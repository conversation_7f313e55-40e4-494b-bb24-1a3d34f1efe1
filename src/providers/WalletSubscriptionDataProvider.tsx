import { useQuery } from '@apollo/client';
import React, { createContext, useContext, useEffect, useState } from 'react';

import { GET_WALLET_SUBSCRIPTION } from '../client/queries';
import { UserTypeEnum } from '../common/enums';
import {
  BillingData,
  IGetWalletSubscriptionResponse,
  WalletSubscriptionDataContextValue,
} from '../common/interfaces';
import { processBillingData } from '../utils/helpers';
import { useUserInfo } from './UserInfoProvider';

interface WalletSubscriptionProp {
  children?: React.ReactNode;
}

const WalletSubscriptionDataContext =
  createContext<WalletSubscriptionDataContextValue>({
    walletSubscriptionData: null,
    billingData: null,
    errorData: null,
    refetchWalletSubscription: Function,
  });

export const WalletSubscriptionData = () =>
  useContext(WalletSubscriptionDataContext);

const WalletSubscriptionDataProvider = (props: WalletSubscriptionProp) => {
  const { children } = props;
  const [walletSubscriptionData, setWalletSubscriptionData] =
    useState<IGetWalletSubscriptionResponse | null>(null);
  const { userInfo } = useUserInfo();

  const [errorData, setErrorData] = useState<any>(null);
  const [billingData, setBillingData] = useState<BillingData>({
    subscription: null,
    discounts: 0,
    totalValue: 0,
    currency: '',
  });

  const { refetch: refetchWalletSubscription, error } = useQuery(
    GET_WALLET_SUBSCRIPTION,
    {
      fetchPolicy: 'network-only',
      notifyOnNetworkStatusChange: true,
      variables: {
        userId: userInfo?.userId,
      },
      skip: !userInfo || userInfo.type !== UserTypeEnum.SUBS_WALLET,
      onCompleted: async (completedData: IGetWalletSubscriptionResponse) => {
        setWalletSubscriptionData(completedData);
        const {
          subscription: processedSubscription,
          discounts: processedDiscounts,
          totalValue: processedTotalValue,
          currency: processedCurrency,
        } = processBillingData(completedData);
        setBillingData({
          subscription: processedSubscription || null,
          discounts: processedDiscounts,
          totalValue: processedTotalValue,
          currency: processedCurrency,
        });
      },
      onError: (e) => {
        setErrorData(error);
        console.error('Failed to query walletSubscription', JSON.stringify(e));
      },
    },
  );

  useEffect(() => {
    if (userInfo?.userId && userInfo?.type === UserTypeEnum.SUBS_WALLET) {
      refetchWalletSubscription();
    }
  }, [refetchWalletSubscription, userInfo?.type, userInfo?.userId]);

  return (
    <WalletSubscriptionDataContext.Provider
      value={{
        walletSubscriptionData,
        billingData,
        errorData,
        refetchWalletSubscription,
      }}
    >
      {children}
    </WalletSubscriptionDataContext.Provider>
  );
};

export default WalletSubscriptionDataProvider;
