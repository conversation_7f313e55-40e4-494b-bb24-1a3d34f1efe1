import { <PERSON><PERSON><PERSON><PERSON>, ApolloQueryR<PERSON>ult, useQuery } from '@apollo/client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useEffect, useState } from 'react';

import { GET_USER_INFO } from '../client/queries';
import {
  AsyncStorageKey,
  MandateStatus,
  MembershipStatus,
  UserTypeEnum,
} from '../common/enums';
import { IMandate, IMmembership, IUser, IUserInfo } from '../common/interfaces';
import { getLatestMembership } from '../utils/helpers';
import { useSettings } from './Settings';

export interface UserInfoPropsOutput {
  userInfo?: IUserInfo;
  isLoading?: boolean;
  error?: ApolloError;
  resubscribed?: boolean;
  refetchUserInfo?: (
    variables?: IUser,
  ) => Promise<ApolloQueryResult<IUserInfo>>;
}

interface UserInfoProp {
  children?: React.ReactNode;
}

export const UserInfoContext = createContext<UserInfoPropsOutput>({});
export const useUserInfo = () => useContext(UserInfoContext);
export const useUserInfoContext = (): UserInfoPropsOutput =>
  useContext(UserInfoContext);
export const getMandate = async (userDetails: IUserInfo) => {
  const status = userDetails?.gocardless?.mandateStatus;

  // User is currently pending or active, remove the temporary mandate
  if (status === MandateStatus.PENDING || status === MandateStatus.ACTIVE) {
    await AsyncStorage.removeItem(AsyncStorageKey.DIRECT_DEBIT_REQUESTED);
    return status;
  }

  const temporaryMandateData = JSON.parse(
    (await AsyncStorage.getItem(AsyncStorageKey.DIRECT_DEBIT_REQUESTED)) ||
      '{}',
  );
  // temporary mandate exists
  if (
    userDetails &&
    userDetails.userId === temporaryMandateData?.user_id &&
    temporaryMandateData?.temporaryMandate
  ) {
    return temporaryMandateData?.temporaryMandate;
  }
  // no temporary/ cancelled mandate, return actual mandate
  return status;
};

export const hadPreviousSubscription = (membership: IMmembership[]) => {
  let hadPreviousSub = false;
  membership.forEach((m: IMmembership) => {
    if (
      m.userType === UserTypeEnum.SUBS_WALLET &&
      (m.membershipStatus === MembershipStatus.CANCELLED ||
        !!m.membershipRequestCancelDate)
    ) {
      hadPreviousSub = true;
    }
  });
  return hadPreviousSub;
};

const getResubscriberStatus = (
  mandate: IMandate,
  membership: IMmembership[],
  userType?: string,
) => {
  switch (userType) {
    case UserTypeEnum.PAYG:
      return mandate.mandateId !== '' && mandate.mandateStatus !== '';
    case UserTypeEnum.PAYG_WALLET || UserTypeEnum.SUBS_WALLET:
      return hadPreviousSubscription(membership);
    default:
      return false;
  }
};
const UserInfoProvider = (props: UserInfoProp) => {
  const { children } = props;
  const { user, isInternetReachable } = useSettings();

  const [userInfo, setUserInfo] = useState<IUserInfo | undefined>(undefined);
  const [resubscribed, setResubscribed] = useState(false);
  const [wasOffline, setWasOffline] = useState(false);

  const {
    loading: isLoading,
    error,
    refetch: refetchUserInfo,
  } = useQuery(GET_USER_INFO, {
    fetchPolicy: 'network-only',
    notifyOnNetworkStatusChange: true,
    variables: {
      userId: user.userId,
    },
    onCompleted: async (userInfoData) => {
      const mandateStatus = await getMandate({
        ...userInfoData?.userInfo,
        userId: user.userId,
      });
      setUserInfo({
        ...userInfoData.userInfo,
        gocardless: {
          ...userInfoData?.userInfo.gocardless,
          mandateStatus,
        },
        userId: user.userId,
        latestMembership: getLatestMembership(userInfoData.userInfo.membership),
      });
      setResubscribed(
        getResubscriberStatus(
          {
            ...userInfoData?.userInfo.gocardless,
            mandateStatus,
          },
          userInfoData?.userInfo.membership,
          userInfoData?.userInfo.type,
        ),
      );
    },
    onError: (e) => {
      console.error('Failed to query userInfo', JSON.stringify(e));
    },
  });

  useEffect(() => {
    if (!isInternetReachable) {
      setWasOffline(true);
    } else if (wasOffline) {
      refetchUserInfo();
      setWasOffline(false);
    }
  }, [isInternetReachable, wasOffline, refetchUserInfo]);

  return (
    <UserInfoContext.Provider
      value={{
        ...props,
        userInfo,
        isLoading,
        error,
        resubscribed,
        refetchUserInfo,
      }}
    >
      {children}
    </UserInfoContext.Provider>
  );
};

export default UserInfoProvider;
