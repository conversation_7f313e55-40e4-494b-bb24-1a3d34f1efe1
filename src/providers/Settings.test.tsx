import { SFConsent } from '@bp/pulse-auth-sdk/dist/types';
import { SupportedLocale } from '@bp/pulse-shared-types/lib/enums/SupportedLocale';
import { SupportedLocales } from '@bp/pulse-shared-types/lib/types/SupportedLocales';
import { createNavigationContainerRef } from '@react-navigation/native';
import { render, screen } from '@testing-library/react-native';
import React from 'react';
import { Text } from 'react-native';

import { SubscriptionProvider } from '../common/interfaces';
import { TRANSLATIONS } from '../translations';
import { SettingsContextProvider, useSettings } from './Settings';

const TestComponent = () => {
  const { t } = useSettings();

  return <Text testID="settings">{t.testing.FOR_TESTING_PURPOSES}</Text>;
};

const getDefaultProps = (): SubscriptionProvider => ({
  apiURL: '',
  apiKey: '',
  locale: SupportedLocale.EN_GB,
  navigation: createNavigationContainerRef(),
  user: {
    userId: '',
    country: 'UK',
  },
  getToken: () =>
    new Promise<string>(() => {
      return '';
    }),
  cardComponent: '',
  onExitMFE: () => {},
  onRequestToStartCharge: () => {},
  onNavigateToRFID: () => {},
  onNavigateToProfile: () => {},
  onNavigateToADACUnlink: () => {},
  featureFlags: {
    enableInvoicesList: false,
    enableOfferDetails: false,
    enableRFID: false,
    introOfferCredit: 0,
    introOfferCreditDuration: 0,
    introPercentageDiscount: 20,
    subsChargingRatesPromo: 0.44,
    disableIntroOffer: false,
    savingRate: 55,
    subscriptionAmount: 7.49,
    pollingTimeout: 36000,
    subsDrivingDistancekm: '215',
    subsChargekWh: '47',
    block_subscription_upgrade_during_migration: false,
    extraTariffDiscountPercentage: 10,
    'subsChargeRateAC<22kW': 0.42,
    'subsChargeRateDC<50kW': 0.46,
    'subsChargeRateDC>50kW': 0.54,
    enableThreeDsInCreateSubscription: false,
  },
  onAnalyticsEvent: () => {},
  onPaymentRequired: () => {},
  emspTermsAndConditions: 'http://example.com',
  tariffPricing: 'http://example.com',
  isInternetReachable: true,
  consents: [],
  updateConsents: (_) => {
    return new Promise(() => {});
  },
  requiredConsents: {
    accepted: false,
    consentType: SFConsent.EV_TERMS_AND_CONDITIONS_DE,
  },
});

const renderComponent = (locale: SupportedLocales) => {
  return render(
    <SettingsContextProvider {...getDefaultProps()} locale={locale}>
      <TestComponent />
    </SettingsContextProvider>,
  );
};

describe('Settings Provider Test', () => {
  it('Can correctly render settings', () => {
    const { queryByTestId } = renderComponent(SupportedLocale.EN_GB);
    const result = queryByTestId('settings');
    expect(result).toBeTruthy();
  });

  it('Renders translations in en-GB when the locale prop value is `en-GB`', () => {
    renderComponent(SupportedLocale.EN_GB);
    const translationText = TRANSLATIONS.en_GB.testing.FOR_TESTING_PURPOSES;
    const result = screen.getByText(translationText);
    expect(result).toBeTruthy();
  });

  it('Renders translations in de-DE when the locale prop value is `de-DE`', () => {
    renderComponent(SupportedLocale.DE_DE);
    const translationText = TRANSLATIONS.de_DE.testing.FOR_TESTING_PURPOSES;
    const result = screen.getByText(translationText);
    expect(result).toBeTruthy();
  });

  it('Renders translations in nl-NL when the locale prop value is `nl-nl`', () => {
    renderComponent(SupportedLocale.NL_NL);
    const translationText = TRANSLATIONS.nl_NL.testing.FOR_TESTING_PURPOSES;
    const result = screen.getByText(translationText);
    expect(result).toBeTruthy();
  });

  it('Renders translations in fr-FR when the locale prop value is `fr-FR`', () => {
    renderComponent(SupportedLocale.FR_FR);
    const translationText = TRANSLATIONS.fr_FR.testing.FOR_TESTING_PURPOSES;
    const result = screen.getByText(translationText);
    expect(result).toBeTruthy();
  });

  it('Renders translations in es-ES when the locale prop value is `es-ES`', () => {
    renderComponent(SupportedLocale.ES_ES);
    const translationText = TRANSLATIONS.es_ES.testing.FOR_TESTING_PURPOSES;
    const result = screen.getByText(translationText);
    expect(result).toBeTruthy();
  });
});
