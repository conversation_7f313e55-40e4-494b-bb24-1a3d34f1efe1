import { render } from '@testing-library/react-native';
import React, { useEffect } from 'react';
import { Text } from 'react-native';

import { AddressContextProvider, useAddressContext } from './AddressProvider';

const TestComponent = () => {
  const { address1, address2, town, postalCode, country, setAddressCtx } =
    useAddressContext();
  useEffect(() => {
    setAddressCtx('address1', 'address2', 'town', 'postalCode', 'country');
  });

  return (
    <>
      {address1 && address2 && town && postalCode && country && (
        <Text testID="address">{`${address1} ${address2}, ${town} ${postalCode} ${country}`}</Text>
      )}
    </>
  );
};

describe('Address Provider Tests', () => {
  it('Can correctly set an Address', () => {
    const { queryByTestId } = render(
      <AddressContextProvider>
        <TestComponent />
      </AddressContextProvider>,
    );
    const result = queryByTestId('address');
    expect(result).toBeTruthy();
  });
});
