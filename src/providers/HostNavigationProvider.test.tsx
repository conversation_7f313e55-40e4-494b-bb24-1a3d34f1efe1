/*
    Needs improvements!
*/
import { render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { BackHandler, Text } from 'react-native';

import {
  mockNavigation,
  MockNavigationContainer,
  MockScreens,
} from '../utils/MockNavigationContainer';
import { HostNavigationProvider } from './HostNavigationProvider';

let mockedNavigation: any = jest.fn();

const mockHostNavigationObj = {
  ...mockNavigation,
  canGoBack: jest.fn(),
  getCurrentRoute: jest.fn(),
};

jest.mock('./Settings', () => ({
  useSettings: () => ({ navigationKey: mockedNavigation }),
}));

jest.mock('../common/enums', () => ({
  WalletScreenNames: { Screen1: 'Screen1' },
}));

jest.mock('react-native/Libraries/Utilities/BackHandler', () =>
  jest.requireActual(
    'react-native/Libraries/Utilities/__mocks__/BackHandler.js',
  ),
);

jest.spyOn(global.console, 'error').mockImplementation(jest.fn());

describe('HostNavigationProvider', () => {
  it('Should render children and prevent going back', () => {
    const { getByTestId } = render(
      <HostNavigationProvider navigation={mockHostNavigationObj}>
        <Text testID="child" />
      </HostNavigationProvider>,
    );

    expect(getByTestId('child')).toBeDefined();
  });

  it('should call navigation', async () => {
    const { getByTestId } = render(
      <MockNavigationContainer navigation={mockHostNavigationObj}>
        <HostNavigationProvider navigation={mockHostNavigationObj}>
          <MockScreens navigation={mockHostNavigationObj} />
        </HostNavigationProvider>
      </MockNavigationContainer>,
    );

    await waitFor(async () =>
      expect(getByTestId('navigateScreen')).toBeDefined(),
    );
  });

  it('should not call navigation', () => {
    mockedNavigation = null;
    render(
      <MockNavigationContainer navigation={mockHostNavigationObj}>
        <HostNavigationProvider navigation={mockHostNavigationObj}>
          <MockScreens navigation={mockHostNavigationObj} />
        </HostNavigationProvider>
      </MockNavigationContainer>,
    );
    // @ts-ignore
    BackHandler.mockPressBack();
  });
});
