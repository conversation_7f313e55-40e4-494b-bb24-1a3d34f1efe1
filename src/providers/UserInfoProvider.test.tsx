import { MockedProvider } from '@apollo/client/testing';
import {
  render,
  screen,
  waitFor,
  waitForElementToBeRemoved,
} from '@testing-library/react-native';
import { GraphQLError } from 'graphql/error/GraphQLError';
import React from 'react';
import { Text } from 'react-native';

import { GET_USER_INFO } from '../client/queries';
import { MembershipStatus, UserTypeEnum } from '../common/enums';
import { TRANSLATIONS } from '../translations';
import UserInfoProvider, {
  hadPreviousSubscription,
  useUserInfo,
} from './UserInfoProvider';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { userId: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const MOCKS = [
  {
    request: {
      query: GET_USER_INFO,
      variables: {
        userId: mockUseSettingsObj.user.userId,
      },
    },
    result: {
      data: {
        userInfo: {
          type: 'PAYG',
          status: '200',
          country: mockUseSettingsObj.country,
          gocardless: {
            mandateStatus: 'mock',
            mandateId: 'mock',
          },
          balance: '1.00',
          tagIds: {
            tagId: ['Tag1', 'Tag2'],
          },
          membership: [
            {
              userType: UserTypeEnum.SUBS_WALLET,
              membershipStatus: MembershipStatus.CANCELLED,
              membershipRequestCancelDate: null,
              membershipStartDate: '2024-10-16T00:00:00.000Z',
              membershipBillingCycleDate: '2024-11-16T00:00:00.000Z',
            },
          ],
        },
      },
    },
  },
];

const TestComponent = () => {
  const { userInfo, isLoading, error } = useUserInfo();
  return (
    <>
      {error && <div>{mockTranslations.testing.ERROR}</div>}
      {isLoading && (
        <Text testID="loading">{mockTranslations.testing.LOADING}</Text>
      )}
      {userInfo && <Text testID="userType">{userInfo?.type}</Text>}
      {userInfo && <Text testID="userId">{userInfo?.userId}</Text>}
    </>
  );
};

describe('User Info Provider Tests', () => {
  it('Can correctly fetch data', async () => {
    const { toJSON } = render(
      <MockedProvider mocks={MOCKS}>
        <UserInfoProvider>
          <TestComponent />
        </UserInfoProvider>
      </MockedProvider>,
    );
    await waitForElementToBeRemoved(() => screen.getByTestId('loading'));
    expect(toJSON()).toMatchSnapshot();
  });

  it('should show loading if the gql call to refetchUserInfo fails', async () => {
    const errorResult = {
      errors: [new GraphQLError('mock Backend Error')],
    };
    const _MOCKS = [
      {
        request: {
          query: GET_USER_INFO,
          variables: {
            userId: mockUseSettingsObj.user.userId,
          },
        },
        result: errorResult,
      },
    ];
    const renderComponent = () =>
      render(
        <MockedProvider mocks={_MOCKS}>
          <UserInfoProvider>
            <TestComponent />
          </UserInfoProvider>
        </MockedProvider>,
      );
    await waitFor(() => {
      expect(
        renderComponent().getByText(mockUseSettingsObj.t.testing.LOADING),
      ).toBeDefined();
    });
  });
  it('should return false if there is no cancelled subscription in the membership list', () => {
    const membership = [
      {
        userType: UserTypeEnum.SUBS_WALLET,
        membershipStatus: MembershipStatus.ACTIVE,
        membershipRequestCancelDate: null,
        membershipStartDate: '2024-10-14T00:00:00.000Z',
        membershipEndDate: '',
        membershipBillingCycleDate: '2024-11-14T00:00:00.000Z',
      },
      {
        userType: UserTypeEnum.PAYG_WALLET,
        membershipStatus: MembershipStatus.ACTIVE,
        membershipRequestCancelDate: null,
        membershipStartDate: '2024-10-15T00:00:00.000Z',
        membershipEndDate: '',
        membershipBillingCycleDate: '2024-11-15T00:00:00.000Z',
      },
    ];
    expect(hadPreviousSubscription(membership)).toBe(false);
  });
  it('should return true if user had a previous subscription', () => {
    const membership = [
      {
        userType: UserTypeEnum.SUBS_WALLET,
        membershipStatus: MembershipStatus.CANCELLED,
        membershipRequestCancelDate: null,
        membershipStartDate: '2024-10-16T00:00:00.000Z',
        membershipEndDate: '',
        membershipBillingCycleDate: '2024-11-16T00:00:00.000Z',
      },
    ];
    expect(hadPreviousSubscription(membership)).toBe(true);
  });
});
