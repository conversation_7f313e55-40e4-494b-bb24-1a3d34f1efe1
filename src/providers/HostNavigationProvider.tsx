import {
  CommonActions,
  NavigationContainerRef,
  Route,
  useRoute,
} from '@react-navigation/native';
import React, { createContext, useCallback, useContext } from 'react';

interface RouteParams {
  isModalVisible?: boolean;
}

interface ContextOutput {
  canGoBack: () => boolean;
  navigate: (screen: string, params?: object, resetStack?: boolean) => void;
  navigateWithKey: (key: Route<string, object | undefined> | undefined) => void;
  setParams: (params: object) => void;
  getParams: (
    paramName: keyof RouteParams,
  ) => RouteParams[keyof RouteParams] | undefined;
  navigation: NavigationContainerRef<{}>;
}

// @ts-expect-error
const HostNavContext = createContext<ContextOutput>({});

export const useHostNavigation = (): ContextOutput =>
  useContext(HostNavContext);

const useGetParams = <T extends keyof RouteParams>(
  paramName: T,
): RouteParams[T] | undefined => {
  const route = useRoute<Route<string, RouteParams>>();
  return route.params ? route.params[paramName] : undefined;
};

export const HostNavigationProvider = ({
  children,
  navigation,
}: {
  children: React.ReactNode;
  navigation: NavigationContainerRef<{}>;
}) => {
  // Custom navigate handler allowing stack to be reset in props
  const navigate = useCallback(
    (screenName: string, params?: object, resetStack = false) => {
      if (resetStack) {
        return navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: screenName, params }],
          }),
        );
      }
      return navigation.dispatch(
        CommonActions.navigate({
          name: screenName,
          params,
        }),
      );
    },
    [navigation],
  );

  const setParams = useCallback(
    (params: object) => {
      navigation.dispatch(CommonActions.setParams(params));
    },
    [navigation],
  );

  const navigateWithKey = (
    key: Route<string, object | undefined> | undefined,
  ) => (!key ? undefined : navigation.dispatch(CommonActions.navigate(key)));

  const canGoBack = useCallback(() => navigation.canGoBack(), [navigation]);

  const getParams = useGetParams;

  return (
    <HostNavContext.Provider
      value={{
        canGoBack,
        navigate,
        navigateWithKey,
        setParams,
        getParams,
        navigation,
      }}
    >
      {children}
    </HostNavContext.Provider>
  );
};
