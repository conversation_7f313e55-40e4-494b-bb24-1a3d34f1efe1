import { useLazyQuery } from '@apollo/client';
import React, { createContext, useContext, useEffect, useState } from 'react';

import { GET_SUBSCRIPTION_PREFERENCE } from '../client/queries';
import { UserTypeEnum } from '../common/enums';
import * as I from '../common/interfaces';
import { useUserInfo } from './UserInfoProvider';

interface PreferencesPropsOutput {
  subPreferences?: I.SubPreferances;
  isLoading: boolean;
}

interface UserPreferencesProp {
  children?: React.ReactNode;
}

// @ts-expect-error
export const SubsPreferencesContext = createContext<PreferencesPropsOutput>();
export const useSubsPreferences = () => useContext(SubsPreferencesContext);
export const useUserPreferencesContext = (): PreferencesPropsOutput =>
  useContext(SubsPreferencesContext);
const SubsPreferencesProvider = (props: UserPreferencesProp) => {
  const { children } = props;
  const [subPreferences, setSubPreferences] = useState<
    I.SubPreferances | undefined
  >(undefined);
  const { userInfo } = useUserInfo();

  const [getSubscriptionPreferences, { loading: isLoading }] = useLazyQuery(
    GET_SUBSCRIPTION_PREFERENCE,
    {
      fetchPolicy: 'network-only',
      onCompleted: (data) => {
        if (data?.getSubscriptionPreference?.status.toString() === '200') {
          setSubPreferences(
            data.getSubscriptionPreference.payload.data as I.SubPreferances,
          );
        }
      },
      onError: () => {},
    },
  );

  // Going to need a variable to pass if there is a change on user subscription during the same session
  useEffect(() => {
    const fetchSubPreferences = async () => {
      await getSubscriptionPreferences({
        variables: {
          userId: userInfo?.userId,
        },
      });
    };

    if (
      userInfo?.userId &&
      userInfo.type !== UserTypeEnum.PAYG_WALLET &&
      userInfo.type !== UserTypeEnum.SUBS_WALLET
    ) {
      fetchSubPreferences();
    }
  }, [userInfo, getSubscriptionPreferences]);

  return (
    <SubsPreferencesContext.Provider
      value={{
        ...props,
        subPreferences,
        isLoading,
      }}
    >
      {children}
    </SubsPreferencesContext.Provider>
  );
};

export default SubsPreferencesProvider;
