import React, { createContext, useContext, useState } from 'react';

const initialContext: any = {
  address1: '',
  address2: '',
  town: '',
  postalCode: '',
};
export const AddressContext = createContext(initialContext);
export const useAddressContext = () => useContext(AddressContext);
export const AddressContextProvider = (props: any) => {
  const { children } = props;
  const [rfidType, setRFIDType] = useState<string | null>('');
  const [address1, setAddress1] = useState<string | null>('');
  const [address2, setAddress2] = useState<string | null>('');
  const [town, setTown] = useState<string | null>('');
  const [postalCode, setPostalCode] = useState<string | null>('');
  const [country] = useState<string | null>('UK');

  const setAddressCtx = (
    address1Data: string | null,
    address2Data: string | null,
    townData: string | null,
    postalCodeData: string | null,
  ) => {
    setAddress1(address1Data);
    setAddress2(address2Data);
    setTown(townData);
    setPostalCode(postalCodeData);
  };

  return (
    <AddressContext.Provider
      value={{
        rfidType,
        setRFIDType,
        address1,
        setAddressCtx,
        address2,
        town,
        postalCode,
        country,
      }}
    >
      {children}
    </AddressContext.Provider>
  );
};
