import { Mocked<PERSON>rovider } from '@apollo/client/testing';
import { renderHook } from '@testing-library/react-hooks';
import React from 'react';

import { GET_SUBSCRIPTION_PREFERENCE } from '../client/queries';
import { TRANSLATIONS } from '../translations';
import SubsPreferencesProvider, {
  useSubsPreferences,
} from './SubsPreferencesProvider';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { userId: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const MOCKS = [
  {
    request: {
      query: GET_SUBSCRIPTION_PREFERENCE,
      variables: {
        userId: mockUseSettingsObj.user.userId,
      },
    },
    result: {
      data: {
        getSubscriptionPreference: {
          status: 200,
          payload: {
            data: {
              cardPreference: 'mock',
              addressLine: 'mock',
              addressCity: 'mock',
              addressCountry: 'mock',
              addressPostcode: 'mock',
              nextBillingDate: 'mock',
              toBeCancelled: false,
            },
          },
        },
      },
    },
  },
];

describe('Subscription Preferences Provider Tests', () => {
  it('Can correctly fetch data', () => {
    const wrapper = ({ children }: any) => (
      <MockedProvider mocks={MOCKS}>
        <SubsPreferencesProvider>{children}</SubsPreferencesProvider>
      </MockedProvider>
    );
    const { result } = renderHook(() => useSubsPreferences(), { wrapper });
    expect(result.current.isLoading).toBe(false);
  });
});
