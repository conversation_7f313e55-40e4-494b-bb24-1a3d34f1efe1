import { SupportedLocale } from '@bp/pulse-shared-types/lib/enums/SupportedLocale';
import React, { createContext, useContext, useEffect, useState } from 'react';

import * as I from '../common/interfaces';
import { TRANSLATIONS } from '../translations';
import { ITranslation } from '../translations/schema';
import { updateTranslations } from '../utils/updateTranslations';

const defaultFeatureFlags: I.featureFlags = {
  block_subscription_upgrade_during_migration: false,
  enableInvoicesList: false,
  enableOfferDetails: false,
  enableRFID: false,
  extraTariffDiscountPercentage: 10,
  introOfferCredit: 0,
  introOfferCreditDuration: 0,
  introPercentageDiscount: 25,
  subsChargingRatesPromo: 0.44,
  disableIntroOffer: false,
  savingRate: 55,
  'subsChargeRateAC<22kW': 0.42,
  'subsChargeRateDC<50kW': 0.46,
  'subsChargeRateDC>50kW': 0.54,
  subscriptionAmount: 7.49,
  pollingTimeout: 36000,
  subsDrivingDistancekm: '215',
  subsChargekWh: '47',
  enableThreeDsInCreateSubscription: false,
};

export interface ISettingsContextOutput extends I.SubscriptionProvider {
  t: ITranslation;
}

// @ts-expect-error
export const SettingsContext = createContext<ISettingsContextOutput>({});
export const useSettings = (): ISettingsContextOutput =>
  useContext(SettingsContext);
export const SettingsContextProvider = (props: I.SubscriptionProvider) => {
  const { children, featureFlags, locale, brand } = props;
  const [translationSet, setTranslationSet] = useState<ITranslation>(
    TRANSLATIONS.en_GB,
  );

  const replaceStrings = {
    brand: brand || 'bp',
  };

  // Set the translation set based on the locale property passed to the MFE
  useEffect(() => {
    let rawTranslations;
    switch (locale) {
      case SupportedLocale.EN_GB:
        rawTranslations = TRANSLATIONS.en_GB;
        break;
      case SupportedLocale.DE_DE:
        rawTranslations = TRANSLATIONS.de_DE;
        break;
      case SupportedLocale.NL_NL:
        rawTranslations = TRANSLATIONS.nl_NL;
        break;
      case SupportedLocale.ES_ES:
        rawTranslations = TRANSLATIONS.es_ES;
        break;
      case SupportedLocale.FR_FR:
        rawTranslations = TRANSLATIONS.fr_FR;
        break;
      default:
        rawTranslations = TRANSLATIONS.en_GB;
    }
    const updatedTranslations = updateTranslations(
      rawTranslations,
      replaceStrings,
    );

    setTranslationSet(updatedTranslations);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [locale]);

  return (
    <SettingsContext.Provider
      value={{
        ...props,
        t: translationSet,
        featureFlags: { ...defaultFeatureFlags, ...featureFlags },
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
};
