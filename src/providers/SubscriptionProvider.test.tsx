import { MockedProvider } from '@apollo/client/testing';
import {
  act,
  fireEvent,
  render,
  waitForElementToBeRemoved,
} from '@testing-library/react-native';
import React from 'react';
import { Button, Text } from 'react-native';

import { GET_SUBS_PLAN, VALIDATE_OFFER_CODE } from '../client/queries';
import { Validity } from '../common/enums';
import { TRANSLATIONS } from '../translations';
import {
  SubscriptionContextProvider,
  useSubscriptionContext,
} from './SubscriptionProvider';
const mockTranslations = TRANSLATIONS.en_GB;

const mockUserObj = {
  user: { userId: '123' },
  t: mockTranslations,
  onAnalyticsEvent: () => jest.fn(),
};
const mockUseSettings = jest.fn().mockReturnValue(mockUserObj);
jest.mock('./Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const MOCKS = [
  {
    request: {
      query: GET_SUBS_PLAN,
      variables: {
        userId: '123',
      },
    },
    result: {
      data: {
        getSubsPlan: {
          billingAmount: {
            amount: 10,
            currency: 'GBP',
          },
        },
      },
    },
  },
  {
    request: {
      query: VALIDATE_OFFER_CODE,
      variables: {
        offerCode: 'test123',
      },
    },
    result: {
      data: {
        validateOffer: {
          offer: {
            offerType: 'SUBS',
            creditAmount: 0,
            subsDiscount: 10,
          },
          isValid: true,
        },
      },
    },
  },
];

const TestComponent = () => {
  const { loading, subsPlanError, currency } = useSubscriptionContext();
  return (
    <>
      {subsPlanError && <div>{'ERROR'}</div>}
      {loading && <Text testID="loading">{'Loading'}</Text>}
      {currency && <Text testID="currency">{currency}</Text>}
    </>
  );
};

const OfferTestComponent = () => {
  const { creditAmount } = useSubscriptionContext();
  return (
    <>{creditAmount && <Text testID="creditAmount">{creditAmount}</Text>}</>
  );
};

jest.spyOn(console, 'error').mockImplementation(() => {});

const OfferTestComponentValidation = () => {
  const { setOfferCode, valid, checkHandler } = useSubscriptionContext();
  const onClick = () => {
    setOfferCode('test123');
  };
  return (
    <>
      {valid === Validity.valid && (
        <Text testID="valid">{'Valid offer code'}</Text>
      )}
      {valid === Validity.notSet && (
        <Text testID="notSet">{'Not set offer code'}</Text>
      )}
      {valid === Validity.invalid && (
        <Text testID="invalid">{'Invalid offer code'}</Text>
      )}
      {<Button testID="setOfferCode" onPress={onClick} title="Click" />}
      {
        <Button
          testID="validateOfferCode"
          onPress={checkHandler}
          title="Check"
        />
      }
    </>
  );
};

describe('Subscription Provider Tests', () => {
  it('Can correctly fetch data', async () => {
    const { getByTestId, toJSON } = render(
      <MockedProvider mocks={MOCKS}>
        <SubscriptionContextProvider>
          <TestComponent />
        </SubscriptionContextProvider>
      </MockedProvider>,
    );
    await waitForElementToBeRemoved(() => getByTestId('loading'));
    expect(toJSON()).toMatchSnapshot();
  });
  it('Can correctly work with offerCode', async () => {
    const { toJSON } = render(
      <MockedProvider mocks={MOCKS}>
        <SubscriptionContextProvider>
          <OfferTestComponent />
        </SubscriptionContextProvider>
      </MockedProvider>,
    );
    expect(toJSON()).toMatchSnapshot();
  });

  it('Can set state to invalid', async () => {
    const { getByTestId } = render(
      <MockedProvider mocks={MOCKS}>
        <SubscriptionContextProvider>
          <OfferTestComponentValidation />
        </SubscriptionContextProvider>
      </MockedProvider>,
    );

    const checkButton = getByTestId('validateOfferCode');
    expect(checkButton).toBeDefined();
    fireEvent.press(checkButton);

    expect(getByTestId('invalid')).toBeDefined();
  });

  it('Can correctly set invalid state on query failing', async () => {
    const { getByTestId } = render(
      <MockedProvider
        mocks={[
          {
            request: {
              query: VALIDATE_OFFER_CODE,
              variables: {
                offerCode: 'test123',
              },
            },
            result: {},
            error: { name: 'test', message: 'Error' },
          },
        ]}
      >
        <SubscriptionContextProvider>
          <OfferTestComponentValidation />
        </SubscriptionContextProvider>
      </MockedProvider>,
    );
    const button = getByTestId('setOfferCode');
    expect(button).toBeDefined();

    act(() => {
      fireEvent.press(button);
    });

    const checkButton = getByTestId('validateOfferCode');
    expect(checkButton).toBeDefined();

    act(() => {
      fireEvent.press(checkButton);
    });

    expect(console.error).toHaveBeenCalled();
  });
});
