import { MockedProvider } from '@apollo/client/testing';
import { render } from '@testing-library/react-native';
import React from 'react';
import { Text } from 'react-native';

import { GET_WALLET_SUBSCRIPTION } from '../client/queries';
import { rawData } from '../utils/mockData';
import WalletSubscriptionProvider, {
  WalletSubscriptionData,
} from './WalletSubscriptionDataProvider';

const MOCKS = [
  {
    request: {
      query: GET_WALLET_SUBSCRIPTION,
      variables: {
        userId: 'd8471df4-116c-488b-8dde-7410a3e08b81',
      },
    },
    result: {
      data: { rawData },
    },
  },
];

const TestComponent = () => {
  const { walletSubscriptionData } = WalletSubscriptionData();

  return (
    <>
      {walletSubscriptionData && (
        <Text testID="subscription">
          {JSON.stringify(walletSubscriptionData)}
        </Text>
      )}
    </>
  );
};

describe('Wallet Subscription Provider Tests', () => {
  it('Can correctly fetch wallet subscription data', async () => {
    const { toJSON } = render(
      <MockedProvider mocks={MOCKS}>
        <WalletSubscriptionProvider>
          <TestComponent />
        </WalletSubscriptionProvider>
      </MockedProvider>,
    );
    expect(toJSON()).toMatchSnapshot();
  }, 30000);
});
