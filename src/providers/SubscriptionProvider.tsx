import { useLazyQuery, useQuery } from '@apollo/client';
import React, { createContext, useCallback, useContext, useState } from 'react';

import {
  SubsAnalyticsEventSubsOfferCodeCheckClick,
  SubsAnalyticsEventSubsOfferCodeCheckError,
  SubsAnalyticsEventSubsOfferCodeCheckSuccess,
} from '../analytics/events';
import { GET_SUBS_PLAN, VALIDATE_OFFER_CODE } from '../client/queries';
import {
  Currency,
  OfferInvalidReason,
  OfferType,
  Validity,
} from '../common/enums';
import {
  ISubscriptionContextProvider,
  IValidateOfferRequest,
  IValidateOfferResponse,
} from '../common/interfaces';
import { useSettings } from './Settings';
import { useUserInfo } from './UserInfoProvider';

const initialContext: ISubscriptionContextProvider = {
  loading: false,
  subscription: 0,
  totalAmount: 0,
  subsPlanError: false,
  currency: Currency.GBP,
  subsDiscount: 0,
  setSubsDiscount: () => {},
  offerType: '',
  setOfferType: () => {},
  getSubsPlan: () => Promise.resolve(null),
  offerCode: '',
  offerName: '',
  offerDescription: '',
  offerDuration: 0,
  setOfferCode: () => {},
  valid: Validity.notSet,
  setValid: () => {},
  validatedOfferCode: '',
  errorMessage: '',
  setErrorMessage: () => {},
  creditAmount: 0,
  checkHandler: () => Promise.resolve(),
  onOfferCodeChange: undefined,
  offerCodeLoading: false,
  isSubscriptionPaused: false,
  setIsSubscriptionPaused: () => {},
  isPausedModalVisible: false,
  setIsPausedModalVisible: () => {},
};
export const SubscriptionContext = createContext(initialContext);
export const useSubscriptionContext = () => useContext(SubscriptionContext);
export const SubscriptionContextProvider = (props: any) => {
  const { children } = props;
  const { user, t, onAnalyticsEvent } = useSettings();
  const { resubscribed } = useUserInfo();
  const [subscription, setSubscription] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const [subsPlanError, setSubsPlanError] = useState(false);
  const [currency, setCurrency] = useState(Currency.GBP);
  const [subsDiscount, setSubsDiscount] = useState(0);
  const [offerType, setOfferType] = useState('');
  const [validatedOfferCode, setValidatedOfferCode] = useState('');
  const [creditAmount, setCreditAmount] = useState(0);
  const [offerCode, setOfferCode] = useState('');
  const [valid, setValid] = useState(Validity.notSet);
  const [errorMessage, setErrorMessage] = useState('');
  const [isPausedModalVisible, setIsPausedModalVisible] = useState(false);
  const [isSubscriptionPaused, setIsSubscriptionPaused] = useState(false);
  const [offerName, setOfferName] = useState('');
  const [offerDescription, setOfferDescription] = useState('');
  const [offerDuration, setOfferDuration] = useState(0);

  const { refetch: refetchSubsPlan, loading } = useQuery(GET_SUBS_PLAN, {
    fetchPolicy: 'network-only',
    variables: {
      userId: user.userId,
    },
    onCompleted: (info) => {
      if (
        info.getSubsPlan.plans[0].billingAmount &&
        info.getSubsPlan.plans[0].billingAmount.amount &&
        info.getSubsPlan.plans[0].billingAmount.currency
      ) {
        setSubscription(info.getSubsPlan.plans[0].billingAmount.amount);
        setTotalAmount(info.getSubsPlan.plans[0].billingAmount.amount);
        setCurrency(info.getSubsPlan.plans[0].billingAmount.currency);
      }
      setSubsPlanError(false);
    },
    onError: () => {
      setSubsPlanError(true);
    },
  });

  const getSubsPlan = useCallback(() => {
    setSubsPlanError(false);
    return refetchSubsPlan();
  }, [refetchSubsPlan]);

  const [validateOfferQuery, { data, loading: offerCodeLoading }] =
    useLazyQuery<IValidateOfferResponse, IValidateOfferRequest>(
      VALIDATE_OFFER_CODE,
      {
        fetchPolicy: 'network-only',
        variables: {
          offerCode,
        },
        onCompleted: () => {
          if (!data?.validateOffer || !data.validateOffer.offer?.offerCode) {
            setValid(Validity.invalid);
            return;
          }

          const { offer, isValid, reason } = data.validateOffer;

          if (!isValid) {
            onAnalyticsEvent(
              SubsAnalyticsEventSubsOfferCodeCheckError({
                entered_value: offerCode,
                error_message: reason ?? t.confirmSubscription.INVALID_OFFER,
              }),
            );

            if (reason === OfferInvalidReason.EXCEEDED_OFFER_LIMIT) {
              setErrorMessage(t.confirmSubscription.TO_MANY_OFFER_CODES);
            }
            setValid(Validity.invalid);
            return;
          }

          setValid(Validity.valid);

          onAnalyticsEvent(
            SubsAnalyticsEventSubsOfferCodeCheckSuccess({
              entered_value: offerCode,
              first_time_subscribing: !resubscribed,
            }),
          );

          const {
            offerCode: offerCodeValid,
            offerType: offerTypeValid,
            creditAmount: creditAmountValid = 0,
            subsDiscount: subsDiscountValid = 0,
            subsDuration: subsDurationValid = 0,
            offerName: offerNameValid = '',
            offerPublicDescription: offerDescriptionValid = '',
          } = offer;

          setValidatedOfferCode(offerCodeValid);
          setOfferName(offerNameValid);
          setOfferDescription(offerDescriptionValid);
          setOfferDuration(subsDurationValid);

          if (offerTypeValid === OfferType.CREDIT) {
            setCreditAmount(creditAmountValid);
            setOfferType(OfferType.CREDIT);
          } else {
            setSubsDiscount(
              subsDiscountValid === 0 ? subscription : subsDiscountValid,
            );
            setTotalAmount(
              Number(
                (subsDiscountValid === 0
                  ? 0
                  : subscription - subsDiscountValid
                ).toFixed(2),
              ),
            );
            if (offerTypeValid === OfferType.SUBS) {
              setOfferType(OfferType.SUBS);
            } else if (offerTypeValid === OfferType.COMBO) {
              setCreditAmount(creditAmountValid);
              setOfferType(OfferType.COMBO);
            }
          }
        },
        onError: () => {
          setValid(Validity.invalid);
          console.error('Failed validating the offer code');
          setErrorMessage(t.confirmSubscription.FAILED_VALIDITY_CHECK);

          onAnalyticsEvent(
            SubsAnalyticsEventSubsOfferCodeCheckError({
              entered_value: offerCode,
              error_message: t.confirmSubscription.FAILED_VALIDITY_CHECK,
            }),
          );
        },
      },
    );

  const validateOfferCode = async () => {
    await validateOfferQuery({
      variables: {
        offerCode: offerCode.toUpperCase(),
      },
    });
  };

  const checkHandler = async () => {
    if (!offerCode) {
      setValid(Validity.invalid);
      setErrorMessage(t.confirmSubscription.EMPTY_OFFER_FIELD);

      onAnalyticsEvent(
        SubsAnalyticsEventSubsOfferCodeCheckError({
          entered_value: offerCode,
          error_message: t.confirmSubscription.EMPTY_OFFER_FIELD,
        }),
      );
      return;
    }
    onAnalyticsEvent(
      SubsAnalyticsEventSubsOfferCodeCheckClick({
        entered_value: offerCode,
        first_time_subscribing: !resubscribed,
      }),
    );

    validateOfferCode();
  };

  //reset to default screen when editing the offer code
  const onOfferCodeChange = () => {
    setOfferType('');
    setSubsDiscount(0);
    setValidatedOfferCode('');
    setTotalAmount(subscription);
    setOfferName('');
    setOfferDescription('');
    setOfferDuration(0);
  };

  return (
    <SubscriptionContext.Provider
      value={{
        loading,
        subscription,
        totalAmount,
        subsPlanError,
        setSubsDiscount,
        currency,
        subsDiscount,
        offerType,
        setOfferType,
        getSubsPlan,
        offerCode,
        setOfferCode,
        valid,
        setValid,
        validatedOfferCode,
        errorMessage,
        setErrorMessage,
        creditAmount,
        checkHandler,
        onOfferCodeChange,
        offerCodeLoading,
        isSubscriptionPaused,
        setIsSubscriptionPaused,
        isPausedModalVisible,
        setIsPausedModalVisible,
        offerName,
        offerDescription,
        offerDuration,
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};
