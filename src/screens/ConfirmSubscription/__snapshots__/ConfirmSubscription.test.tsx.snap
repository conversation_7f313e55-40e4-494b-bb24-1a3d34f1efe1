// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ConfirmSubscription component should render correctly 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 0,
    }
  }
>
  <RCTScrollView
    contentContainerStyle={
      {
        "flexGrow": 1,
      }
    }
    keyboardShouldPersistTaps="always"
    style={
      {
        "display": "flex",
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "paddingTop": 30,
      }
    }
  >
    <View>
      <View
        style={{}}
      >
        <Text
          needsPadding={true}
          style={
            {
              "color": "#212121",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 28,
              "paddingBottom": 8,
              "paddingLeft": 24,
              "textAlign": "left",
            }
          }
        >
          My subscription
        </Text>
        <View
          style={
            {
              "borderColor": "#dedede",
              "borderLeftWidth": 0,
              "borderRightWidth": 0,
              "borderStyle": "solid",
              "borderWidth": 1,
              "paddingBottom": 16,
              "paddingLeft": 24,
              "paddingRight": 24,
              "paddingTop": 16,
            }
          }
        >
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "left",
                }
              }
            >
              To pay now (first month free):
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "left",
                }
              }
            >
              <Text
                align="right"
                strikethrough={true}
                style={
                  {
                    "color": "#212121",
                    "fontSize": 13,
                    "fontWeight": "400",
                    "lineHeight": 23,
                    "textAlign": "right",
                    "textDecorationLine": "line-through",
                    "textDecorationStyle": "solid",
                  }
                }
              >
                £7.85
              </Text>
               
              £0.00
            </Text>
          </View>
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "left",
                }
              }
            >
              To pay from 22 June 2024:
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "left",
                }
              }
            >
              £7.85
            </Text>
          </View>
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "left",
                }
              }
            >
              Contract term:
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "left",
                }
              }
            >
              1-month rolling
            </Text>
          </View>
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "left",
                }
              }
            >
              Billing date
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "left",
                }
              }
            >
              14th of each month
            </Text>
          </View>
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              flex={true}
              style={
                {
                  "color": "#212121",
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "flexShrink": 1,
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "left",
                }
              }
            >
              Cancellation:
            </Text>
            <Text
              align="right"
              flex={true}
              style={
                {
                  "color": "#212121",
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "flexShrink": 1,
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                  "textAlign": "right",
                }
              }
            >
              Cancel any time before your billing date
            </Text>
          </View>
          <View
            style={
              {
                "backgroundColor": "#dedede",
                "height": 1,
                "marginBottom": 16,
                "marginLeft": 0,
                "marginRight": 0,
                "marginTop": 8,
                "width": "100%",
              }
            }
          />
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 16,
                  "fontWeight": "400",
                  "lineHeight": 28,
                  "paddingBottom": 0,
                  "paddingLeft": 0,
                  "textAlign": "left",
                }
              }
            >
              To pay now
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 16,
                  "fontWeight": "400",
                  "lineHeight": 28,
                  "paddingBottom": 0,
                  "paddingLeft": 0,
                  "textAlign": "left",
                }
              }
            >
              <Text
                strikethrough={true}
                style={
                  {
                    "color": "#212121",
                    "fontSize": 16,
                    "fontWeight": "400",
                    "lineHeight": 28,
                    "paddingBottom": 0,
                    "paddingLeft": 0,
                    "textAlign": "left",
                    "textDecorationLine": "line-through",
                    "textDecorationStyle": "solid",
                  }
                }
              >
                £7.85
              </Text>
               
              £0.00
            </Text>
          </View>
        </View>
      </View>
      <View
        style={
          {
            "display": "flex",
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "justifyContent": "space-between",
            "paddingBottom": 16,
            "paddingLeft": 24,
            "paddingRight": 24,
            "paddingTop": 20,
          }
        }
      >
        <View
          style={
            {
              "marginBottom": 24,
            }
          }
        >
          <View
            style={
              {
                "alignItems": "flex-end",
                "display": "flex",
                "flexDirection": "row",
              }
            }
          >
            <View
              style={
                {
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "flexShrink": 1,
                  "minHeight": 57,
                }
              }
            >
              <View
                style={
                  {
                    "flexDirection": "row",
                    "flexWrap": "wrap",
                  }
                }
              >
                <Text
                  accessibilityLabel="OfferCode"
                  accessibilityRole="text"
                  bold={false}
                  style={
                    {
                      "flexShrink": 1,
                      "fontSize": 14,
                      "lineHeight": 24,
                      "marginBottom": 7,
                    }
                  }
                  testID="OfferCode"
                >
                  Offer code
                </Text>
                <View
                  style={
                    {
                      "flexBasis": 0,
                      "flexGrow": 0,
                      "flexShrink": 1,
                    }
                  }
                />
              </View>
              <View
                style={
                  {
                    "justifyContent": "center",
                    "minHeight": 48,
                  }
                }
              >
                <TextInput
                  accessibilityLabel="Enter OfferCode"
                  accessible={true}
                  active={false}
                  autoCapitalize="characters"
                  autoCorrect={false}
                  clearButtonEnabled={false}
                  error={false}
                  invalid={true}
                  maxLength={20}
                  onBlur={[Function]}
                  onChangeText={[Function]}
                  onFocus={[Function]}
                  placeholderTextColor="rgba(17, 17, 17, 0.65)"
                  shadow={false}
                  style={
                    {
                      "backgroundColor": "#FFFFFF",
                      "borderBottomLeftRadius": 0,
                      "borderBottomRightRadius": 0,
                      "borderColor": "#DEDEDE",
                      "borderStyle": "solid",
                      "borderTopLeftRadius": 0,
                      "borderTopRightRadius": 0,
                      "borderWidth": 1,
                      "color": "#000000",
                      "flexBasis": 0,
                      "flexGrow": 1,
                      "flexShrink": 1,
                      "fontSize": 16,
                      "maxHeight": 90,
                      "minHeight": 48,
                      "paddingBottom": 5,
                      "paddingLeft": 10,
                      "paddingRight": 10,
                      "paddingTop": 5,
                    }
                  }
                  success={false}
                  testID="Enter OfferCode"
                  value=""
                />
              </View>
            </View>
            <View
              accessibilityState={
                {
                  "busy": undefined,
                  "checked": undefined,
                  "disabled": false,
                  "expanded": undefined,
                  "selected": undefined,
                }
              }
              accessibilityValue={
                {
                  "max": undefined,
                  "min": undefined,
                  "now": undefined,
                  "text": undefined,
                }
              }
              accessible={true}
              collapsable={false}
              focusable={true}
              onBlur={[Function]}
              onClick={[Function]}
              onFocus={[Function]}
              onResponderGrant={[Function]}
              onResponderMove={[Function]}
              onResponderRelease={[Function]}
              onResponderTerminate={[Function]}
              onResponderTerminationRequest={[Function]}
              onStartShouldSetResponder={[Function]}
              style={
                {
                  "alignItems": "center",
                  "backgroundColor": "#343434",
                  "borderBottomLeftRadius": 5,
                  "borderBottomRightRadius": 5,
                  "borderTopLeftRadius": 5,
                  "borderTopRightRadius": 5,
                  "display": "flex",
                  "height": 48,
                  "justifyContent": "center",
                  "marginLeft": 8,
                  "width": 102,
                }
              }
            >
              <Text
                style={
                  {
                    "color": "#FFFFFF",
                    "fontSize": 15,
                    "lineHeight": 20,
                    "textAlign": "center",
                  }
                }
              >
                Check
              </Text>
            </View>
          </View>
        </View>
        <View
          style={
            {
              "display": "flex",
            }
          }
        >
          <Text
            needsPaddingBottom={true}
            style={
              {
                "color": "#212121",
                "fontSize": 16,
                "fontWeight": "400",
                "lineHeight": 28,
                "paddingBottom": 8,
                "paddingLeft": 0,
              }
            }
          >
            Linked payment card
          </Text>
          <Text
            style={
              {
                "color": "#212121",
                "fontSize": 12,
                "fontWeight": "400",
                "lineHeight": 21,
              }
            }
          >
            Payments will be collected from this card.
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "paddingBottom": 32,
            "paddingLeft": 24,
            "paddingRight": 24,
            "paddingTop": 24,
          }
        }
      >
        <View
          accessibilityHint="ConfirmSubscriptionContinue"
          accessibilityLabel="Confirm and authorise"
          accessibilityRole="button"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": false,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#000096",
              "borderBottomLeftRadius": 23,
              "borderBottomRightRadius": 23,
              "borderColor": "transparent",
              "borderStyle": "solid",
              "borderTopLeftRadius": 23,
              "borderTopRightRadius": 23,
              "borderWidth": 0,
              "justifyContent": "center",
              "minHeight": 46,
              "opacity": 1,
              "paddingHorizontal": 25.5,
              "paddingVertical": 12,
            }
          }
          testID="ConfirmSubscriptionCTA"
        >
          <View>
            <Text
              disabled={false}
              inverted={false}
              size="large"
              style={
                {
                  "color": "#ffffff",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 15,
                  "letterSpacing": 0.7,
                  "textAlign": "center",
                }
              }
              type="primary"
            >
              Confirm and authorise
            </Text>
          </View>
        </View>
      </View>
    </View>
  </RCTScrollView>
</RCTSafeAreaView>
`;
