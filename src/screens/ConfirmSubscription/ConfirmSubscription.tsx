import { useMutation, useQuery } from '@apollo/client';
import {
  IThreeDS,
  useWallet,
  WalletScreenNames,
} from '@bp/bppay-wallet-feature';
import {
  Button,
  ButtonAction,
  ButtonSize,
  NoSignalError,
} from '@bp/ui-components/mobile/core';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import {
  SubsAnalyticsEventSubsConfirmAndAuthoriseClick,
  SubsAnalyticsEventSubsConfirmSubscriptionOpen,
  SubsAnalyticsEventSubsWalletAddCardFailure,
  SubsAnalyticsSubsWalletAddCardSuccess,
} from '../../analytics/events';
import {
  APPLY_OFFER,
  CREATE_WALLET_SUBSCRIPTION,
  GET_WALLET_SUBSCRIPTION,
} from '../../client/queries';
import {
  OfferType,
  SubscriptionScreenNames,
  UserTypeEnum,
  Validity,
} from '../../common/enums';
import {
  IApplyOfferRequest,
  IApplyOfferResponse,
  ICreateWalletSubscriptionRequest,
  ICreateWalletSubscriptionResponse,
  IGetWalletSubscriptionResponse,
} from '../../common/interfaces';
import { ErrorLabel, Spinner, TextInput } from '../../components';
import { CardDescription } from '../../components/Atoms/CardDescription/CardDescription';
import { SubscriptionDetails } from '../../components/Organisms/SubscriptionDetails/SubscriptionDetails';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useSubscriptionContext } from '../../providers/SubscriptionProvider';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { getCountrySpecificText, POLLING_INTERVAL } from '../../utils/helpers';
import { requestWithRetry } from '../../utils/retry';
import * as S from './ConfirmSubscription.styles';

export const ConfirmSubscription = ({ route }: any) => {
  const {
    t,
    isInternetReachable,
    cardComponent,
    onAnalyticsEvent,
    featureFlags,
    user,
  } = useSettings();
  const { pollingTimeout, enableThreeDsInCreateSubscription } = featureFlags;
  const { navigate } = useHostNavigation();
  const { userInfo, resubscribed } = useUserInfo();

  const {
    selectedCard,
    triggerAddPaymentCardFlow,
    triggerCVVFlow,
    setSubsDefaultNotificationTrigger,
  } = useWallet();
  const intervalId = useRef<NodeJS.Timeout>();
  const timeoutId = useRef<NodeJS.Timeout>();
  const paramsOfferCode = route?.params?.offerCode;

  const {
    loading: subsPlanLoading,
    offerType,
    offerDescription,
    offerDuration,
    subsDiscount,
    currency,
    subscription,
    totalAmount,
    subsPlanError,
    getSubsPlan,
    offerCode,
    setOfferCode,
    valid,
    validatedOfferCode,
    setValid,
    errorMessage,
    setErrorMessage,
    creditAmount,
    checkHandler,
    onOfferCodeChange,
    offerCodeLoading,
  } = useSubscriptionContext();

  const [isPolling, setIsPolling] = useState(false);

  const hasActiveCard = selectedCard != null;
  const { refetch, loading: isGetWalletSubscriptionLoading } = useQuery(
    GET_WALLET_SUBSCRIPTION,
    {
      notifyOnNetworkStatusChange: true,
      fetchPolicy: 'network-only',
      variables: {
        userId: userInfo?.userId,
      },
      skip: !isPolling,
      onCompleted: (response: IGetWalletSubscriptionResponse) => {
        getWalletSubscription(response);
      },
      onError: (error) => {
        console.error('GetWalletSubscription failed', error.message);
        return navigate(SubscriptionScreenNames.SubscriptionFailed, {
          onBackPress: route?.params?.onBackPress,
        });
      },
    },
  );

  const [applyOfferMutation, { loading: applyOfferLoading }] = useMutation<
    IApplyOfferResponse,
    IApplyOfferRequest
  >(APPLY_OFFER, {
    onCompleted: async (data: IApplyOfferResponse) => {
      if (data?.applyOffer?.error) {
        return navigate(SubscriptionScreenNames.SubscriptionFailed, {
          onBackPress: route?.params?.onBackPress,
        });
      }
      return null;
    },
    onError: (error) => {
      console.error('ApplyOffer failed', error.message);
      return navigate(SubscriptionScreenNames.SubscriptionFailed, {
        onBackPress: route?.params?.onBackPress,
      });
    },
  });

  const getWalletSubscription = useCallback(
    async (response: IGetWalletSubscriptionResponse) => {
      const subscriptionData = response.getWalletSubscription;
      if (!['ACTIVE', 'INACTIVE'].includes(subscriptionData.status)) {
        return null;
      }
      setIsPolling(false);
      clearInterval(intervalId.current);
      clearTimeout(timeoutId.current);

      if (validatedOfferCode && subscriptionData.status === 'ACTIVE') {
        await requestWithRetry(applyOfferMutation, {
          offerCode: validatedOfferCode,
          userId: userInfo?.userId ?? '',
        });
      }

      return navigate(
        subscriptionData.status === 'ACTIVE'
          ? SubscriptionScreenNames.SubscriptionSuccess
          : SubscriptionScreenNames.SubscriptionFailed,
        {
          onBackPress:
            subscriptionData.status === 'ACTIVE'
              ? null
              : route?.params?.onBackPress,

          subscriptionData:
            subscriptionData.status === 'ACTIVE' ? subscriptionData : null,
        },
      );
    },
    [
      validatedOfferCode,
      navigate,
      route?.params?.onBackPress,
      applyOfferMutation,
      userInfo?.userId,
    ],
  );

  const [createWalletSubscriptionMutation] = useMutation<
    ICreateWalletSubscriptionResponse,
    ICreateWalletSubscriptionRequest
  >(CREATE_WALLET_SUBSCRIPTION, {
    onCompleted: (data) => {
      setSubsDefaultNotificationTrigger(false);
      if (data?.createWalletSubscription.status === '200') {
        startPolling();
        navigate(SubscriptionScreenNames.Loading);
      } else {
        navigate(SubscriptionScreenNames.SubscriptionFailed, {
          onBackPress: route?.params?.onBackPress,
        });
      }
    },
    onError: (error) => {
      console.error('CreateWalletSubscription failed', error.message);
      navigate(SubscriptionScreenNames.SubscriptionFailed, {
        onBackPress: route?.params?.onBackPress,
      });
    },
  });

  const startPolling = () => {
    setIsPolling(true);
    intervalId.current = setInterval(() => {
      refetch();
    }, POLLING_INTERVAL);
    timeoutId.current = setTimeout(() => {
      setIsPolling(false);
      clearInterval(intervalId.current);
      navigate(SubscriptionScreenNames.SubscriptionFailed, {
        onBackPress: route?.params?.onBackPress,
      });
    }, pollingTimeout);
  };

  const handleOfferCodeChange = useCallback(
    (value: string) => {
      setOfferCode(value);
      onOfferCodeChange?.(value);
    },
    [onOfferCodeChange, setOfferCode],
  );

  const onPressHandler = async () => {
    if (userInfo?.type === UserTypeEnum.PAYG_WALLET) {
      onAnalyticsEvent(
        SubsAnalyticsEventSubsConfirmAndAuthoriseClick({
          tag_ids: userInfo?.tagIds || [],
          offer_code: offerCode,
          first_time_subscribing: !resubscribed,
        }),
      );
    }
    triggerCVVFlow({
      customNavigationKey: {
        name: SubscriptionScreenNames.ConfirmSubscription,
        key: SubscriptionScreenNames.ConfirmSubscription,
      },

      onSuccess: async (threeDS?: IThreeDS) => {
        try {
          await createWalletSubscriptionMutation({
            variables: {
              userId: userInfo?.userId ?? '',
              paymentMethodId: selectedCard?.paymentMethodId ?? '',
              ...(offerCode && { offerCode }),
              country: userInfo?.country,
              ...(enableThreeDsInCreateSubscription && threeDS
                ? { threeDS }
                : {}),
            },
          });
        } catch (error) {
          console.error('CreateWalletSubscription failed', error);
          navigate(SubscriptionScreenNames.SubscriptionFailed, {
            onBackPress: route?.params?.onBackPress,
          });
        }
      },
      onFailure: () => {
        navigate(SubscriptionScreenNames.SubscriptionFailed, {
          onBackPress: route?.params?.onBackPress,
        });
      },
    });
  };

  const addPaymentMethod = () => {
    triggerAddPaymentCardFlow({
      onAddCardSuccessfulAnalytics: () => {
        if (!hasActiveCard) {
          onAnalyticsEvent(
            SubsAnalyticsSubsWalletAddCardSuccess({
              first_time_subscribing: !resubscribed,
            }),
          );
        }
      },
      onAddCardUnsuccessfulAnalytics: (error?: any) => {
        if (!hasActiveCard) {
          onAnalyticsEvent(
            SubsAnalyticsEventSubsWalletAddCardFailure({
              errorMessage: 'error',
            }),
          );
          navigate(WalletScreenNames.CardFailed);
        }
        console.error('Add card failed:', error);
      },
      isSubsDefault: true,
      customNavigationKey: {
        name: SubscriptionScreenNames.ConfirmSubscription,
        key: SubscriptionScreenNames.ConfirmSubscription,
      },
    });
  };

  useEffect(() => {
    if (offerCode.match(/[^a-zA-Z0-9]+/)) {
      setErrorMessage(t.confirmSubscription.SPECIAL_CHARACTER_WARNING);
      setValid(Validity.invalid);
      return;
    }
    setErrorMessage('');
    setValid(Validity.notSet);
  }, [
    offerCode,
    setErrorMessage,
    setValid,
    t.confirmSubscription.SPECIAL_CHARACTER_WARNING,
  ]);

  useEffect(() => {
    if (typeof paramsOfferCode === 'string' && paramsOfferCode.length) {
      setOfferCode(paramsOfferCode);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (offerCode === paramsOfferCode) {
      checkHandler();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [offerCode]);

  useEffect(() => {
    if (userInfo?.type === UserTypeEnum.PAYG_WALLET) {
      onAnalyticsEvent(
        SubsAnalyticsEventSubsConfirmSubscriptionOpen({
          first_time_subscribing: !resubscribed,
        }),
      );
    }
  }, [onAnalyticsEvent, resubscribed, userInfo?.type]);

  const isSubsOffer =
    offerType === OfferType.SUBS || offerType === OfferType.COMBO;

  const isCreditOffer =
    offerType === OfferType.CREDIT || offerType === OfferType.COMBO;

  const isLoading =
    isPolling ||
    subsPlanLoading ||
    applyOfferLoading ||
    isGetWalletSubscriptionLoading;

  const isDisabled =
    isLoading || !isInternetReachable || selectedCard?.isExpired;

  return (
    <>
      {subsPlanError ? (
        <NoSignalError
          loadingFailedText={getCountrySpecificText(
            t,
            'networkConnectionError.text.FAILED_HEADING_TEXT',
            user?.country,
          )}
          subheadingText={t.networkConnectionError.text.SUB_HEADING_TEXT}
          retryBtnText={t.networkConnectionError.text.RETRY}
          retryBtnOnPress={() => getSubsPlan()}
        />
      ) : (
        <S.ScreenWrapper>
          <S.Container keyboardShouldPersistTaps="always">
            <SubscriptionDetails
              hasActiveDiscount={isSubsOffer}
              hasActiveCreditOffer={isCreditOffer}
              subsDiscount={subsDiscount}
              creditAmount={creditAmount}
              currency={currency}
              subscription={subscription}
              totalAmount={totalAmount}
              isFirstTimeSubscriber={!resubscribed}
              hasValidOffer={valid === Validity.valid && !!validatedOfferCode}
              offerDuration={offerDuration}
            />
            <S.MiddleWrapper>
              <S.OfferWrapper>
                <S.OfferCodeWrapper>
                  <TextInput
                    label="Offer code"
                    onChangeText={handleOfferCodeChange}
                    value={offerCode}
                    autoCapitalize="characters"
                    autoCorrect={false}
                    accessibilityLabel="OfferCode"
                    error={valid === Validity.invalid}
                    validCode={valid === Validity.valid}
                    maxLength={20}
                  />
                  <S.AddButton
                    onPress={checkHandler}
                    disabled={
                      errorMessage ===
                      t.confirmSubscription.SPECIAL_CHARACTER_WARNING
                    }
                  >
                    {offerCodeLoading ? (
                      <Spinner active size="small" color="white" />
                    ) : (
                      <S.AddButtonText>
                        {t.confirmSubscription.CHECK_BUTTON}
                      </S.AddButtonText>
                    )}
                  </S.AddButton>
                </S.OfferCodeWrapper>
                {valid === Validity.invalid &&
                  (errorMessage ? (
                    <ErrorLabel status="error">{errorMessage}</ErrorLabel>
                  ) : (
                    <ErrorLabel status="error">
                      {t.confirmSubscription.INVALID_OFFER}
                    </ErrorLabel>
                  ))}
                {valid === Validity.valid &&
                  validatedOfferCode &&
                  offerDescription && (
                    <S.OfferAppliedText>
                      {offerDescription} {`(${t.confirmSubscription.APPLIED})`}
                    </S.OfferAppliedText>
                  )}
              </S.OfferWrapper>

              <CardDescription
                title={t.confirmSubscription.DESCRIPTION_TITLE}
                description={t.confirmSubscription.DESCRIPTION}
              />
            </S.MiddleWrapper>
            {cardComponent && (
              <S.PaymentContainer
                accessibilityLabel={`${selectedCard?.cardType} ${t.confirmSubscription.CARD_ENDING_TEXT} ${selectedCard?.lastFour}`}
              >
                {cardComponent}
              </S.PaymentContainer>
            )}
            <S.ButtonWrapper>
              {selectedCard ? (
                <Button
                  type={ButtonAction.PRIMARY}
                  onPress={onPressHandler}
                  loading={isLoading}
                  disabled={isDisabled}
                  accessibilityLabel={
                    t.gocardless.buttons.CONFIRM_AND_AUTHORISE
                  }
                  testID="ConfirmSubscriptionCTA"
                  accessibilityHint="ConfirmSubscriptionContinue"
                  size={ButtonSize.DEFAULT}
                >
                  {t.gocardless.buttons.CONFIRM_AND_AUTHORISE}
                </Button>
              ) : (
                <Button
                  type={ButtonAction.PRIMARY}
                  onPress={addPaymentMethod}
                  loading={isLoading}
                  disabled={isDisabled}
                  accessibilityLabel="AddPaymentMethod Button"
                  testID="ConfirmSubscriptionCTA"
                  accessibilityHint="AddPaymentMethod"
                  size={ButtonSize.DEFAULT}
                >
                  {t.gocardless.buttons.ADD_PAYMENT_METHOD}
                </Button>
              )}
            </S.ButtonWrapper>
          </S.Container>
        </S.ScreenWrapper>
      )}
    </>
  );
};
