import { Pressable } from 'react-native';
import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
`;

export const Text = styled.Text`
  text-align: left;
  color: ${(props: any) =>
    props.theme.subscriptionMfe.bottomNavigation.disabled.text};
`;

export const Container = styled.ScrollView.attrs(() => ({
  contentContainerStyle: {
    flexGrow: 1,
  },
}))`
  flex: 1;
  padding-top: 30px;
  display: flex;
`;

export const OfferCodeWrapper = styled.View`
  display: flex;
  flex-direction: row;
  align-items: flex-end;
`;

export const OfferWrapper = styled.View`
  margin-bottom: 24px;
`;

export const OfferAppliedText = styled.Text`
  font-size: 12px;
  line-height: 21px;
  font-weight: 400;
  color: ${({ theme }) => theme.subscriptionMfe.gocardless.text.banner};
`;

export const AddButton = styled(Pressable)`
  width: 102px;
  height: 48px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #343434;
  margin-left: 8px;

  ${({ disabled, theme }: any) =>
    disabled &&
    `
      background-color: ${theme.subscriptionMfe.color.grey.two};
    `}
`;

export const AddButtonText = styled.Text`
  color: ${(props: any) => props.theme.subscriptionMfe.color.white};
  font-size: 15px;
  line-height: 20px;
  text-align: center;
`;

export const MiddleWrapper = styled.View`
  padding: 20px 24px 16px 24px;
  display: flex;
  flex: 1;
  justify-content: space-between;
`;

export const DummyWallet = styled.View`
  padding: 24px;
`;

export const ButtonWrapper = styled.View`
  padding: 24px 24px 32px 24px;
`;

export const PaymentContainer = styled.View`
  height: 80px;
  background-color: ${({ theme }) => theme.bpCore.colors.appBackground.primary};
`;
