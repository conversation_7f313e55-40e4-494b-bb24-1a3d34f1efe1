import { ThemeProvider } from '@bp/ui-components/mobile';
import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { Linking } from 'react-native';

import { TRANSLATIONS } from '../../translations';
import { updateTranslations } from '../../utils/updateTranslations';

const mockTranslations = updateTranslations(TRANSLATIONS.en_GB, {
  brand: 'bp',
});
const mockUseSettingsObj = {
  t: mockTranslations,
  onAnalyticsEvent: () => jest.fn(),
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));
jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => ({ userInfo: { country: 'UK' } }),
}));
jest.mock('../../utils/helpers', () => ({
  getPhoneNumber: jest.fn().mockReturnValue('test-number'),
}));

const openURLSpy = jest.spyOn(Linking, 'openURL');

import SubscriptionFailed from './SubscriptionFailed';

const renderWithTheme = () =>
  render(
    <ThemeProvider>
      <SubscriptionFailed />
    </ThemeProvider>,
  );

describe('Subscription Failed Screen', () => {
  it('renders without crashing', () => {
    const SubscriptionFailedScreen = renderWithTheme();
    expect(SubscriptionFailedScreen.toJSON()).toMatchSnapshot();
  });

  it('renders button with accessibility label', async () => {
    const { getByLabelText } = renderWithTheme();
    await waitFor(() => {
      const button = getByLabelText(
        mockTranslations.gocardless.buttons.GOBACKANDTRYAGAIN,
      );
      expect(button).toBeDefined();
    });
  });

  it('should render text on the page', () => {
    const { getByText } = renderWithTheme();
    expect(
      getByText(mockTranslations.gocardless.text.SOMETHING_WENT_WRONG),
    ).toBeTruthy();
    expect(
      getByText(mockTranslations.gocardless.text.SUBSCRIPTION_HAS_NOT_STARTED),
    ).toBeTruthy();
    expect(
      getByText(
        `${mockTranslations.gocardless.text.TROUBLESHOOTING_TEXT} test-number`,
      ),
    ).toBeTruthy();
  });

  it('should call the customer care number', () => {
    const { getByText } = renderWithTheme();
    const phoneNumberButton = getByText('test-number');
    fireEvent.press(phoneNumberButton);

    expect(openURLSpy).toHaveBeenCalledWith('tel:test-number');
  });
});
