import {
  But<PERSON>,
  <PERSON>tonAction,
  ButtonSize,
  TouchableOpacity,
} from '@bp/ui-components/mobile/core';
import React, { useEffect } from 'react';
import { Linking } from 'react-native';

import SubscriptionFailedImage from '../../../assets/images/subscription-failed.png';
import {
  SubsAnalyticsEventSubsSetupFailureOpen,
  SubsAnalyticsEventSubsSetupFailureRetryClick,
} from '../../analytics/events';
import {
  Countries,
  SubscriptionScreenNames,
  UserTypeEnum,
} from '../../common/enums';
import { CloseIcon } from '../../components';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useSubscriptionContext } from '../../providers/SubscriptionProvider';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { getPhoneNumber } from '../../utils/helpers';
import * as S from './SubscriptionFailed.styles';

const SubscriptionFailed = ({ route }: any) => {
  const { t, onAnalyticsEvent } = useSettings();
  const { userInfo, resubscribed } = useUserInfo();
  const { offerCode } = useSubscriptionContext();
  const { navigate } = useHostNavigation();
  const { params } = route || {};
  const phoneNumber = userInfo?.country && getPhoneNumber(userInfo);
  const isPaygWalletUser = userInfo?.type === UserTypeEnum.PAYG_WALLET;
  const onCloseIcon = () =>
    isPaygWalletUser
      ? navigate(SubscriptionScreenNames.SubscriptionLanding, {
          from: SubscriptionScreenNames.SubscriptionFailed,
        })
      : null;

  const onPressGoBack = () => {
    if (isPaygWalletUser) {
      if (userInfo?.country === Countries.DE.toString()) {
        navigate(SubscriptionScreenNames.MyComfortMembershipActivation, {
          from: SubscriptionScreenNames.SubscriptionFailed,
          onBackPress: params?.onBackPress,
        });
      } else {
        navigate(SubscriptionScreenNames.ConfirmSubscription, {
          from: SubscriptionScreenNames.SubscriptionFailed,
          onBackPress: params?.onBackPress,
        });
      }

      onAnalyticsEvent(
        SubsAnalyticsEventSubsSetupFailureRetryClick({
          first_time_subscribing: !resubscribed,
          entered_value: offerCode,
        }),
      );
    }
  };

  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventSubsSetupFailureOpen({
        first_time_subscribing: !resubscribed,
        entered_value: offerCode,
        error_message: t.gocardless.text.SUBSCRIPTION_HAS_NOT_STARTED,
      }),
    );
  }, [
    offerCode,
    onAnalyticsEvent,
    resubscribed,
    t.gocardless.text.SUBSCRIPTION_HAS_NOT_STARTED,
  ]);
  return (
    <S.ScreenWrapper>
      <S.CloseButtonContainer>
        <S.CloseButton>
          <TouchableOpacity onPress={onCloseIcon}>
            <CloseIcon color="#000" />
          </TouchableOpacity>
        </S.CloseButton>
      </S.CloseButtonContainer>

      <S.IllustrationView>
        <S.HeaderText testID="Header.Text">
          {t.gocardless.text.SOMETHING_WENT_WRONG}
        </S.HeaderText>
      </S.IllustrationView>
      <S.StyledIllustrationView>
        <S.StyledIllustration source={SubscriptionFailedImage} />
      </S.StyledIllustrationView>

      <S.SubscriptionHasNotStartedContainer>
        <S.SubNotStartedText testID="SubscriptionHasNotStarted.Label">
          {t.gocardless.text.SUBSCRIPTION_HAS_NOT_STARTED}
        </S.SubNotStartedText>
      </S.SubscriptionHasNotStartedContainer>

      <S.TroubleShootingContainer>
        <S.TroubleShootingText testID="Troubleshooting.View">
          {`${t.gocardless.text.TROUBLESHOOTING_TEXT} `}
          {phoneNumber && (
            <S.PhoneNumberText
              accessibilityLabel={
                t.gocardless.accessibility.CUSTOMER_CARE_NUMBER.LABEL
              }
              accessibilityHint={
                t.gocardless.accessibility.CUSTOMER_CARE_NUMBER.HINT
              }
              onPress={() => Linking.openURL(`tel:${phoneNumber}`)}
            >
              {phoneNumber}
            </S.PhoneNumberText>
          )}
        </S.TroubleShootingText>
      </S.TroubleShootingContainer>

      <S.ButtonContainer>
        <Button
          type={ButtonAction.PRIMARY}
          disabled={false}
          onPress={onPressGoBack}
          size={ButtonSize.DEFAULT}
          testID="GoBackAndTryAgain.Button"
          accessibilityLabel={
            t.gocardless.accessibility.GOBACKANDTRYAGAIN.LABEL
          }
          accessibilityHint={t.gocardless.accessibility.GOBACKANDTRYAGAIN.HINT}
        >
          {t.gocardless.buttons.GOBACKANDTRYAGAIN}
        </Button>
      </S.ButtonContainer>
    </S.ScreenWrapper>
  );
};

export default SubscriptionFailed;
