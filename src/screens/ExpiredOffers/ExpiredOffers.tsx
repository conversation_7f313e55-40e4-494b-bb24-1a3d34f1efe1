import moment from 'moment';
import React from 'react';
import { FlatList } from 'react-native';
import { useTheme } from 'styled-components/native';

import { Spacer, TableActivityRow } from '../../components';
import { useSettings } from '../../providers/Settings';
import * as S from './ExpiredOffers.styles';

interface VoucherData {
  offerCode: string;
  partner: string;
  offer: string;
  clientPays: string;
  creditAmount: string;
  activeUntil: string;
}

interface Row {
  item: VoucherData;
  index: number;
}

export default ({ route }: any) => {
  const { params } = route;
  const { t } = useSettings();
  const theme = useTheme();

  const renderItem = (row: Row) => {
    const { item } = row;
    if (item.activeUntil === '0') {
      return null;
    }
    return (
      <TableActivityRow
        leftText={item.partner}
        rightText={`Expired ${moment(item.activeUntil).format('DD/MM/YY')}`}
        visibleButton={false}
        topBorderWidth={row.index === 0 ? '1px' : '0px'}
        rightTextColor={theme.subscriptionMfe.voucher.color.expired}
        type="voucher"
      />
    );
  };

  return (
    <S.AppliedVoucherContainer bounces={false}>
      <S.TopViewContainer>
        <Spacer vSpace={24} />
        <S.BannerText>{t.gocardless.text.EXPVOUCHERS_SUBS_TITLE}</S.BannerText>
        <Spacer vSpace={18} />
        <S.SubTitle>{t.gocardless.text.EXPVOUCHERS_SUBS_TEXT}</S.SubTitle>
        <Spacer vSpace={18} />
      </S.TopViewContainer>
      {params && params.voucher && params.voucher.length > 0 && (
        <S.MiddleViewContainer>
          <FlatList
            data={params.voucher}
            renderItem={renderItem}
            scrollEnabled={false}
          />
        </S.MiddleViewContainer>
      )}
      <Spacer vSpace={32} />
    </S.AppliedVoucherContainer>
  );
};
