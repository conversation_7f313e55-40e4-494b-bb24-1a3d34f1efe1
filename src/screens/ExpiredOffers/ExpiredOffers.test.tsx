import { MockedProvider } from '@apollo/client/testing';
import { render } from '@testing-library/react-native';
import React from 'react';

import { CANCEL_SUBSCRIPTION, GET_GOCARDLESS_URL } from '../../client/queries';
import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import ExpiredOffers from './ExpiredOffers';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { userId: '123', authenticated: false },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const MOCKS = [
  {
    request: {
      query: GET_GOCARDLESS_URL,
      variables: {
        userId: mockUseSettingsObj.user.userId,
      },
    },
    result: {
      data: {
        userInfo: {
          type: 'PAYG',
        },
      },
    },
  },
  {
    request: {
      query: CANCEL_SUBSCRIPTION,
      variables: {
        userId: '',
      },
    },
    result: {
      data: {
        cancelSubscription: {
          status: '',
        },
      },
    },
  },
];

describe('Expired Offers component', () => {
  it('should render correctly', () => {
    const params = {};
    const { toJSON } = render(
      <Theme>
        <MockedProvider mocks={MOCKS}>
          <ExpiredOffers route={params} />
        </MockedProvider>
      </Theme>,
    );
    expect(toJSON()).toMatchSnapshot();
  });
});
