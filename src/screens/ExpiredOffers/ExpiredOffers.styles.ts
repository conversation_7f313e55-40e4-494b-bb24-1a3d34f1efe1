import { Dimensions } from 'react-native';
import { styled } from 'styled-components/native';

const phoneWidth = Dimensions.get('screen').width > 320 ? 24 : 8;

export const BannerText = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.grey.one};
  font-size: 20px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: left;
  padding-right: ${phoneWidth}px;
  padding-left: ${phoneWidth}px;
`;

export const SubTitle = styled.Text`
  color: ${(p: any) => p.theme.subscriptionMfe.color.black2};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: left;
  padding-right: ${phoneWidth}px;
  padding-left: ${phoneWidth}px;
`;

export const IWantToStay = styled.TouchableOpacity``;

export const UnderlineText = styled.Text`
  height: 24px;
  width: 302px;
  color: ${(p: any) => p.theme.subscriptionMfe.color.primary};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
  text-decoration-line: underline;
`;

export const AppliedVoucherContainer = styled.ScrollView`
  flex: 1;
  flex-direction: column;
  background-color: #f5f5f5;
`;

export const TopViewContainer = styled.View``;

export const MiddleViewContainer = styled.View`
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
`;

export const BottomViewContainer = styled.View`
  padding-left: 16px;
  padding-right: 16px;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;
`;

export const BottomText = styled.Text`
  height: 40px;
  width: 343px;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 14px;
  letter-spacing: 0;
  line-height: 20px;
  text-align: center;
`;

export const DateText = styled.Text`
  text-align: left;
  font-size: 16px;
  align-items: flex-start;
  color: ${(p: any) => p.theme.subscriptionMfe.color.primary};
`;

export const SubText = styled.Text`
  text-align: left;
  font-size: 16px;
`;

export const InnerContainer = styled.ImageBackground`
  flex: 1;
  background-color: rgba(0, 0, 150, 0.8);
`;

export const ModalView = styled.View`
  width: 100%;
  justify-content: center;
  align-items: center;
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 20px;
`;

export const ModalTitleText = styled.Text`
  width: 100%;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.banner};
  font-size: 20px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const ModalText = styled.Text`
  width: 100%;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const HeaderPosition = styled.SafeAreaView``;
