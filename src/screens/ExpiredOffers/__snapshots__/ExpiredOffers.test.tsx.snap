// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Expired Offers component should render correctly 1`] = `
<RCTScrollView
  bounces={false}
  style={
    {
      "backgroundColor": "#f5f5f5",
      "flexBasis": 0,
      "flexDirection": "column",
      "flexGrow": 1,
      "flexShrink": 1,
    }
  }
>
  <View>
    <View
      style={{}}
    >
      <View
        style={
          {
            "height": 24,
            "width": 1,
          }
        }
      />
      <Text
        style={
          {
            "color": "#212121",
            "fontSize": 20,
            "letterSpacing": 0,
            "lineHeight": 24,
            "paddingLeft": 24,
            "paddingRight": 24,
            "textAlign": "left",
          }
        }
      >
        Your expired offers
      </Text>
      <View
        style={
          {
            "height": 18,
            "width": 1,
          }
        }
      />
      <Text
        style={
          {
            "color": "#1D1D26",
            "fontSize": 16,
            "letterSpacing": 0,
            "lineHeight": 24,
            "paddingLeft": 24,
            "paddingRight": 24,
            "textAlign": "left",
          }
        }
      >
        This list shows the offer codes currently expired for your subscription.
      </Text>
      <View
        style={
          {
            "height": 18,
            "width": 1,
          }
        }
      />
    </View>
    <View
      style={
        {
          "height": 32,
          "width": 1,
        }
      }
    />
  </View>
</RCTScrollView>
`;
