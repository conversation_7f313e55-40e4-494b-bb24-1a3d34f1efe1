import { styled } from 'styled-components/native';

interface ILightningWrapper {
  marginTop?: any;
}

export const ScreenWrapper = styled.View`
  flex: 1;
  justify-content: space-between;
`;
export const ScrollContent = styled.ScrollView`
  flex-grow: 1;
  padding: 24px;
`;
export const ScreenTitle = styled.Text`
  font-size: 16px;
  line-height: 28px;
  letter-spacing: 0.1px;
`;
export const PerksWrapper = styled.View`
  margin-top: 17px;
  gap: 16px;
`;
export const PerkContainer = styled.View`
  flex-direction: row;
`;
export const PerkTitleRow = styled.View`
  flex-direction: row;
  align-items: center;
`;
export const PerkTextWrapper = styled.View`
  margin-left: 10px;
  flex: 1;
`;
export const LightningWrapper = styled.View<ILightningWrapper>`
  align-self: flex-start;
  margin-top: ${({ marginTop }) => `${marginTop}px`};
`;
export const PerkTitle = styled.Text`
  font-size: 16px;
  font-weight: bold;
  line-height: 28px;
  letter-spacing: 0.1px;
`;
export const PerkSubtitle = styled.Text`
  font-size: 13px;
  line-height: 23px;
  letter-spacing: 0.2px;
`;
export const BottomFooter = styled.View`
  padding: 24px;
`;
export const NoticeWrapper = styled.View`
  gap: 14px;
  margin: 0 auto;
  margin-top: 20%;
`;
export const NoticeText = styled.Text`
  font-size: 12px;
  line-height: 21px;
  font-weight: 300;
`;
export const ButtonsWrapper = styled.View`
  gap: 16px;
`;
