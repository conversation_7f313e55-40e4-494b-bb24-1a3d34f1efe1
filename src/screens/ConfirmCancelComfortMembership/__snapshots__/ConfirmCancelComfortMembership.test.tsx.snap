// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<ConfirmCancelComfortMembership /> should correctly render screen contents 1`] = `
<RCTSafeAreaView
  style={
    {
      "flex": 1,
    }
  }
>
  <View
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "justifyContent": "space-between",
      }
    }
  >
    <RCTScrollView
      contentContainerStyle={
        {
          "flexGrow": 1,
          "paddingBottom": 24,
        }
      }
      style={
        {
          "flexGrow": 1,
          "paddingBottom": 24,
          "paddingLeft": 24,
          "paddingRight": 24,
          "paddingTop": 24,
        }
      }
    >
      <View>
        <Text
          style={
            {
              "fontSize": 16,
              "letterSpacing": 0.1,
              "lineHeight": 28,
            }
          }
        >
          When you cancel your Extra Tariff, you will charge again in the Classic Tariff​.
        </Text>
        <View
          style={
            {
              "gap": 16,
              "marginTop": 17,
            }
          }
        >
          <View
            style={
              {
                "flexDirection": "row",
              }
            }
          >
            <View
              marginTop={10}
              style={
                {
                  "alignSelf": "flex-start",
                  "marginTop": 10,
                }
              }
            >
              <SvgMock />
            </View>
            <View
              style={
                {
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "flexShrink": 1,
                  "marginLeft": 10,
                }
              }
            >
              <Text
                style={
                  {
                    "fontSize": 16,
                    "fontWeight": "bold",
                    "letterSpacing": 0.1,
                    "lineHeight": 28,
                  }
                }
              >
                Ideal if you drive 215+ km a month ¹
              </Text>
              <Text
                style={
                  {
                    "fontSize": 13,
                    "letterSpacing": 0.2,
                    "lineHeight": 23,
                  }
                }
              >
                Charge just 47 kWh and your savings cover the monthly fee – more charging means more savings!
              </Text>
            </View>
          </View>
        </View>
        <View
          style={
            {
              "gap": 14,
              "marginBottom": 0,
              "marginLeft": "auto",
              "marginRight": "auto",
              "marginTop": "20%",
            }
          }
        >
          <View>
            <Text
              style={
                {
                  "fontSize": 12,
                  "fontWeight": "300",
                  "lineHeight": 21,
                }
              }
            >
              ¹ Based on an average consumption of 20kWh/100km and charging at DC (more than 50kW) Aral pulse chargers in Germany.
            </Text>
          </View>
          <Text
            style={
              {
                "fontSize": 12,
                "fontWeight": "300",
                "lineHeight": 21,
              }
            }
          >
            Your Extra Tariff will end on 13/10/2024
          </Text>
        </View>
      </View>
    </RCTScrollView>
    <View
      style={
        {
          "paddingBottom": 24,
          "paddingLeft": 24,
          "paddingRight": 24,
          "paddingTop": 24,
        }
      }
    >
      <View
        style={
          {
            "gap": 16,
          }
        }
      >
        <View
          accessibilityHint="Yes, cancel Extra Tariff"
          accessibilityLabel="Yes, cancel Extra Tariff"
          accessibilityRole="button"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": false,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#000096",
              "borderBottomLeftRadius": 23,
              "borderBottomRightRadius": 23,
              "borderColor": "transparent",
              "borderStyle": "solid",
              "borderTopLeftRadius": 23,
              "borderTopRightRadius": 23,
              "borderWidth": 0,
              "justifyContent": "center",
              "minHeight": 46,
              "opacity": 1,
              "paddingHorizontal": 25.5,
              "paddingVertical": 12,
            }
          }
          testID="confirm-cancel-comfort-membership-cancel-subscription-button"
        >
          <View>
            <Text
              disabled={false}
              inverted={false}
              size="large"
              style={
                {
                  "color": "#ffffff",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 15,
                  "letterSpacing": 0.7,
                  "textAlign": "center",
                }
              }
              type="primary"
            >
              Yes, cancel Extra Tariff
            </Text>
          </View>
        </View>
        <View
          accessibilityHint="Navigates back to you Extra Tariff details page"
          accessibilityLabel="No, don't cancel my Extra Tariff"
          accessibilityRole="button"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": false,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#ffffff",
              "borderBottomLeftRadius": 23,
              "borderBottomRightRadius": 23,
              "borderColor": "#0164cc",
              "borderStyle": "solid",
              "borderTopLeftRadius": 23,
              "borderTopRightRadius": 23,
              "borderWidth": 1,
              "justifyContent": "center",
              "minHeight": 46,
              "opacity": 1,
              "paddingHorizontal": 25.5,
              "paddingVertical": 12,
            }
          }
          testID="confirm-cancel-comfort-membership-keep-subscription-button"
        >
          <View>
            <Text
              disabled={false}
              inverted={false}
              size="large"
              style={
                {
                  "color": "#111111",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 15,
                  "letterSpacing": 0.7,
                  "textAlign": "center",
                }
              }
              type="secondary"
            >
               Keep Extra Tariff
            </Text>
          </View>
        </View>
      </View>
    </View>
  </View>
</RCTSafeAreaView>
`;
