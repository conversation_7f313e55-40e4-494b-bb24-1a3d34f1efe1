import { MockedProvider } from '@apollo/client/testing';
import { ThemeProvider } from '@bp/ui-components/mobile';
import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';

import { CANCEL_SUBSCRIPTION } from '../../client/queries';
import { StatusReason, SubscriptionScreenNames } from '../../common/enums';
import { TRANSLATIONS } from '../../translations';
import { updateTranslations } from '../../utils/updateTranslations';
import ConfirmCancelComfortMembership from './ConfirmCancelComfortMembership';

const mockTranslations = updateTranslations(TRANSLATIONS.en_GB, {
  brand: 'bp',
});

const mockUseSettingsObj = {
  t: mockTranslations,
  featureFlags: {
    savingRate: 55,
    subsDrivingDistancekm: 215,
    subsChargekWh: 47,
  },
  isInternetReachable: true,
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);

jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => ({ userInfo: { country: 'UK' } }),
}));

const mockUserInfoObj = {
  userInfo: {
    userId: '123',
    latestMembership: {
      membershipEndDate: '2024-10-14T00:00:00.000Z',
      membershipBillingCycleDate: '2024-10-14T00:00:00.000Z',
    },
  },
};

const mockUserInfo = jest.fn().mockReturnValue(mockUserInfoObj);
jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfo(),
}));

const mockNavigate = jest.fn();
const mockDispatch = jest.fn();

const mockHostNavigationObj = {
  navigate: mockNavigate,
  navigation: {
    dispatch: mockDispatch,
  },
};

jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

const MOCKS = [
  {
    request: {
      query: CANCEL_SUBSCRIPTION,
      variables: {
        userId: '123',
      },
    },
    result: {
      data: {
        cancelSubscription: {
          statusReason: StatusReason.PENDING_CANCELLATION,
        },
      },
    },
  },
];

const renderScreen = () =>
  render(
    <ThemeProvider>
      <MockedProvider mocks={MOCKS}>
        <ConfirmCancelComfortMembership />
      </MockedProvider>
    </ThemeProvider>,
  );

const CANCEL_SUBSCRIPTION_BUTTON_TEST_ID =
  'confirm-cancel-comfort-membership-cancel-subscription-button';
const KEEP_SUBSCRIPTION_BUTTON_TEST_ID =
  'confirm-cancel-comfort-membership-keep-subscription-button';

describe('<ConfirmCancelComfortMembership />', () => {
  it('should correctly render screen contents', () => {
    const { toJSON } = renderScreen();

    expect(toJSON()).toMatchSnapshot();
  });
  it('should trigger navigate when clicked on cancel subscription button', async () => {
    const { getByTestId } = renderScreen();

    expect(getByTestId(CANCEL_SUBSCRIPTION_BUTTON_TEST_ID)).toBeDefined();

    fireEvent.press(getByTestId(CANCEL_SUBSCRIPTION_BUTTON_TEST_ID));

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('CancelSubscriptionFailed', {
        navigateBackScreen: SubscriptionScreenNames.MyComfortMembership,
      });
    });
  });
  it('should trigger navigation.dispatch when clicked on keep subscription button', () => {
    const { getByTestId } = renderScreen();

    expect(getByTestId(KEEP_SUBSCRIPTION_BUTTON_TEST_ID)).toBeDefined();

    fireEvent.press(getByTestId(KEEP_SUBSCRIPTION_BUTTON_TEST_ID));

    expect(mockDispatch).toHaveBeenCalledWith({ type: 'GO_BACK' });
  });
});
