import { useMutation } from '@apollo/client';
import {
  <PERSON>ton,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import { CommonActions } from '@react-navigation/native';
import React from 'react';
import { PixelRatio, SafeAreaView, View } from 'react-native';

import Lightning from '../../../assets/svg/lightning.svg';
import { CANCEL_WALLET_SUBSCRIPTION } from '../../client/queries';
import { SubscriptionScreenNames } from '../../common/enums';
import { ICancelWalletSubscriptionMutationResponse } from '../../common/interfaces';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { formatDateMinusOneDay } from '../../utils/helpers';
import * as S from './ConfirmCancelComfortMembership.styles';

const ConfirmCancelComfortMembership: React.FC = () => {
  const { t, featureFlags, isInternetReachable } = useSettings();
  const { userInfo } = useUserInfo();
  const { navigate, navigation } = useHostNavigation();

  const [cancelMembershipMutation, { loading: isCancelling }] = useMutation(
    CANCEL_WALLET_SUBSCRIPTION,
    {
      variables: {
        userId: userInfo!.userId,
      },
      onCompleted: async ({
        cancelWalletSubscription: { cancelledOn, ...rest },
      }: ICancelWalletSubscriptionMutationResponse) => {
        console.log('cancelledOn', cancelledOn);
        console.log('rest', rest);
        return navigate(
          SubscriptionScreenNames.AdacCancelSubsConfirmation,
          {
            cancelledOn,
          },
          true,
        );
      },
      onError: (e) => {
        navigate(SubscriptionScreenNames.CancelSubscriptionFailed, {
          navigateBackScreen: SubscriptionScreenNames.MyComfortMembership,
        });
        console.error(
          'Failed to do cancelSubscription mutation',
          JSON.stringify(e),
        );
      },
    },
  );

  const handleKeepClick = () => navigation.dispatch(CommonActions.goBack());
  const fontScale = PixelRatio.getFontScale();
  const iconOffset = fontScale * 5;

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <S.ScreenWrapper>
        <S.ScrollContent
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 24 }}
        >
          <S.ScreenTitle>
            {t.confirmCancelComfortMembership.title}
          </S.ScreenTitle>
          <S.PerksWrapper>
            <S.PerkContainer>
              <S.LightningWrapper marginTop={iconOffset}>
                <Lightning />
              </S.LightningWrapper>

              <S.PerkTextWrapper>
                <S.PerkTitle>
                  {t.confirmCancelComfortMembership.perks.idealDrive.title?.replace(
                    '{{subsDrivingDistancekm}}',
                    featureFlags.subsDrivingDistancekm,
                  )}
                </S.PerkTitle>
                <S.PerkSubtitle>
                  {t.confirmCancelComfortMembership.perks.idealDrive.subtitle?.replace(
                    '{{subsChargekWh}}',
                    featureFlags.subsChargekWh,
                  )}
                </S.PerkSubtitle>
              </S.PerkTextWrapper>
            </S.PerkContainer>
          </S.PerksWrapper>
          <S.NoticeWrapper>
            <View>
              <S.NoticeText>
                {t.confirmCancelComfortMembership.notices.averageConsumption}
              </S.NoticeText>
            </View>
            <S.NoticeText>
              {t.confirmCancelComfortMembership.notices.membershipWillEnd.replace(
                '{{endDate}}',
                formatDateMinusOneDay(
                  userInfo?.latestMembership.membershipBillingCycleDate!,
                ),
              )}
            </S.NoticeText>
          </S.NoticeWrapper>
        </S.ScrollContent>

        <S.BottomFooter>
          <S.ButtonsWrapper>
            <Button
              type={ButtonAction.PRIMARY}
              onPress={cancelMembershipMutation}
              size={ButtonSize.DEFAULT}
              accessibilityLabel={
                t.confirmCancelComfortMembership.buttons.cancel.accessibility
                  .label
              }
              accessibilityHint={
                t.confirmCancelComfortMembership.buttons.cancel.accessibility
                  .label
              }
              loading={isCancelling}
              disabled={isCancelling || !isInternetReachable}
              testID="confirm-cancel-comfort-membership-cancel-subscription-button"
            >
              {t.confirmCancelComfortMembership.buttons.cancel.label}
            </Button>

            <Button
              type={ButtonAction.SECONDARY}
              onPress={handleKeepClick}
              size={ButtonSize.DEFAULT}
              accessibilityLabel={
                t.confirmCancelComfortMembership.buttons.keep.accessibility
                  .label
              }
              accessibilityHint={
                t.confirmCancelComfortMembership.buttons.keep.accessibility.hint
              }
              disabled={isCancelling}
              testID="confirm-cancel-comfort-membership-keep-subscription-button"
            >
              {t.confirmCancelComfortMembership.buttons.keep.label}
            </Button>
          </S.ButtonsWrapper>
        </S.BottomFooter>
      </S.ScreenWrapper>
    </SafeAreaView>
  );
};

export default ConfirmCancelComfortMembership;
