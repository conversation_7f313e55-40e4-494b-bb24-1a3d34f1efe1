import { NavigationContainer } from '@react-navigation/native';
import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import { updateTranslations } from '../../utils/updateTranslations';
import { SubscriptionEnding } from './SubscriptionEnding';

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => ({
    navigate: jest.fn(),
    navigation: {
      getState: jest.fn(),
    },
  }),
}));

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'bp' }),
  user: { id: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
  isInternetReachable: true,
  featureFlags: {
    subsChargingRatesPromo: 0.44,
    introPercentageDiscount: 20,
  },
};

jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettingsObj,
}));

const mockUserInfoObj = {
  userInfo: {
    gocardless: {
      mandateStatus: 'mock',
      mandateId: 'mock',
    },
  },
  isLoading: false,
  refetchUserInfo: jest.fn(),
};

jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfoObj,
}));

jest.mock('../../providers/SubsPreferencesProvider', () => ({
  useSubsPreferences: () => ({ subPreferences: {} }),
}));

jest.mock('../../providers/WalletSubscriptionDataProvider', () => ({
  WalletSubscriptionData: () => ({
    walletSubscriptionData: {
      getWalletSubscription: {
        cancelledOn: '2023-09-15T00:00:00Z',
      },
    },
  }),
}));

const renderWithTheme = () =>
  render(
    <NavigationContainer>
      <Theme>
        <SubscriptionEnding />
      </Theme>
    </NavigationContainer>,
  );

describe('SubscriptionEnding component', () => {
  it('should render correctly from cancel subscription confirmation screen', () => {
    const { toJSON } = renderWithTheme();
    expect(toJSON()).toMatchSnapshot();
  });

  it('should return spinner when loading', () => {
    jest
      .spyOn(React, 'useState')
      .mockImplementationOnce(() => [true, jest.fn()]);
    const { queryAllByTestId } = renderWithTheme();
    expect(queryAllByTestId('Spinner')).toBeTruthy();
  });
});
