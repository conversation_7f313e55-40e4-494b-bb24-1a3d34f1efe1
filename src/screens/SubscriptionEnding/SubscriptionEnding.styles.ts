import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
`;

export const ScrollWrapper = styled.ScrollView``;

export const DescriptionWrapper = styled.View`
  padding: 24px 24px 16px 24px;
`;

export const PaymentContainer = styled.View`
  height: 80px;
  background-color: ${({ theme }) => theme.bpCore.colors.appBackground.primary};
`;
export const TextContainer = styled.Text`
  font-size: 16px;
  line-height: 23px;
`;
