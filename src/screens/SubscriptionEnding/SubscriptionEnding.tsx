import React, { useEffect, useState } from 'react';
import { BackHandler } from 'react-native';
import { useTheme } from 'styled-components/native';

import { Countries } from '../../common/enums';
import { Spacer } from '../../components';
import { HeaderWithBackground } from '../../components/Atoms/HeaderWithBackground/HeaderWithBackground';
import { RowWithIcon } from '../../components/Molecules/RowWithIcon/RowWithIcon';
import { LoadingPage } from '../../components/Organisms/LoadingPage/LoadingPage';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { WalletSubscriptionData } from '../../providers/WalletSubscriptionDataProvider';
import { formatDate } from '../../utils/helpers';
import { mySubscriptionContentData } from '../MySubscription/MySubscription.functions';
import * as S from './SubscriptionEnding.styles';

export const SubscriptionEnding = () => {
  const { t, featureFlags, isInternetReachable, onNavigateToProfile } =
    useSettings();
  const { walletSubscriptionData } = WalletSubscriptionData();
  const { userInfo } = useUserInfo();
  const theme = useTheme();
  const subsChargingRatesPromo = featureFlags.subsChargingRatesPromo;
  const userCountry = userInfo?.country;
  const [activeUntil, setActiveUntil] = useState('');

  useEffect(() => {
    if (
      walletSubscriptionData?.getWalletSubscription?.cancelledOn &&
      !isNaN(
        Date.parse(walletSubscriptionData?.getWalletSubscription?.cancelledOn),
      )
    ) {
      const cancelledOnDate = new Date(
        walletSubscriptionData?.getWalletSubscription?.cancelledOn,
      );
      cancelledOnDate.setDate(cancelledOnDate.getDate() - 1);
      setActiveUntil(formatDate(cancelledOnDate.toISOString()));
    }
  }, [walletSubscriptionData?.getWalletSubscription?.cancelledOn]);

  useEffect(() => {
    const handleBackButtonPress = () => {
      onNavigateToProfile();
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      handleBackButtonPress,
    );

    return () => backHandler.remove();
  }, [onNavigateToProfile]);

  if (isInternetReachable && !(walletSubscriptionData && userInfo)) {
    return <LoadingPage text={t.gocardless.text.LOADING} />;
  }

  return (
    <S.ScreenWrapper>
      <S.ScrollWrapper bounces={false}>
        <HeaderWithBackground
          title={
            userCountry?.toString() === Countries.DE
              ? t.subscriptionEnding.TITLE_EXTRA_TARIFF
              : t.subscriptionEnding.TITLE
          }
          subTitle={
            activeUntil
              ? t.subscriptionEnding.SUBTITLE.replace(
                  '{{activeUntil}}',
                  activeUntil,
                )
              : ''
          }
        />
        <Spacer vSpace={24} />
        {mySubscriptionContentData({
          theme,
          translations: t,
          subsChargingRatesPromo,
          userCountry,
          percentageDiscount: featureFlags.introPercentageDiscount.toString(),
          activeUntil,
        }).map(RowWithIcon)}
        <S.DescriptionWrapper>
          {activeUntil !== '' && userCountry?.toString() !== Countries.DE && (
            <S.TextContainer>
              {t.subscriptionEnding.TEXT.replace(
                '{{activeUntil}}',
                activeUntil,
              )}
            </S.TextContainer>
          )}
        </S.DescriptionWrapper>
      </S.ScrollWrapper>
    </S.ScreenWrapper>
  );
};
