// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MySubscription rendering Should render correctly 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 0,
    }
  }
>
  <RCTScrollView
    bounces={false}
    style={{}}
  >
    <View>
      <View
        accessibilityIgnoresInvertColors={true}
        style={
          {
            "justifyContent": "center",
          }
        }
      >
        <Image
          resizeMode="cover"
          source={
            {
              "testUri": "../../../assets/images/bgImage.png",
            }
          }
          style={
            [
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              },
              {
                "height": undefined,
                "width": undefined,
              },
              undefined,
            ]
          }
        />
        <View
          style={
            {
              "alignItems": "center",
              "fontFamily": "Roboto-Medium",
              "justifyContent": "flex-start",
              "marginTop": 25,
              "paddingBottom": 0,
              "paddingLeft": 24,
              "paddingRight": 24,
              "paddingTop": 0,
              "width": "100%",
            }
          }
        >
          <Text
            style={
              {
                "color": "#FFFFFF",
                "fontSize": 38,
                "letterSpacing": 0.4,
                "lineHeight": 55,
                "maxWidth": 330,
                "textAlign": "center",
              }
            }
            type="primary"
          >
            Currently saving up to 20%
          </Text>
          <Text
            style={
              {
                "color": "#FFFFFF",
                "fontFamily": "Roboto-Light",
                "fontSize": 26,
                "lineHeight": 39,
                "marginBottom": 16,
                "textAlign": "center",
              }
            }
          >
            on our standard rates*
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "height": 24,
            "width": 1,
          }
        }
      />
      <View
        style={
          {
            "alignItems": "flex-start",
            "flexDirection": "row",
            "paddingBottom": 0,
            "paddingLeft": 16,
            "paddingRight": 16,
            "paddingTop": 0,
          }
        }
      >
        <View
          style={
            {
              "marginRight": 8,
              "paddingTop": 8,
              "width": 20,
            }
          }
        >
          <RNSVGSvgView
            bbHeight="24"
            bbWidth="24"
            focusable={false}
            height="24"
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 24,
                  "width": 24,
                },
              ]
            }
            width="24"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                propList={
                  [
                    "fill",
                    "stroke",
                  ]
                }
                stroke={null}
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      -590,
                      -644,
                    ]
                  }
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        588,
                        642,
                      ]
                    }
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          2,
                          2,
                        ]
                      }
                    >
                      <RNSVGPath
                        d="M15.0658784,0.251930531 C15.3381714,-0.224582136 16.0681486,0.0220722076 15.995614,0.56608186 L15.995614,0.56608186 L15.07,7.5 L19.5018094,7.5 C19.8484189,7.5 20.080858,7.83950965 19.9779221,8.15356538 L19.9779221,8.15356538 L19.9450732,8.23133782 L13.9432638,19.7313378 C13.6863867,20.2235371 12.9385545,19.9929757 13.0034247,19.4415794 L13.0034247,19.4415794 L13.937,11.5 L9.5,11.5 C9.14562031,11.5 8.91328081,11.1466111 9.02944566,10.8300718 L9.02944566,10.8300718 L9.06587843,10.7519305 Z M14.666,2.967 L10.361,10.5 L14.5,10.5 C14.769367,10.5 14.9844253,10.7115509 14.99925,10.9705539 L14.99925,10.9705539 L14.9965753,11.0584206 L14.314,16.858 L18.676,8.5 L14.5,8.5 C14.2278448,8.5 14.0117495,8.2842647 14.0003869,8.02260574 L14.0003869,8.02260574 L14.004386,7.93391814 L14.666,2.967 Z M7.5,14 C7.77614237,14 8,14.2238576 8,14.5 C8,14.7761424 7.77614237,15 7.5,15 L4.5,15 C4.22385763,15 4,14.7761424 4,14.5 C4,14.2238576 4.22385763,14 4.5,14 L7.5,14 Z M7.5,7 C7.77614237,7 8,7.22385763 8,7.5 C8,7.77614237 7.77614237,8 7.5,8 L0.5,8 C0.223857625,8 3.38176876e-17,7.77614237 0,7.5 C-3.38176876e-17,7.22385763 0.223857625,7 0.5,7 L7.5,7 Z M11.5,3 C11.7761424,3 12,3.22385763 12,3.5 C12,3.77614237 11.7761424,4 11.5,4 L4.5,4 C4.22385763,4 4,3.77614237 4,3.5 C4,3.22385763 4.22385763,3 4.5,3 L11.5,3 Z"
                        fill={
                          {
                            "payload": 4278190230,
                            "type": 0,
                          }
                        }
                        propList={
                          [
                            "fill",
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
        <Text
          style={
            {
              "paddingBottom": 12,
              "paddingLeft": 0,
              "paddingRight": 24,
              "paddingTop": 0,
            }
          }
        >
          <Text
            style={
              {
                "color": "#1D1D26",
                "fontSize": 16,
                "fontWeight": "bold",
                "lineHeight": 28,
                "textAlign": "left",
              }
            }
          >
            Savings on all bp pulse chargers
            

          </Text>
          <Text
            style={
              {
                "color": "#1D1D26",
                "fontSize": 13,
                "lineHeight": 24,
                "textAlign": "left",
              }
            }
          >
            Save up to 20% across the entire bp pulse network versus our standard rates*
          </Text>
        </Text>
      </View>
      <View
        style={
          {
            "alignItems": "flex-start",
            "flexDirection": "row",
            "paddingBottom": 0,
            "paddingLeft": 16,
            "paddingRight": 16,
            "paddingTop": 0,
          }
        }
      >
        <View
          style={
            {
              "marginRight": 8,
              "paddingTop": 8,
              "width": 20,
            }
          }
        >
          <RNSVGSvgView
            bbHeight="24"
            bbWidth="24"
            focusable={false}
            height="24"
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 24,
                  "width": 24,
                },
              ]
            }
            width="24"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                propList={
                  [
                    "fill",
                  ]
                }
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                  matrix={
                    [
                      1,
                      0,
                      0,
                      1,
                      -592,
                      -1079,
                    ]
                  }
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        588,
                        1074,
                      ]
                    }
                  >
                    <RNSVGPath
                      d="M9 9h6m3 0-6 8.4L6 9l2.4-3h7.2L18 9z"
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      propList={
                        [
                          "stroke",
                        ]
                      }
                      stroke={
                        {
                          "payload": 4278190230,
                          "type": 0,
                        }
                      }
                    />
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
        <Text
          style={
            {
              "paddingBottom": 12,
              "paddingLeft": 0,
              "paddingRight": 24,
              "paddingTop": 0,
            }
          }
        >
          <Text
            style={
              {
                "color": "#1D1D26",
                "fontSize": 16,
                "fontWeight": "bold",
                "lineHeight": 28,
                "textAlign": "left",
              }
            }
          >
            Our best on the go charging rates - from just £0.44/kWh.
            

          </Text>
          <Text
            style={
              {
                "color": "#1D1D26",
                "fontSize": 13,
                "lineHeight": 24,
                "textAlign": "left",
              }
            }
          />
        </Text>
      </View>
      <View
        style={{}}
      >
        <Text
          needsPadding={true}
          style={
            {
              "color": "#212121",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 28,
              "paddingBottom": 8,
              "paddingLeft": 24,
            }
          }
        >
          Subscription details
        </Text>
        <View
          style={
            {
              "borderColor": "#dedede",
              "borderLeftWidth": 0,
              "borderRightWidth": 0,
              "borderStyle": "solid",
              "borderWidth": 1,
              "minHeight": 221,
              "paddingBottom": 16,
              "paddingLeft": 24,
              "paddingRight": 24,
              "paddingTop": 16,
            }
          }
        >
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
            testID="my-subscription-details-start-date-row"
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              Start date
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              03/07/2023
            </Text>
          </View>
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              Billing date
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              3rd
            </Text>
          </View>
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              Billing schedule
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              Monthly
            </Text>
          </View>
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              Subscription (Excl. discount)
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              £8.00
            </Text>
          </View>
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              Discount
            </Text>
            <Text
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "400",
                  "lineHeight": 23,
                }
              }
            >
              -£1.00
            </Text>
          </View>
          <View
            style={
              {
                "display": "flex",
                "flexDirection": "row",
                "justifyContent": "space-between",
                "paddingBottom": 8,
              }
            }
          >
            <Text
              bold={true}
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "700",
                  "lineHeight": 23,
                }
              }
            >
              Total
            </Text>
            <Text
              bold={true}
              style={
                {
                  "color": "#212121",
                  "fontSize": 13,
                  "fontWeight": "700",
                  "lineHeight": 23,
                }
              }
            >
              £7.00
            </Text>
          </View>
        </View>
      </View>
      <View
        style={
          {
            "paddingBottom": 16,
            "paddingLeft": 24,
            "paddingRight": 24,
            "paddingTop": 24,
          }
        }
      >
        <View
          style={
            {
              "display": "flex",
            }
          }
        >
          <Text
            needsPaddingBottom={true}
            style={
              {
                "color": "#212121",
                "fontSize": 16,
                "fontWeight": "400",
                "lineHeight": 28,
                "paddingBottom": 8,
                "paddingLeft": 0,
              }
            }
          >
            Selected payment method
          </Text>
          <Text
            style={
              {
                "color": "#212121",
                "fontSize": 12,
                "fontWeight": "400",
                "lineHeight": 21,
              }
            }
          >
            This card will collect payments for your monthly subscription and can be changed anytime.
          </Text>
        </View>
      </View>
      <View
        accessibilityHint="Cancel your subscription"
        accessibilityLabel="Cancel your subscription"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": false,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        onBlur={[Function]}
        onClick={[Function]}
        onFocus={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "alignItems": "center",
            "borderColor": "#dedede",
            "borderLeftWidth": 0,
            "borderRightWidth": 0,
            "borderStyle": "solid",
            "borderTopWidth": 0,
            "borderWidth": 1,
            "display": "flex",
            "flexDirection": "row",
            "paddingBottom": 25,
            "paddingLeft": 24,
            "paddingRight": 24,
            "paddingTop": 25,
          }
        }
        testID="cancelWidget"
      >
        <RNSVGSvgView
          align="xMidYMid"
          bbHeight="24"
          bbWidth="24"
          focusable={false}
          height="24"
          meetOrSlice={0}
          minX={0}
          minY={0}
          style={
            [
              {
                "backgroundColor": "transparent",
                "borderWidth": 0,
              },
              {
                "flex": 0,
                "height": 24,
                "width": 24,
              },
            ]
          }
          testID="rightArrow"
          vbHeight={24}
          vbWidth={24}
          width="24"
        >
          <RNSVGGroup
            fill={
              {
                "payload": 4278190080,
                "type": 0,
              }
            }
          >
            <RNSVGGroup
              fill={null}
              propList={
                [
                  "fill",
                ]
              }
            >
              <RNSVGPath
                d="M0 0h24v24H0z"
                fill={null}
                propList={
                  [
                    "fill",
                  ]
                }
              />
              <RNSVGPath
                d="M15 7.38c1.8 1.02 3 3 3 5.22 0 3.3-2.7 6-6 6s-6-2.7-6-6c0-2.22 1.2-4.14 3-5.22m3-1.98v4.2"
                fill={
                  {
                    "payload": 4278190080,
                    "type": 0,
                  }
                }
                propList={
                  [
                    "stroke",
                  ]
                }
                stroke={
                  {
                    "payload": 4293282121,
                    "type": 0,
                  }
                }
              />
            </RNSVGGroup>
          </RNSVGGroup>
        </RNSVGSvgView>
        <Text
          style={
            {
              "color": "#e64949",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 28,
              "marginLeft": 16,
            }
          }
        >
          Cancel your subscription
        </Text>
        <View
          style={
            {
              "display": "flex",
              "flexBasis": 0,
              "flexDirection": "row",
              "flexGrow": 1,
              "flexShrink": 1,
              "justifyContent": "flex-end",
            }
          }
        >
          <RNSVGSvgView
            align="xMidYMid"
            bbHeight="24"
            bbWidth="24"
            focusable={false}
            height="24"
            meetOrSlice={0}
            minX={0}
            minY={0}
            style={
              [
                {
                  "backgroundColor": "transparent",
                  "borderWidth": 0,
                },
                {
                  "flex": 0,
                  "height": 24,
                  "width": 24,
                },
              ]
            }
            testID="rightArrow"
            vbHeight={24}
            vbWidth={24}
            width="24"
          >
            <RNSVGGroup
              fill={
                {
                  "payload": 4278190080,
                  "type": 0,
                }
              }
            >
              <RNSVGGroup
                fill={null}
                propList={
                  [
                    "fill",
                  ]
                }
              >
                <RNSVGPath
                  d="M0 0h24v24H0z"
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                />
                <RNSVGPath
                  d="M8.646 18.354a.5.5 0 0 1-.057-.638l.057-.07L14.293 12 8.646 6.354a.5.5 0 0 1-.057-.638l.057-.07a.5.5 0 0 1 .638-.057l.07.057 6 6a.5.5 0 0 1 .057.638l-.057.07-6 6a.5.5 0 0 1-.708 0z"
                  fill={
                    {
                      "payload": 4293282121,
                      "type": 0,
                    }
                  }
                  propList={
                    [
                      "fill",
                    ]
                  }
                />
              </RNSVGGroup>
            </RNSVGGroup>
          </RNSVGSvgView>
        </View>
      </View>
      <View
        style={
          {
            "justifyContent": "center",
          }
        }
      >
        <Text
          style={
            {
              "fontFamily": "Roboto-Regular",
              "marginBottom": 16,
              "marginLeft": 24,
              "marginRight": 24,
              "marginTop": 10,
            }
          }
        >
          <Text
            style={
              {
                "color": "#1D1D26",
                "fontSize": 13,
                "fontWeight": "300",
                "letterSpacing": 0.2,
                "lineHeight": 23,
                "textAlign": "left",
              }
            }
          >
            * Payment by bp pulse charge card or via the bp pulse app is required. Rates and average subscriber savings are subject to change and can vary across the bp pulse network – check 
            <Text
              accessibilityLabel="here"
              accessibilityRole="button"
              onPress={[Function]}
              style={
                {
                  "alignItems": "flex-start",
                  "color": "#000096",
                  "fontSize": 12,
                  "textAlign": "center",
                  "textDecorationColor": "black",
                  "textDecorationLine": "underline",
                  "textDecorationStyle": "solid",
                }
              }
            >
              here
            </Text>
             for latest prices.
          </Text>
        </Text>
      </View>
      <View
        style={
          {
            "height": 24,
            "width": 1,
          }
        }
      />
    </View>
  </RCTScrollView>
  <Modal
    animationType="fade"
    hardwareAccelerated={false}
    onRequestClose={[Function]}
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 0,
        "width": "100%",
      }
    }
    transparent={true}
    visible={false}
  />
</RCTSafeAreaView>
`;
