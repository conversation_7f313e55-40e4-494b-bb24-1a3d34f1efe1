import { useWallet } from '@bp/bppay-wallet-feature';
import { ActionConfirmationModal } from '@bp/ui-components/mobile/core';
import React, { useCallback, useEffect, useState } from 'react';
import { useTheme } from 'styled-components/native';

import {
  SubsAnalyticsEventCancelSubscriptionClick,
  SubsAnalyticsEventSubsMySubscriptionScreenOpen,
} from '../../analytics/events';
import { SubscriptionScreenNames } from '../../common/enums';
import { LinkParagraphComponent, Spacer } from '../../components';
import { CardDescription } from '../../components/Atoms/CardDescription/CardDescription';
import { HeaderWithBackground } from '../../components/Atoms/HeaderWithBackground/HeaderWithBackground';
import { CancelSubscriptionWidget } from '../../components/Molecules/CancelSubscriptionWidget/CancelSubscriptionWidget';
import { Notifications } from '../../components/Molecules/Notifications/Notifications';
import { RowWithIcon } from '../../components/Molecules/RowWithIcon/RowWithIcon';
import { LoadingPage } from '../../components/Organisms/LoadingPage/LoadingPage';
import { MySubscriptionDetails } from '../../components/Organisms/MySubscriptionDetails/MySubscriptionDetails';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { mySubscriptionContentData } from './MySubscription.functions';
import * as S from './MySubscription.styles';

export const MySubscription = () => {
  const {
    t,
    cardComponent,
    isInternetReachable,
    featureFlags,
    onAnalyticsEvent,
  } = useSettings();
  const {
    subsDefaultNotificationTrigger,
    setSubsDefaultNotificationTrigger,
    subsDefaultCardError,
    setSubsDefaultCardError,
  } = useWallet();
  const { userInfo } = useUserInfo();
  const theme = useTheme();
  const [modalIsVisible, setModalIsVisible] = useState(false);

  const { navigate } = useHostNavigation();

  const onHandleClick = (shouldNavigate: boolean) => {
    setModalIsVisible(false);
    if (shouldNavigate) {
      navigate(SubscriptionScreenNames.CancelMembership);
    }
  };

  const onComplete = useCallback(() => {
    setSubsDefaultCardError(false);
    setSubsDefaultNotificationTrigger(false);
  }, [setSubsDefaultCardError, setSubsDefaultNotificationTrigger]);

  useEffect(() => {
    onAnalyticsEvent(SubsAnalyticsEventSubsMySubscriptionScreenOpen({}));
  }, [onAnalyticsEvent]);

  if (isInternetReachable && !(featureFlags && userInfo)) {
    return <LoadingPage text={t.gocardless.text.LOADING} />;
  }

  return (
    <S.ScreenWrapper>
      <S.ScrollWrapper bounces={false}>
        <HeaderWithBackground
          title={t.mySubscription.TITLE.replace(
            '{{percentageDiscount}}',
            featureFlags.introPercentageDiscount.toString(),
          )}
          subTitle={t.mySubscription.SUBTITLE}
        />
        <Spacer vSpace={24} />
        {mySubscriptionContentData({
          theme,
          translations: t,
          subsChargingRatesPromo: featureFlags.subsChargingRatesPromo,
          userCountry: userInfo?.country,
          percentageDiscount: featureFlags.introPercentageDiscount.toString(),
        }).map(RowWithIcon)}
        <MySubscriptionDetails
          isMySubscription
          userCountry={userInfo?.country!}
        />
        <S.DescriptionWrapper>
          <CardDescription
            title={t.mySubscription.CARD_DESCRIPTION.TITLE}
            description={t.mySubscription.CARD_DESCRIPTION.DESCRIPTION}
          />
        </S.DescriptionWrapper>
        {cardComponent && (
          <S.PaymentContainer>{cardComponent}</S.PaymentContainer>
        )}
        <CancelSubscriptionWidget
          theme={theme}
          disabled={!isInternetReachable}
          title={t.cancelSubscription.TITLE}
          onCancel={() => {
            setModalIsVisible(true);
            onAnalyticsEvent(SubsAnalyticsEventCancelSubscriptionClick({}));
          }}
        />
        <S.BottomViewContainer>
          <LinkParagraphComponent />
        </S.BottomViewContainer>

        <Spacer vSpace={24} />
      </S.ScrollWrapper>
      <ActionConfirmationModal
        isVisible={modalIsVisible}
        titleText={t.mySubscription.CANCEL_MODAL.TITLE}
        titleMessage={t.mySubscription.CANCEL_MODAL.DESCRIPTION.replace(
          '{{percentageDiscount}}',
          featureFlags.introPercentageDiscount.toString(),
        )}
        primaryButtonText={t.mySubscription.CANCEL_MODAL.KEEP}
        accessibilityLabel={t.mySubscription.CANCEL_MODAL.KEEP}
        primaryButtonOnPress={() => onHandleClick(false)}
        secondaryButtonText={t.mySubscription.CANCEL_MODAL.LOSE}
        secondaryAccessibilityLabel={t.mySubscription.CANCEL_MODAL.LOSE}
        secondaryButtonOnPress={() => onHandleClick(true)}
      />
      {(subsDefaultNotificationTrigger || subsDefaultCardError) && (
        <Notifications
          success={!subsDefaultCardError}
          onComplete={onComplete}
        />
      )}
    </S.ScreenWrapper>
  );
};
