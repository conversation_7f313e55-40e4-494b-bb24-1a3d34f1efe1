import React from 'react';

import { Countries, PulseDataItemIds } from '../../common/enums';
import { IMySubscriptionData } from '../../common/interfaces';
import { ChargingOnGoIcon, PolarDiamondIcon } from '../../components/Atoms';
import { formatCurrency, getCurrency } from '../../utils/helpers';
export const mySubscriptionContentData = ({
  theme,
  translations,
  subsChargingRatesPromo,
  userCountry,
  percentageDiscount,
  activeUntil,
}: IMySubscriptionData) => {
  if (userCountry === Countries.DE) {
    return [
      {
        id: PulseDataItemIds.BP_PULSE_CARD,
        icon: (
          <ChargingOnGoIcon
            color={theme.subscriptionMfe?.text.color.tertiary}
          />
        ),
        title: translations.cancelSubscription.STEP_4.TITLE,
        subTitle: activeUntil
          ? translations.subscriptionEnding.TEXT_EXTRA_TARIFF.replace(
              '{{activeUntil}}',
              activeUntil,
            )
          : '',
      },
    ];
  } else {
    return [
      {
        id: PulseDataItemIds.BP_PULSE_CARD,
        icon: (
          <ChargingOnGoIcon
            color={theme.subscriptionMfe?.text.color.tertiary}
          />
        ),
        title: translations.cancelSubscription.STEP_1.TITLE,
        subTitle: translations.cancelSubscription.STEP_1.SUBTITLE.replace(
          '{{percentageDiscount}}',
          percentageDiscount ?? '',
        ),
      },
      {
        id: PulseDataItemIds.DIAMOND,
        icon: (
          <PolarDiamondIcon
            color={theme.subscriptionMfe?.text.color.tertiary}
          />
        ),
        title: translations.cancelSubscription.STEP_3.TITLE.replace(
          '{{rate}}',
          formatCurrency({
            amount: subsChargingRatesPromo,
            currency: getCurrency(userCountry),
          }),
        ),
        subTitle: '',
      },
    ];
  }
};
