import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import {
  MandateStatus,
  MembershipStatus,
  SubscriptionScreenNames,
  UserCountry,
  UserTypeEnum,
} from '../../common/enums';
import { IMmembership, IUserInfo } from '../../common/interfaces';
import Theme from '../../themes/Theme';
import TRANSLATIONS from '../../translations/messages.json';
import { rawData } from '../../utils/mockData';
import { updateTranslations } from '../../utils/updateTranslations';
import { MySubscription } from './MySubscription';
const mockWalletSubscriptionObj = {
  walletSubscriptionData: rawData,
  subscription: {
    billingItemId: '651dce90-2497-4a13-9503-e16cccbb375b',
    planId: 'ac7e03e1-0da2-4515-8412-7ec556f32b8d',
    name: 'Plan A - September 14',
    amount: '0',
    currency: 'GBP',
  },
  totalValue: 7.99,
  currency: 'GBP',
};
const mockWalletSubscriptionData = jest
  .fn()
  .mockReturnValue(mockWalletSubscriptionObj);
jest.mock('../../providers/WalletSubscriptionDataProvider', () => ({
  WalletSubscriptionData: () => mockWalletSubscriptionData(),
}));
const mockTranslations = TRANSLATIONS;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'bp' }),
  isInternetReachable: true,
  featureFlags: {
    subsChargingRatesPromo: 0.44,
    introPercentageDiscount: 20,
  },
  onAnalyticsEvent: () => jest.fn(),
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

const mockMembership: IMmembership = {
  userType: UserTypeEnum.SUBS_WALLET,
  membershipStatus: MembershipStatus.CANCELLED,
  membershipRequestCancelDate: null,
  membershipStartDate: '2024-10-16T00:00:00.000Z',
  membershipEndDate: '',
  membershipBillingCycleDate: '2024-11-16T00:00:00.000Z',
};

const mockUserInfoObj: { userInfo: IUserInfo } = {
  userInfo: {
    userId: 'user-id',
    type: 'user-type',
    status: '',
    gocardless: {
      mandateStatus: MandateStatus.ACTIVE,
      mandateId: '',
    },
    tagIds: [],
    balance: 0,
    country: UserCountry.UK,
    membership: [mockMembership],
    latestMembership: mockMembership,
    partnerType: '',
  },
};
const mockUserInfo = jest.fn().mockReturnValue(mockUserInfoObj);
jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfo(),
}));

const renderWithTheme = () =>
  render(
    <Theme>
      <MySubscription />
    </Theme>,
  );
describe('MySubscription rendering', () => {
  it('Should render correctly', () => {
    const result = renderWithTheme();
    expect(result.toJSON()).toMatchSnapshot();
  });

  it('Should show and hide the modal on specific actions', async () => {
    const { getByText, queryByText } = renderWithTheme();
    const button = getByText(mockTranslations.cancelSubscription.TITLE);
    fireEvent(button, 'onCancel');
    expect(
      getByText(mockTranslations.mySubscription.CANCEL_MODAL.TITLE),
    ).toBeDefined();

    const keepButton = getByText(
      mockTranslations.mySubscription.CANCEL_MODAL.KEEP,
    );
    fireEvent(keepButton, 'primaryButtonOnPress');
    expect(
      queryByText(mockTranslations.mySubscription.CANCEL_MODAL.TITLE),
    ).toBeNull();
  });

  it('Should navigate to cancelMembershipScreen', async () => {
    const { getByText } = renderWithTheme();
    const button = getByText(mockTranslations.cancelSubscription.TITLE);
    fireEvent(button, 'onCancel');

    const loseButton = getByText(
      mockTranslations.mySubscription.CANCEL_MODAL.LOSE,
    );
    expect(loseButton).toBeDefined();

    fireEvent(loseButton, 'secondaryButtonOnPress');
    expect(mockHostNavigationObj.navigate).toHaveBeenCalledWith(
      SubscriptionScreenNames.CancelMembership,
    );
  });
});
