// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Cancel Subscription Confirmation component should render correctly 1`] = `
[
  <RCTScrollView
    alwaysBounceVertical={false}
    contentContainerStyle={
      {
        "flexGrow": 1,
      }
    }
    style={{}}
    testID="cancel-subscription-confirmation-wrapper-container"
  >
    <View>
      <Text
        style={
          {
            "color": "#111111",
            "fontSize": 28,
            "fontWeight": "normal",
            "letterSpacing": 0.5,
            "lineHeight": 40,
            "marginBottom": 16,
            "marginHorizontal": 24,
            "textAlign": "center",
          }
        }
        testID="cancel-subscription-confirmation-wrapper-title"
      >
        Sorry to see you leave
      </Text>
      <View
        style={
          {
            "alignItems": "center",
            "marginVertical": "5%",
          }
        }
        testID="cancel-subscription-confirmation-wrapper-image-container"
      >
        <SvgMock />
      </View>
      <Text
        numberOfLines={5}
        style={
          {
            "color": "#111111",
            "fontSize": 18,
            "letterSpacing": 0.15,
            "lineHeight": 32,
            "marginHorizontal": 24,
            "textAlign": "center",
          }
        }
        testID="cancel-subscription-confirmation-wrapper-subtitle"
      >
        Your subscription has been cancelled.
      </Text>
    </View>
  </RCTScrollView>,
  <View
    bottomInset={0}
    style={
      {
        "marginHorizontal": 24,
        "paddingBottom": 24,
      }
    }
    testID="cancel-subscription-confirmation-wrapper-children-wrapper"
  >
    <View
      accessibilityHint="Finish"
      accessibilityLabel="Finish"
      accessibilityRole="button"
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": false,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "backgroundColor": "#000096",
          "borderBottomLeftRadius": 28,
          "borderBottomRightRadius": 28,
          "borderColor": "transparent",
          "borderStyle": "solid",
          "borderTopLeftRadius": 28,
          "borderTopRightRadius": 28,
          "borderWidth": 0,
          "justifyContent": "center",
          "minHeight": 56,
          "opacity": 1,
          "paddingHorizontal": 29.5,
          "paddingVertical": 15,
        }
      }
      testID="cancel-subscription-confirmation-finish-button"
    >
      <View>
        <Text
          disabled={false}
          inverted={false}
          size="xlarge"
          style={
            {
              "color": "#ffffff",
              "fontFamily": "Roboto-Regular",
              "fontSize": 16,
              "letterSpacing": 0.7,
              "textAlign": "center",
            }
          }
          type="primary"
        >
          Finish
        </Text>
      </View>
    </View>
  </View>,
]
`;
