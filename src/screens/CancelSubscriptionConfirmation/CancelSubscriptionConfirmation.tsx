import {
  But<PERSON>,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import React, { useEffect } from 'react';

import SadChargepoint from '../../../assets/svg/sadChargepoint.svg';
import { SubsAnalyticsEventSubsUnpaidSubsDowngradeSuccess } from '../../analytics/events';
import { SubscriptionScreenNames } from '../../common/enums';
import { SubscriptionCancelledWrapper } from '../../components';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useSubscriptionContext } from '../../providers/SubscriptionProvider';
import { useUserInfo } from '../../providers/UserInfoProvider';

const CancelSubscriptionConfirmation = ({ route }: any) => {
  const { navigate } = useHostNavigation();
  const { t, onAnalyticsEvent } = useSettings();
  const { userInfo } = useUserInfo();

  const { isSubscriptionPaused } = useSubscriptionContext();

  const { params } = route;

  const onPressFinish = () => {
    navigate(SubscriptionScreenNames.SubscriptionEnding);
  };

  useEffect(() => {
    if (isSubscriptionPaused) {
      onAnalyticsEvent(
        SubsAnalyticsEventSubsUnpaidSubsDowngradeSuccess({
          tag_ids: userInfo?.tagIds || [],
        }),
      );
    }
  }, [isSubscriptionPaused, onAnalyticsEvent, userInfo?.tagIds]);

  const membership = userInfo?.membership;

  const isMembershipActive =
    membership?.some((member) => member.membershipStatus === 'ACTIVE') || false;

  return (
    <SubscriptionCancelledWrapper
      title={t.gocardless.text.SORRY_TO_SEE_YOU_LEAVE_TITLE}
      subtitle={
        !isMembershipActive || !params?.cancelledOn
          ? t.gocardless.text.SORRY_TO_SEE_YOU_LEAVE_CANCEL_TXT_SIMPLE
          : t.gocardless.text.SORRY_TO_SEE_YOU_LEAVE_CANCEL_TXT.replace(
              '{{endDate}}',
              params?.cancelledOn,
            )
      }
      image={<SadChargepoint />}
      testId="cancel-subscription-confirmation"
    >
      <Button
        accessibilityLabel={t.gocardless.accessibility.FINISH_BUTTON.LABEL}
        accessibilityHint={t.gocardless.accessibility.FINISH_BUTTON.HINT}
        type={ButtonAction.PRIMARY}
        size={ButtonSize.XLARGE}
        onPress={onPressFinish}
        testID="cancel-subscription-confirmation-finish-button"
      >
        {t.gocardless.buttons.FINISH_BTN}
      </Button>
    </SubscriptionCancelledWrapper>
  );
};

export default CancelSubscriptionConfirmation;
