import { NavigationContainer } from '@react-navigation/native';
import { fireEvent, render, screen } from '@testing-library/react-native';
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { SubscriptionScreenNames } from '../../common/enums';
import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import CancelSubscriptionConfirmation from './CancelSubscriptionConfirmation';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockNavigate = jest.fn();
const mockUseFocusEffect = jest.fn();

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useFocusEffect: (callback: () => void) => {
    mockUseFocusEffect(callback);
  },
}));

jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

const mockRoute = {
  params: {
    cancelledOn: '2024/08/08',
  },
};

const renderWithTheme = () =>
  render(
    <NavigationContainer>
      <Theme>
        <SafeAreaProvider>
          <CancelSubscriptionConfirmation route={mockRoute} />
        </SafeAreaProvider>
      </Theme>
    </NavigationContainer>,
  );

describe('Cancel Subscription Confirmation component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    const { toJSON } = renderWithTheme();
    expect(toJSON()).toMatchSnapshot();
  });

  it('should click to finish button', () => {
    renderWithTheme();
    const { getByText } = screen;
    const finishButton = getByText(
      mockUseSettingsObj.t.gocardless.buttons.FINISH_BTN,
    );
    fireEvent.press(finishButton);
    expect(mockNavigate).toHaveBeenCalledWith(
      SubscriptionScreenNames.SubscriptionEnding,
    );
  });

  it('should display correct title', () => {
    renderWithTheme();
    const { getByText } = screen;

    expect(
      getByText(
        mockUseSettingsObj.t.gocardless.text.SORRY_TO_SEE_YOU_LEAVE_TITLE,
      ),
    ).toBeTruthy();
  });

  it('should have correct accessibility properties on finish button', () => {
    renderWithTheme();
    const { getByLabelText } = screen;

    const finishButton = getByLabelText(
      mockUseSettingsObj.t.gocardless.accessibility.FINISH_BUTTON.LABEL,
    );
    expect(finishButton).toBeTruthy();
    expect(finishButton.props.accessibilityHint).toBe(
      mockUseSettingsObj.t.gocardless.accessibility.FINISH_BUTTON.HINT,
    );
  });
});
