import { ImageBackground } from 'react-native';
import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
`;
export const ScrollWrapper = styled.ScrollView``;

export const UpperViewContainer = styled(ImageBackground)`
  justify-content: center;
`;

export const TitleContainer = styled.View`
  width: 100%;
  padding: 10px 24px;
  justify-content: space-between;
  align-items: center;
`;
export const FreeText = styled.Text<{ type: string }>`
  color: ${({ theme }) => theme.subscriptionMfe.color.white};
  font-size: 40px;
  line-height: 47px;
  letter-spacing: 0.4px;
  font-weight: bold;
  text-align: center;
  max-width: ${({ type }) => (type === 'primary' ? '350px' : 'unset')};
`;
export const Text = styled.Text`
  color: ${({ theme }) => theme.subscriptionMfe.color.white};
  font-size: 20px;
  font-weight: 300;
  line-height: 39px;
  text-align: center;
  margin-bottom: 16px;
`;
export const DescriptionWrapper = styled.View`
  padding: 24px 24px 16px 24px;
`;
export const PaymentContainer = styled.View`
  margin-top: 20px;
  min-height: 80px;
  background-color: ${({ theme }) => theme.bpCore.colors.appBackground.primary};
`;
export const BottomViewContainer = styled.View`
  justify-content: center;
`;
export const PerkContainer = styled.View`
  flex-direction: row;
  margin-left: 20px;
`;
export const PerkTitle = styled.Text`
  font-size: 14px;
  font-weight: bold;
  line-height: 28px;
  letter-spacing: 0.1px;
`;
export const PerkSubtitle = styled.Text`
  font-size: 13px;
  line-height: 23px;
  letter-spacing: 0.2px;
`;
export const PerkTextWrapper = styled.View`
  margin-left: 5px;
  flex: 1;
`;
export const SectionWrapper = styled.View`
  margin: 15px 10px 0px 10px;
  gap: 16px;
`;
export const SectionHeader = styled.Text`
  margin: 17px 25px 10px 35px;
  font-size: 14px;
`;
export const MembershipDetails = styled.View`
  margin: 17px 25px 0px 35px;
  gap: 10px;
`;
export const MembershipDetailsRow = styled.View`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 12px;
`;
export const MembershipDetailsLabel = styled.Text<{
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
}>`
  flex: 0.4;
  font-family: ${({ fontFamily }) => fontFamily || 'Roboto-Light'};
  font-size: ${({ fontSize }) => fontSize || '13px'};
  ${({ fontWeight }) => (fontWeight ? `font-weight: ${fontWeight};` : '')};
  color: #111111;
`;
export const MembershipDetailsLabelWider = styled.Text<{
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
}>`
  flex: 0.7;
  font-family: ${({ fontFamily }) => fontFamily || 'Roboto-Light'};
  font-size: ${({ fontSize }) => fontSize || '13px'};
  ${({ fontWeight }) => (fontWeight ? `font-weight: ${fontWeight};` : '')};
  color: #111111;
`;
export const MembershipDetailsValueContainer = styled.View`
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
`;
export const MembershipDetailsValue = styled.Text`
  display: flex;
  flex: 0.6;
  font-weight: 700;
  color: #111111;
  font-family: Roboto-Medium;
  font-size: 13px;
  text-align: right;
  align-self: center;
  width: 100%;
`;
export const DetailsIndex = styled.Text`
  display: flex;
  font-weight: 700;
  color: #111111;
`;
export const MembershipExtraInfoWrapper = styled.View`
  display: flex;
  gap: 3px;
  margin: 17px 25px 0px 25px;
`;
export const MembershipExtraInfoItem = styled.View`
  display: flex;
  flex-direction: row;
  gap: 5px;
`;
export const MembershipExtraInfo = styled.Text`
  font-size: 12px;
  line-height: 21px;
  letter-spacing: 0.25px;
  font-weight: 300;
`;
export const AmexInfo = styled.Text`
  font-size: 12px;
  line-height: 21px;
  letter-spacing: 0.25px;
  margin: 0 25px;
`;
export const AmexHeader = styled.Text`
  margin: 25px 25px 10px 25px;
  font-size: 14px;
`;
export const Line = styled.View`
  margin-bottom: 5px;
  border: 0.6px solid #ededed;
`;
export const MidLine = styled.View`
  border: 0.8px solid #ededed;
  margin-bottom: 12px;
`;
