import { useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { useTheme } from 'styled-components/native';

import { SubsAnalyticsEventSubsMySubscriptionScreenOpen } from '../../analytics/events';
import { UserCountry } from '../../common/enums';
import { CarSideNormal, FootnoteDetail } from '../../components';
import { CancelSubscriptionWidget } from '../../components/Molecules/CancelSubscriptionWidget/CancelSubscriptionWidget';
import CustomLinkParagraphComponent from '../../components/Molecules/CustomLinkParagraphComponent/CustomLinkParagraphComponent';
import CancelSubscriptionPopup from '../../components/Organisms/CancelSubscriptionPopup/CancelSubscriptionPopup';
import {
  details,
  tariffs,
} from '../../components/Organisms/MembershipOnboard/MembershipOnboard.functions';
import { NetworkConnectionErrorPage } from '../../components/Organisms/NetworkConnectionErrorPage/NetworkConnectionErrorPage';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import {
  formatCurrency,
  formatDate,
  getCurrency,
  getDateWithSuffix,
} from '../../utils/helpers';
import * as S from './MyComfortMembership.styles';
const MyComfortMembership = () => {
  const {
    t,
    cardComponent,
    isInternetReachable,
    featureFlags,
    locale,
    tariffPricing,
    onAnalyticsEvent,
  } = useSettings();
  const [isPopupVisible, setIsPopupVisible] = useState(false);

  const theme = useTheme();

  const { userInfo } = useUserInfo();
  const navigation = useNavigation();

  useEffect(() => {
    if (!isInternetReachable) {
      navigation.setOptions({ headerShown: false });
    } else {
      navigation.setOptions({ headerShown: true });
    }
    onAnalyticsEvent(SubsAnalyticsEventSubsMySubscriptionScreenOpen({}));
  }, [onAnalyticsEvent, isInternetReachable, navigation]);

  const subscriptionAmount = formatCurrency({
    amount: featureFlags.subscriptionAmount,
    currency: getCurrency(userInfo?.country),
    locale,
  });

  const subTitle = t.myComfortMembership.subtitle.replace(
    '{{amount}}',
    subscriptionAmount,
  );

  const checkReturningUser = () =>
    userInfo?.membership?.some(
      (membership) => membership.membershipStatus === 'CANCELLED',
    ) && userInfo.type === 'SUBS-WALLET';
  if (!isInternetReachable) {
    return <NetworkConnectionErrorPage />;
  }
  return (
    <S.ScreenWrapper>
      <S.ScrollWrapper bounces={false}>
        <S.UpperViewContainer
          source={require('../../../assets/images/bgImage.png')}
          resizeMode="cover"
        >
          <S.TitleContainer>
            <S.FreeText type={'primary'}>
              {t.myComfortMembership.title.replace(
                '{{extraTariffDiscountPercentage}}',
                featureFlags.extraTariffDiscountPercentage.toString(),
              )}
            </S.FreeText>
            <S.Text>{subTitle}</S.Text>
          </S.TitleContainer>
        </S.UpperViewContainer>

        <S.SectionWrapper>
          <S.PerkContainer>
            <CarSideNormal />
            <S.PerkTextWrapper>
              <S.PerkTitle>
                {t.myComfortMembership.perks.title?.replace(
                  '{{subsDrivingDistancekm}}',
                  featureFlags.subsDrivingDistancekm,
                )}
              </S.PerkTitle>
              <S.PerkSubtitle>
                {t.myComfortMembership.perks.subtitle?.replace(
                  '{{subsChargekWh}}',
                  featureFlags.subsChargekWh,
                )}
              </S.PerkSubtitle>
            </S.PerkTextWrapper>
          </S.PerkContainer>
        </S.SectionWrapper>
        <S.SectionHeader>
          {t.myComfortMembership.membershipDetails.header}
        </S.SectionHeader>
        <S.Line />
        <S.MembershipDetails>
          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.myComfortMembership.membershipDetails.activationDate}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {formatDate(userInfo?.latestMembership?.membershipStartDate!)}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>
          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.myComfortMembership.membershipDetails.monthlyBaseFee}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {checkReturningUser()
                ? subscriptionAmount
                : t.myComfortMembership.membershipDetails.monthlyBaseFeeValue.replace(
                    '{{subscriptionAmount}}',
                    subscriptionAmount,
                  )}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>

          <S.Line />

          {tariffs(
            '',
            userInfo?.country || UserCountry.DE,
            locale,
            featureFlags['subsChargeRateDC>50kW'],
            featureFlags['subsChargeRateDC<50kW'],
            featureFlags['subsChargeRateAC<22kW'],
            t.myComfortMembership.table,
          ).map((tariff, index) => {
            console.log('tariff', tariff);
            return (
              index > 0 && (
                <>
                  <S.MembershipDetailsRow key={tariff.left.title}>
                    <S.MembershipDetailsLabelWider {...tariff.left.style}>
                      {tariff.left.title}
                    </S.MembershipDetailsLabelWider>
                    {tariff.right === '' ? (
                      tariff.right
                    ) : (
                      <S.MembershipDetailsValue>
                        {tariff.right}
                      </S.MembershipDetailsValue>
                    )}
                  </S.MembershipDetailsRow>
                  {(index === 0 || tariff.hasEndLine) && <S.MidLine />}
                </>
              )
            );
          })}

          <S.Line />

          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.myComfortMembership.membershipDetails.contractTerm}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {t.myComfortMembership.membershipDetails.contractTermValue}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>
          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.myComfortMembership.membershipDetails.paymentDueDate}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {t.myComfortMembership.membershipDetails.paymentDueDateValue.replace(
                '{{dueDate}}',
                getDateWithSuffix(
                  userInfo?.latestMembership?.membershipBillingCycleDate!,
                  locale,
                ),
              )}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>
          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.myComfortMembership.membershipDetails.cancellation}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {t.myComfortMembership.membershipDetails.cancellationValue}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>
        </S.MembershipDetails>
        <S.Line />
        {details(t).map(({ left, right }) => (
          <FootnoteDetail key={left} left={left} right={right} />
        ))}
        <FootnoteDetail
          left={t.membershipOnboard.smallText.key2}
          right={t.membershipOnboard.smallText.line2.replace(
            '{{extraTariffDiscountPercentage}}',
            featureFlags.extraTariffDiscountPercentage.toString(),
          )}
        />
        <FootnoteDetail
          left={t.membershipOnboard.smallText.key3}
          right={
            <>
              <CustomLinkParagraphComponent
                text={t.membershipOnboard.smallText.hyperlink}
                links={[
                  {
                    text: t.membershipOnboard.smallText.hyperlink_part_2,
                    url: tariffPricing,
                  },
                ]}
              />
            </>
          }
        />

        <FootnoteDetail right={t.membershipOnboard.smallText.line4} />
        <S.AmexHeader>
          {t.myComfortMembership.paymentDetails.header}
        </S.AmexHeader>
        <S.AmexInfo>
          <S.MembershipExtraInfo>
            {userInfo?.country === UserCountry.DE
              ? t.myComfortMembership.paymentDetails.descriptionExtraTariff
              : t.myComfortMembership.paymentDetails.description}
          </S.MembershipExtraInfo>
        </S.AmexInfo>
        <S.AmexHeader>
          {t.myComfortMembership.myComfortMembershipActivation.amex.header}
        </S.AmexHeader>
        <S.AmexInfo>
          {t.myComfortMembership.myComfortMembershipActivation.amex.description}
        </S.AmexInfo>
        {cardComponent && (
          <S.PaymentContainer>{cardComponent}</S.PaymentContainer>
        )}
        <CancelSubscriptionWidget
          theme={theme}
          disabled={!isInternetReachable}
          title={t.myComfortMembership.cancelMembership.title}
          onCancel={() => setIsPopupVisible(true)}
        />
        <CancelSubscriptionPopup
          isVisible={isPopupVisible}
          toggleModal={() => setIsPopupVisible(false)}
        />
      </S.ScrollWrapper>
    </S.ScreenWrapper>
  );
};

export default MyComfortMembership;
