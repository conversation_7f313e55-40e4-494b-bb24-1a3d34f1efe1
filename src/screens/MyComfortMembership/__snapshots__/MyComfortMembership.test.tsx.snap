// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MyComfortMembership should match snapshot 1`] = `
<View
  style={
    {
      "flex": 1,
    }
  }
>
  <View
    style={
      [
        {
          "backgroundColor": "rgb(242, 242, 242)",
          "flex": 1,
        },
        undefined,
      ]
    }
  >
    <View
      collapsable={false}
      pointerEvents="box-none"
      style={
        {
          "zIndex": 1,
        }
      }
    >
      <View
        accessibilityElementsHidden={false}
        importantForAccessibility="auto"
        onLayout={[Function]}
        pointerEvents="box-none"
        style={null}
      >
        <View
          collapsable={false}
          pointerEvents="box-none"
          style={
            {
              "bottom": 0,
              "left": 0,
              "opacity": 1,
              "position": "absolute",
              "right": 0,
              "top": 0,
              "zIndex": 0,
            }
          }
        >
          <View
            collapsable={false}
            style={
              {
                "backgroundColor": "rgb(255, 255, 255)",
                "borderBottomColor": "rgb(216, 216, 216)",
                "flex": 1,
                "shadowColor": "rgb(216, 216, 216)",
                "shadowOffset": {
                  "height": 0.5,
                  "width": 0,
                },
                "shadowOpacity": 0.85,
                "shadowRadius": 0,
              }
            }
          />
        </View>
        <View
          collapsable={false}
          pointerEvents="box-none"
          style={
            {
              "height": 44,
              "maxHeight": undefined,
              "minHeight": undefined,
              "opacity": undefined,
              "transform": undefined,
            }
          }
        >
          <View
            pointerEvents="none"
            style={
              {
                "height": 0,
              }
            }
          />
          <View
            pointerEvents="box-none"
            style={
              {
                "alignItems": "stretch",
                "flex": 1,
                "flexDirection": "row",
              }
            }
          >
            <View
              collapsable={false}
              pointerEvents="box-none"
              style={
                {
                  "alignItems": "flex-start",
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "justifyContent": "center",
                  "marginStart": 0,
                  "opacity": 1,
                }
              }
            />
            <View
              collapsable={false}
              pointerEvents="box-none"
              style={
                {
                  "justifyContent": "center",
                  "marginHorizontal": 16,
                  "maxWidth": 288,
                  "opacity": 1,
                }
              }
            >
              <Text
                accessibilityRole="header"
                aria-level="1"
                collapsable={false}
                numberOfLines={1}
                onLayout={[Function]}
                style={
                  {
                    "color": "rgb(28, 28, 30)",
                    "fontSize": 17,
                    "fontWeight": "600",
                  }
                }
              >
                MyComfortMembership
              </Text>
            </View>
            <View
              collapsable={false}
              pointerEvents="box-none"
              style={
                {
                  "alignItems": "flex-end",
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "justifyContent": "center",
                  "marginEnd": 0,
                  "opacity": 1,
                }
              }
            />
          </View>
        </View>
      </View>
    </View>
    <RNSScreenContainer
      onLayout={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <RNSScreen
        activityState={2}
        collapsable={false}
        gestureResponseDistance={
          {
            "bottom": -1,
            "end": -1,
            "start": -1,
            "top": -1,
          }
        }
        onGestureCancel={[Function]}
        pointerEvents="box-none"
        sheetAllowedDetents="large"
        sheetCornerRadius={-1}
        sheetExpandsWhenScrolledToEdge={true}
        sheetGrabberVisible={false}
        sheetLargestUndimmedDetent="all"
        style={
          {
            "bottom": 0,
            "left": 0,
            "position": "absolute",
            "right": 0,
            "top": 0,
          }
        }
      >
        <View
          collapsable={false}
          style={
            {
              "opacity": 1,
            }
          }
        />
        <View
          accessibilityElementsHidden={false}
          closing={false}
          gestureVelocityImpact={0.3}
          importantForAccessibility="auto"
          onClose={[Function]}
          onGestureBegin={[Function]}
          onGestureCanceled={[Function]}
          onGestureEnd={[Function]}
          onOpen={[Function]}
          onTransition={[Function]}
          pointerEvents="box-none"
          style={
            [
              {
                "display": "flex",
                "overflow": undefined,
              },
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              },
            ]
          }
          transitionSpec={
            {
              "close": {
                "animation": "spring",
                "config": {
                  "damping": 500,
                  "mass": 3,
                  "overshootClamping": true,
                  "restDisplacementThreshold": 10,
                  "restSpeedThreshold": 10,
                  "stiffness": 1000,
                },
              },
              "open": {
                "animation": "spring",
                "config": {
                  "damping": 500,
                  "mass": 3,
                  "overshootClamping": true,
                  "restDisplacementThreshold": 10,
                  "restSpeedThreshold": 10,
                  "stiffness": 1000,
                },
              },
            }
          }
        >
          <View
            collapsable={false}
            pointerEvents="box-none"
            style={
              {
                "flex": 1,
              }
            }
          >
            <View
              collapsable={false}
              handlerTag={1}
              handlerType="PanGestureHandler"
              needsOffscreenAlphaCompositing={false}
              onGestureHandlerEvent={[Function]}
              onGestureHandlerStateChange={[Function]}
              style={
                {
                  "flex": 1,
                  "transform": [
                    {
                      "translateX": 0,
                    },
                    {
                      "translateX": 0,
                    },
                  ],
                }
              }
            >
              <View
                pointerEvents="box-none"
                style={
                  [
                    {
                      "flex": 1,
                      "overflow": "hidden",
                    },
                    [
                      {
                        "backgroundColor": "rgb(242, 242, 242)",
                      },
                      undefined,
                    ],
                  ]
                }
              >
                <View
                  style={
                    {
                      "flex": 1,
                      "flexDirection": "column-reverse",
                    }
                  }
                >
                  <View
                    style={
                      {
                        "flex": 1,
                      }
                    }
                  >
                    <RCTSafeAreaView
                      style={
                        {
                          "flexBasis": 0,
                          "flexGrow": 1,
                          "flexShrink": 0,
                        }
                      }
                    >
                      <RCTScrollView
                        bounces={false}
                        style={{}}
                      >
                        <View>
                          <View
                            accessibilityIgnoresInvertColors={true}
                            style={
                              {
                                "justifyContent": "center",
                              }
                            }
                          >
                            <Image
                              resizeMode="cover"
                              source={
                                {
                                  "testUri": "../../../assets/images/bgImage.png",
                                }
                              }
                              style={
                                [
                                  {
                                    "bottom": 0,
                                    "left": 0,
                                    "position": "absolute",
                                    "right": 0,
                                    "top": 0,
                                  },
                                  {
                                    "height": undefined,
                                    "width": undefined,
                                  },
                                  undefined,
                                ]
                              }
                            />
                            <View
                              style={
                                {
                                  "alignItems": "center",
                                  "justifyContent": "space-between",
                                  "paddingBottom": 10,
                                  "paddingLeft": 24,
                                  "paddingRight": 24,
                                  "paddingTop": 10,
                                  "width": "100%",
                                }
                              }
                            >
                              <Text
                                style={
                                  {
                                    "color": "#FFFFFF",
                                    "fontSize": 40,
                                    "fontWeight": "bold",
                                    "letterSpacing": 0.4,
                                    "lineHeight": 47,
                                    "maxWidth": 350,
                                    "textAlign": "center",
                                  }
                                }
                                type="primary"
                              >
                                Currently saving at least 10% compared to Classic Tariff
                              </Text>
                              <Text
                                style={
                                  {
                                    "color": "#FFFFFF",
                                    "fontSize": 20,
                                    "fontWeight": "300",
                                    "lineHeight": 39,
                                    "marginBottom": 16,
                                    "textAlign": "center",
                                  }
                                }
                              >
                                Then just €7.49 per month
                              </Text>
                            </View>
                          </View>
                          <View
                            style={
                              {
                                "gap": 16,
                                "marginBottom": 0,
                                "marginLeft": 10,
                                "marginRight": 10,
                                "marginTop": 15,
                              }
                            }
                          >
                            <View
                              style={
                                {
                                  "flexDirection": "row",
                                  "marginLeft": 20,
                                }
                              }
                            >
                              <RNSVGSvgView
                                align="xMidYMid"
                                bbHeight="24"
                                bbWidth="24"
                                focusable={false}
                                height="24"
                                meetOrSlice={0}
                                minX={0}
                                minY={0}
                                style={
                                  [
                                    {
                                      "backgroundColor": "transparent",
                                      "borderWidth": 0,
                                    },
                                    {
                                      "flex": 0,
                                      "height": 24,
                                      "width": 24,
                                    },
                                  ]
                                }
                                vbHeight={24}
                                vbWidth={24}
                                width="24"
                              >
                                <RNSVGGroup
                                  fill={
                                    {
                                      "payload": 4278190080,
                                      "type": 0,
                                    }
                                  }
                                >
                                  <RNSVGGroup
                                    fill={null}
                                    propList={
                                      [
                                        "fill",
                                      ]
                                    }
                                  >
                                    <RNSVGPath
                                      d="M0 0h24v24H0z"
                                      fill={null}
                                      propList={
                                        [
                                          "fill",
                                        ]
                                      }
                                    />
                                    <RNSVGPath
                                      d="M14.777 15.076H9.05m7.51 1.348a1.856 1.856 0 1 1 0-3.712 1.856 1.856 0 0 1 0 3.712zm-9.302 0a1.856 1.856 0 1 1 0-3.712 1.856 1.856 0 0 1 0 3.712zM5.43 14.883l-1.429-.829v-3.197l.8-1.875-.4-.937c2.08-.245 4.144-.469 5.772-.469 2.582 0 4.739 1.132 6.626 2.813 2.628.528 3.2 1.814 3.2 2.343v2.344h-1.655"
                                      fill={
                                        {
                                          "payload": 4278190080,
                                          "type": 0,
                                        }
                                      }
                                      propList={
                                        [
                                          "stroke",
                                        ]
                                      }
                                      stroke={
                                        {
                                          "payload": 4278190230,
                                          "type": 0,
                                        }
                                      }
                                    />
                                  </RNSVGGroup>
                                </RNSVGGroup>
                              </RNSVGSvgView>
                              <View
                                style={
                                  {
                                    "flexBasis": 0,
                                    "flexGrow": 1,
                                    "flexShrink": 1,
                                    "marginLeft": 5,
                                  }
                                }
                              >
                                <Text
                                  style={
                                    {
                                      "fontSize": 14,
                                      "fontWeight": "bold",
                                      "letterSpacing": 0.1,
                                      "lineHeight": 28,
                                    }
                                  }
                                >
                                  Ideal if you drive undefined+ km a month ¹
                                </Text>
                                <Text
                                  style={
                                    {
                                      "fontSize": 13,
                                      "letterSpacing": 0.2,
                                      "lineHeight": 23,
                                    }
                                  }
                                >
                                  Charge just undefined kWh and your savings cover the monthly fee – more charging means more savings!
                                </Text>
                              </View>
                            </View>
                          </View>
                          <Text
                            style={
                              {
                                "fontSize": 14,
                                "marginBottom": 10,
                                "marginLeft": 35,
                                "marginRight": 25,
                                "marginTop": 17,
                              }
                            }
                          >
                            Your Extra Tariff
                          </Text>
                          <View
                            style={
                              {
                                "borderColor": "#ededed",
                                "borderStyle": "solid",
                                "borderWidth": 0.6,
                                "marginBottom": 5,
                              }
                            }
                          />
                          <View
                            style={
                              {
                                "gap": 10,
                                "marginBottom": 0,
                                "marginLeft": 35,
                                "marginRight": 25,
                                "marginTop": 17,
                              }
                            }
                          >
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexDirection": "row",
                                  "justifyContent": "space-between",
                                  "marginBottom": 12,
                                }
                              }
                            >
                              <Text
                                style={
                                  {
                                    "color": "#111111",
                                    "flexBasis": 0,
                                    "flexGrow": 0.4,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Light",
                                    "fontSize": 13,
                                  }
                                }
                              >
                                Activation date
                              </Text>
                              <Text
                                style={
                                  {
                                    "alignSelf": "center",
                                    "color": "#111111",
                                    "display": "flex",
                                    "flexBasis": 0,
                                    "flexGrow": 0.6,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Medium",
                                    "fontSize": 13,
                                    "fontWeight": "700",
                                    "textAlign": "right",
                                    "width": "100%",
                                  }
                                }
                              >
                                17/01/2024
                              </Text>
                            </View>
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexDirection": "row",
                                  "justifyContent": "space-between",
                                  "marginBottom": 12,
                                }
                              }
                            >
                              <Text
                                style={
                                  {
                                    "color": "#111111",
                                    "flexBasis": 0,
                                    "flexGrow": 0.4,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Light",
                                    "fontSize": 13,
                                  }
                                }
                              >
                                Monthly base fee
                              </Text>
                              <Text
                                style={
                                  {
                                    "alignSelf": "center",
                                    "color": "#111111",
                                    "display": "flex",
                                    "flexBasis": 0,
                                    "flexGrow": 0.6,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Medium",
                                    "fontSize": 13,
                                    "fontWeight": "700",
                                    "textAlign": "right",
                                    "width": "100%",
                                  }
                                }
                              >
                                Free trial month, then €7.49
                              </Text>
                            </View>
                            <View
                              style={
                                {
                                  "borderColor": "#ededed",
                                  "borderStyle": "solid",
                                  "borderWidth": 0.6,
                                  "marginBottom": 5,
                                }
                              }
                            />
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexDirection": "row",
                                  "justifyContent": "space-between",
                                  "marginBottom": 12,
                                }
                              }
                            >
                              <Text
                                fontFamily="Roboto-Medium"
                                fontSize="13px"
                                style={
                                  {
                                    "color": "#111111",
                                    "flexBasis": 0,
                                    "flexGrow": 0.7,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Medium",
                                    "fontSize": 13,
                                  }
                                }
                              >
                                Current charging rates in Extra Tariff at Aral pulse in Germany ² ³
                              </Text>
                            </View>
                            <View
                              style={
                                {
                                  "borderColor": "#ededed",
                                  "borderStyle": "solid",
                                  "borderWidth": 0.8,
                                  "marginBottom": 12,
                                }
                              }
                            />
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexDirection": "row",
                                  "justifyContent": "space-between",
                                  "marginBottom": 12,
                                }
                              }
                            >
                              <Text
                                style={
                                  {
                                    "color": "#111111",
                                    "flexBasis": 0,
                                    "flexGrow": 0.7,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Light",
                                    "fontSize": 13,
                                  }
                                }
                              >
                                DC (more than 50kW)
                              </Text>
                              <Text
                                style={
                                  {
                                    "alignSelf": "center",
                                    "color": "#111111",
                                    "display": "flex",
                                    "flexBasis": 0,
                                    "flexGrow": 0.6,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Medium",
                                    "fontSize": 13,
                                    "fontWeight": "700",
                                    "textAlign": "right",
                                    "width": "100%",
                                  }
                                }
                              >
                                €NaN/kWh
                              </Text>
                            </View>
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexDirection": "row",
                                  "justifyContent": "space-between",
                                  "marginBottom": 12,
                                }
                              }
                            >
                              <Text
                                style={
                                  {
                                    "color": "#111111",
                                    "flexBasis": 0,
                                    "flexGrow": 0.7,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Light",
                                    "fontSize": 13,
                                  }
                                }
                              >
                                DC (50kW or less)
                              </Text>
                              <Text
                                style={
                                  {
                                    "alignSelf": "center",
                                    "color": "#111111",
                                    "display": "flex",
                                    "flexBasis": 0,
                                    "flexGrow": 0.6,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Medium",
                                    "fontSize": 13,
                                    "fontWeight": "700",
                                    "textAlign": "right",
                                    "width": "100%",
                                  }
                                }
                              >
                                €NaN/kWh
                              </Text>
                            </View>
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexDirection": "row",
                                  "justifyContent": "space-between",
                                  "marginBottom": 12,
                                }
                              }
                            >
                              <Text
                                style={
                                  {
                                    "color": "#111111",
                                    "flexBasis": 0,
                                    "flexGrow": 0.7,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Light",
                                    "fontSize": 13,
                                  }
                                }
                              >
                                AC (22kW or less)
                              </Text>
                              <Text
                                style={
                                  {
                                    "alignSelf": "center",
                                    "color": "#111111",
                                    "display": "flex",
                                    "flexBasis": 0,
                                    "flexGrow": 0.6,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Medium",
                                    "fontSize": 13,
                                    "fontWeight": "700",
                                    "textAlign": "right",
                                    "width": "100%",
                                  }
                                }
                              >
                                €NaN/kWh
                              </Text>
                            </View>
                            <View
                              style={
                                {
                                  "borderColor": "#ededed",
                                  "borderStyle": "solid",
                                  "borderWidth": 0.6,
                                  "marginBottom": 5,
                                }
                              }
                            />
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexDirection": "row",
                                  "justifyContent": "space-between",
                                  "marginBottom": 12,
                                }
                              }
                            >
                              <Text
                                style={
                                  {
                                    "color": "#111111",
                                    "flexBasis": 0,
                                    "flexGrow": 0.4,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Light",
                                    "fontSize": 13,
                                  }
                                }
                              >
                                Contract term
                              </Text>
                              <Text
                                style={
                                  {
                                    "alignSelf": "center",
                                    "color": "#111111",
                                    "display": "flex",
                                    "flexBasis": 0,
                                    "flexGrow": 0.6,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Medium",
                                    "fontSize": 13,
                                    "fontWeight": "700",
                                    "textAlign": "right",
                                    "width": "100%",
                                  }
                                }
                              >
                                1 month, automatically extended
                              </Text>
                            </View>
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexDirection": "row",
                                  "justifyContent": "space-between",
                                  "marginBottom": 12,
                                }
                              }
                            >
                              <Text
                                style={
                                  {
                                    "color": "#111111",
                                    "flexBasis": 0,
                                    "flexGrow": 0.4,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Light",
                                    "fontSize": 13,
                                  }
                                }
                              >
                                Payment due date
                              </Text>
                              <Text
                                style={
                                  {
                                    "alignSelf": "center",
                                    "color": "#111111",
                                    "display": "flex",
                                    "flexBasis": 0,
                                    "flexGrow": 0.6,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Medium",
                                    "fontSize": 13,
                                    "fontWeight": "700",
                                    "textAlign": "right",
                                    "width": "100%",
                                  }
                                }
                              >
                                Monthly on 16th of each month
                              </Text>
                            </View>
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexDirection": "row",
                                  "justifyContent": "space-between",
                                  "marginBottom": 12,
                                }
                              }
                            >
                              <Text
                                style={
                                  {
                                    "color": "#111111",
                                    "flexBasis": 0,
                                    "flexGrow": 0.4,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Light",
                                    "fontSize": 13,
                                  }
                                }
                              >
                                Cancellation
                              </Text>
                              <Text
                                style={
                                  {
                                    "alignSelf": "center",
                                    "color": "#111111",
                                    "display": "flex",
                                    "flexBasis": 0,
                                    "flexGrow": 0.6,
                                    "flexShrink": 1,
                                    "fontFamily": "Roboto-Medium",
                                    "fontSize": 13,
                                    "fontWeight": "700",
                                    "textAlign": "right",
                                    "width": "100%",
                                  }
                                }
                              >
                                Cancel any time before your billing date
                              </Text>
                            </View>
                          </View>
                          <View
                            style={
                              {
                                "borderColor": "#ededed",
                                "borderStyle": "solid",
                                "borderWidth": 0.6,
                                "marginBottom": 5,
                              }
                            }
                          />
                          <View
                            style={
                              {
                                "flexDirection": "row",
                                "marginBottom": 2,
                                "marginLeft": 24,
                                "marginRight": 24,
                                "marginTop": 2,
                              }
                            }
                          >
                            <Text
                              style={
                                {
                                  "color": "rgb(17, 17, 17)",
                                  "fontFamily": "Roboto-Light",
                                  "fontSize": 11,
                                  "letterSpacing": 0.25,
                                  "lineHeight": 21,
                                  "marginBottom": 2,
                                  "marginLeft": 2,
                                  "marginRight": 2,
                                  "marginTop": 2,
                                }
                              }
                            >
                              ¹
                            </Text>
                            <Text
                              accessibilityHint=""
                              accessibilityRole="text"
                              accessible={true}
                              importantForAccessibility="yes"
                              style={
                                {
                                  "color": "rgb(17, 17, 17)",
                                  "fontFamily": "Roboto-Light",
                                  "fontSize": 11,
                                  "letterSpacing": 0.25,
                                  "lineHeight": 21,
                                  "marginBottom": 2,
                                  "marginLeft": 2,
                                  "marginRight": 2,
                                  "marginTop": 2,
                                }
                              }
                            >
                              Based on an average consumption of 20kWh/100km and charging at DC (more than 50kW) Aral pulse chargers in Germany compared to Classic Tariff.
                            </Text>
                          </View>
                          <View
                            style={
                              {
                                "flexDirection": "row",
                                "marginBottom": 2,
                                "marginLeft": 24,
                                "marginRight": 24,
                                "marginTop": 2,
                              }
                            }
                          >
                            <Text
                              style={
                                {
                                  "color": "rgb(17, 17, 17)",
                                  "fontFamily": "Roboto-Light",
                                  "fontSize": 11,
                                  "letterSpacing": 0.25,
                                  "lineHeight": 21,
                                  "marginBottom": 2,
                                  "marginLeft": 2,
                                  "marginRight": 2,
                                  "marginTop": 2,
                                }
                              }
                            >
                              ²
                            </Text>
                            <Text
                              accessibilityHint=""
                              accessibilityRole="text"
                              accessible={true}
                              importantForAccessibility="yes"
                              style={
                                {
                                  "color": "rgb(17, 17, 17)",
                                  "fontFamily": "Roboto-Light",
                                  "fontSize": 11,
                                  "letterSpacing": 0.25,
                                  "lineHeight": 21,
                                  "marginBottom": 2,
                                  "marginLeft": 2,
                                  "marginRight": 2,
                                  "marginTop": 2,
                                }
                              }
                            >
                              Your energy price is always at least 10% cheaper than our Classic Tariff.
                            </Text>
                          </View>
                          <View
                            style={
                              {
                                "flexDirection": "row",
                                "marginBottom": 2,
                                "marginLeft": 24,
                                "marginRight": 24,
                                "marginTop": 2,
                              }
                            }
                          >
                            <Text
                              style={
                                {
                                  "color": "rgb(17, 17, 17)",
                                  "fontFamily": "Roboto-Light",
                                  "fontSize": 11,
                                  "letterSpacing": 0.25,
                                  "lineHeight": 21,
                                  "marginBottom": 2,
                                  "marginLeft": 2,
                                  "marginRight": 2,
                                  "marginTop": 2,
                                }
                              }
                            >
                              ³
                            </Text>
                            <Text
                              accessibilityHint=""
                              accessibilityRole="text"
                              accessible={true}
                              importantForAccessibility="yes"
                              style={
                                {
                                  "color": "rgb(17, 17, 17)",
                                  "fontFamily": "Roboto-Light",
                                  "fontSize": 11,
                                  "letterSpacing": 0.25,
                                  "lineHeight": 21,
                                  "marginBottom": 2,
                                  "marginLeft": 2,
                                  "marginRight": 2,
                                  "marginTop": 2,
                                }
                              }
                            >
                              Different conditions apply abroad and at third-party providers. 
                              <Text
                                accessibilityLabel=" Get more information about all tariffs"
                                accessibilityRole="button"
                                onPress={[Function]}
                                style={
                                  {
                                    "alignItems": "flex-start",
                                    "color": "#000096",
                                    "fontSize": 12,
                                    "textAlign": "center",
                                    "textDecorationColor": "black",
                                    "textDecorationLine": "underline",
                                    "textDecorationStyle": "solid",
                                  }
                                }
                              >
                                 Get more information about all tariffs
                              </Text>
                               on our website
                            </Text>
                          </View>
                          <View
                            style={
                              {
                                "flexDirection": "row",
                                "marginBottom": 2,
                                "marginLeft": 24,
                                "marginRight": 24,
                                "marginTop": 2,
                              }
                            }
                          >
                            <Text
                              accessibilityHint=""
                              accessibilityRole="text"
                              accessible={true}
                              importantForAccessibility="yes"
                              style={
                                {
                                  "color": "rgb(17, 17, 17)",
                                  "fontFamily": "Roboto-Light",
                                  "fontSize": 11,
                                  "letterSpacing": 0.25,
                                  "lineHeight": 21,
                                  "marginBottom": 2,
                                  "marginLeft": 2,
                                  "marginRight": 2,
                                  "marginTop": 2,
                                }
                              }
                            >
                              Prices include VAT. The Extra Tariff cannot be combined with other special tariffs (e.g. ADAC e-Charge Tariff).
                            </Text>
                          </View>
                          <Text
                            style={
                              {
                                "fontSize": 14,
                                "marginBottom": 10,
                                "marginLeft": 25,
                                "marginRight": 25,
                                "marginTop": 25,
                              }
                            }
                          >
                            Linked payment card
                          </Text>
                          <Text
                            style={
                              {
                                "fontSize": 12,
                                "letterSpacing": 0.25,
                                "lineHeight": 21,
                                "marginBottom": 0,
                                "marginLeft": 25,
                                "marginRight": 25,
                                "marginTop": 0,
                              }
                            }
                          >
                            <Text
                              style={
                                {
                                  "fontSize": 12,
                                  "fontWeight": "300",
                                  "letterSpacing": 0.25,
                                  "lineHeight": 21,
                                }
                              }
                            >
                              This card will collect payments for your monthly fee and can be changed anytime
                            </Text>
                          </Text>
                          <Text
                            style={
                              {
                                "fontSize": 14,
                                "marginBottom": 10,
                                "marginLeft": 25,
                                "marginRight": 25,
                                "marginTop": 25,
                              }
                            }
                          >
                            Amex cards
                          </Text>
                          <Text
                            style={
                              {
                                "fontSize": 12,
                                "letterSpacing": 0.25,
                                "lineHeight": 21,
                                "marginBottom": 0,
                                "marginLeft": 25,
                                "marginRight": 25,
                                "marginTop": 0,
                              }
                            }
                          >
                            Amex cards are not supported for Extra Tariff payments
                          </Text>
                          <View
                            accessibilityHint="Cancel your Extra Tariff"
                            accessibilityLabel="Cancel your Extra Tariff"
                            accessibilityState={
                              {
                                "busy": undefined,
                                "checked": undefined,
                                "disabled": false,
                                "expanded": undefined,
                                "selected": undefined,
                              }
                            }
                            accessibilityValue={
                              {
                                "max": undefined,
                                "min": undefined,
                                "now": undefined,
                                "text": undefined,
                              }
                            }
                            accessible={true}
                            collapsable={false}
                            focusable={true}
                            onBlur={[Function]}
                            onClick={[Function]}
                            onFocus={[Function]}
                            onResponderGrant={[Function]}
                            onResponderMove={[Function]}
                            onResponderRelease={[Function]}
                            onResponderTerminate={[Function]}
                            onResponderTerminationRequest={[Function]}
                            onStartShouldSetResponder={[Function]}
                            style={
                              {
                                "alignItems": "center",
                                "borderColor": "#dedede",
                                "borderLeftWidth": 0,
                                "borderRightWidth": 0,
                                "borderStyle": "solid",
                                "borderTopWidth": 0,
                                "borderWidth": 1,
                                "display": "flex",
                                "flexDirection": "row",
                                "paddingBottom": 25,
                                "paddingLeft": 24,
                                "paddingRight": 24,
                                "paddingTop": 25,
                              }
                            }
                            testID="cancelWidget"
                          >
                            <RNSVGSvgView
                              align="xMidYMid"
                              bbHeight="24"
                              bbWidth="24"
                              focusable={false}
                              height="24"
                              meetOrSlice={0}
                              minX={0}
                              minY={0}
                              style={
                                [
                                  {
                                    "backgroundColor": "transparent",
                                    "borderWidth": 0,
                                  },
                                  {
                                    "flex": 0,
                                    "height": 24,
                                    "width": 24,
                                  },
                                ]
                              }
                              testID="rightArrow"
                              vbHeight={24}
                              vbWidth={24}
                              width="24"
                            >
                              <RNSVGGroup
                                fill={
                                  {
                                    "payload": 4278190080,
                                    "type": 0,
                                  }
                                }
                              >
                                <RNSVGGroup
                                  fill={null}
                                  propList={
                                    [
                                      "fill",
                                    ]
                                  }
                                >
                                  <RNSVGPath
                                    d="M0 0h24v24H0z"
                                    fill={null}
                                    propList={
                                      [
                                        "fill",
                                      ]
                                    }
                                  />
                                  <RNSVGPath
                                    d="M15 7.38c1.8 1.02 3 3 3 5.22 0 3.3-2.7 6-6 6s-6-2.7-6-6c0-2.22 1.2-4.14 3-5.22m3-1.98v4.2"
                                    fill={
                                      {
                                        "payload": 4278190080,
                                        "type": 0,
                                      }
                                    }
                                    propList={
                                      [
                                        "stroke",
                                      ]
                                    }
                                    stroke={
                                      {
                                        "payload": 4293282121,
                                        "type": 0,
                                      }
                                    }
                                  />
                                </RNSVGGroup>
                              </RNSVGGroup>
                            </RNSVGSvgView>
                            <Text
                              style={
                                {
                                  "color": "#e64949",
                                  "fontSize": 16,
                                  "fontWeight": "400",
                                  "lineHeight": 28,
                                  "marginLeft": 16,
                                }
                              }
                            >
                              Cancel your Extra Tariff
                            </Text>
                            <View
                              style={
                                {
                                  "display": "flex",
                                  "flexBasis": 0,
                                  "flexDirection": "row",
                                  "flexGrow": 1,
                                  "flexShrink": 1,
                                  "justifyContent": "flex-end",
                                }
                              }
                            >
                              <RNSVGSvgView
                                align="xMidYMid"
                                bbHeight="24"
                                bbWidth="24"
                                focusable={false}
                                height="24"
                                meetOrSlice={0}
                                minX={0}
                                minY={0}
                                style={
                                  [
                                    {
                                      "backgroundColor": "transparent",
                                      "borderWidth": 0,
                                    },
                                    {
                                      "flex": 0,
                                      "height": 24,
                                      "width": 24,
                                    },
                                  ]
                                }
                                testID="rightArrow"
                                vbHeight={24}
                                vbWidth={24}
                                width="24"
                              >
                                <RNSVGGroup
                                  fill={
                                    {
                                      "payload": 4278190080,
                                      "type": 0,
                                    }
                                  }
                                >
                                  <RNSVGGroup
                                    fill={null}
                                    propList={
                                      [
                                        "fill",
                                      ]
                                    }
                                  >
                                    <RNSVGPath
                                      d="M0 0h24v24H0z"
                                      fill={
                                        {
                                          "payload": 4278190080,
                                          "type": 0,
                                        }
                                      }
                                    />
                                    <RNSVGPath
                                      d="M8.646 18.354a.5.5 0 0 1-.057-.638l.057-.07L14.293 12 8.646 6.354a.5.5 0 0 1-.057-.638l.057-.07a.5.5 0 0 1 .638-.057l.07.057 6 6a.5.5 0 0 1 .057.638l-.057.07-6 6a.5.5 0 0 1-.708 0z"
                                      fill={
                                        {
                                          "payload": 4293282121,
                                          "type": 0,
                                        }
                                      }
                                      propList={
                                        [
                                          "fill",
                                        ]
                                      }
                                    />
                                  </RNSVGGroup>
                                </RNSVGGroup>
                              </RNSVGSvgView>
                            </View>
                          </View>
                          <Modal
                            animationType="fade"
                            hardwareAccelerated={false}
                            onRequestClose={[Function]}
                            style={
                              {
                                "flexBasis": 0,
                                "flexGrow": 1,
                                "flexShrink": 0,
                                "width": "100%",
                              }
                            }
                            transparent={true}
                            visible={false}
                          />
                        </View>
                      </RCTScrollView>
                    </RCTSafeAreaView>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>
      </RNSScreen>
    </RNSScreenContainer>
  </View>
</View>
`;
