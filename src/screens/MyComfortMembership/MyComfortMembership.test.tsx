import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { render } from '@testing-library/react-native';
import React from 'react';

import {
  MandateStatus,
  MembershipStatus,
  UserCountry,
  UserTypeEnum,
} from '../../common/enums';
import Theme from '../../themes/Theme';
import TRANSLATIONS from '../../translations/messages.json';
import { updateTranslations } from '../../utils/updateTranslations';
import MyComfortMembership from './MyComfortMembership';

// Mock modules
jest.mock('../../providers/WalletSubscriptionDataProvider');
jest.mock('../../providers/Settings');
jest.mock('../../providers/HostNavigationProvider');
jest.mock('../../providers/UserInfoProvider');

describe('MyComfortMembership', () => {
  beforeAll(() => {
    // Setup mock implementations
    const mockSettings = jest.requireMock('../../providers/Settings');
    mockSettings.useSettings = () => ({
      t: updateTranslations(TRANSLATIONS, { brand: 'aral' }),
      isInternetReachable: true,
      featureFlags: {
        subsChargingRatesPromo: 0.44,
        introPercentageDiscount: 20,
        subscriptionAmount: 7.49,
        extraTariffDiscountPercentage: 10,
      },
      onAnalyticsEvent: jest.fn(),
    });

    const mockWallet = jest.requireMock(
      '../../providers/WalletSubscriptionDataProvider',
    );
    mockWallet.WalletSubscriptionData = () => ({
      walletSubscriptionData: { id: 'mock-id', status: 'active' },
      subscription: {
        billingItemId: '651dce90-2497-4a13-9503-e16cccbb375b',
        planId: 'ac7e03e1-0da2-4515-8412-7ec556f32b8d',
        name: 'Plan A - September 14',
        amount: '0',
        currency: 'EUR',
      },
      totalValue: 7.99,
      currency: 'EUR',
    });

    const mockNavigation = jest.requireMock(
      '../../providers/HostNavigationProvider',
    );
    mockNavigation.useHostNavigation = () => ({
      navigate: jest.fn(),
      navigation: { goBack: jest.fn() },
    });

    const mockUserInfo = jest.requireMock('../../providers/UserInfoProvider');
    mockUserInfo.useUserInfo = () => ({
      userInfo: {
        userId: 'user-id',
        type: 'user-type',
        status: '',
        gocardless: {
          mandateStatus: MandateStatus.ACTIVE,
          mandateId: '',
        },
        tagIds: [],
        balance: 0,
        country: UserCountry.DE,
        membership: [
          {
            userType: UserTypeEnum.SUBS_WALLET,
            membershipStatus: MembershipStatus.ACTIVE,
            membershipRequestCancelDate: null,
            membershipStartDate: '2024-01-17T00:00:00.000Z',
            membershipEndDate: '',
            membershipBillingCycleDate: '2024-11-16T00:00:00.000Z',
          },
        ],
        latestMembership: {
          userType: UserTypeEnum.SUBS_WALLET,
          membershipStatus: MembershipStatus.ACTIVE,
          membershipRequestCancelDate: null,
          membershipStartDate: '2024-01-17T00:00:00.000Z',
          membershipEndDate: '',
          membershipBillingCycleDate: '2024-11-16T00:00:00.000Z',
        },
      },
    });
  });
  const Stack = createStackNavigator();
  const renderComponent = () =>
    render(
      <Theme>
        <NavigationContainer>
          <Stack.Navigator>
            <Stack.Screen
              name="MyComfortMembership"
              component={MyComfortMembership}
            />
          </Stack.Navigator>
        </NavigationContainer>
      </Theme>,
    );

  // Verify that the component renders correctly and matches the stored snapshot
  it('should match snapshot', () => {
    const { toJSON } = renderComponent();
    expect(toJSON()).toMatchSnapshot();
  });

  // Verify that the cancel subscription button is present in the rendered component
  it('should display cancel subscription button', () => {
    const { getByText } = renderComponent();
    expect(
      getByText(TRANSLATIONS.myComfortMembership.cancelMembership.title),
    ).toBeTruthy();
  });

  // Verify that the contract term information is displayed correctly
  it('should display contract term', () => {
    const { getByText } = renderComponent();
    expect(
      getByText(
        TRANSLATIONS.myComfortMembership.membershipDetails.contractTermValue,
      ),
    ).toBeTruthy();
  });
});
