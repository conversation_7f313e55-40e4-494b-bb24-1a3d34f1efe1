import { render } from '@testing-library/react-native';
import React from 'react';

import {
  MandateStatus,
  MembershipStatus,
  UserCountry,
  UserTypeEnum,
} from '../../common/enums';
import { IMmembership, IUserInfo } from '../../common/interfaces';
import Theme from '../../themes/Theme';
import TRANSLATIONS from '../../translations/messages.json';
import { rawData } from '../../utils/mockData';
import { updateTranslations } from '../../utils/updateTranslations';
import MyComfortMembershipActivation from './MyComfortMembershipActivation';

// Mock settings
const mockTranslations = TRANSLATIONS;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'aral' }),
  isInternetReachable: true,
  featureFlags: {
    subsChargingRatesPromo: 0.44,
    introPercentageDiscount: 20,
    subscriptionAmount: '7,49€',
    extraTariffDiscountPercentage: 10,
  },
  onAnalyticsEvent: jest.fn(),
  requiredConsents: [
    {
      consentId: '1',
      acceptedTime: '99',
      accepted: false,
      consentType: 'Privacy Policy',
      version: '22',
      identityExternalId: '123',
      appName: 'Aral',
    },
  ],
  consents: [
    {
      consentId: '1',
      acceptedTime: '99',
      accepted: false,
      consentType: 'Privacy Policy',
      version: '22',
      identityExternalId: '123',
      appName: 'Aral',
    },
  ],
  emspTermsAndConditions: 'https://terms-and-conditions.com',
  tariffPricing:
    'https://www.aral.de/de/global/retail/pulse/tarife-bezahlmethoden.html',
  updateConsents: () => {},
};

// Mock membership data
const mockMembership: IMmembership = {
  userType: UserTypeEnum.SUBS_WALLET,
  membershipStatus: MembershipStatus.ACTIVE,
  membershipRequestCancelDate: null,
  membershipStartDate: '2024-01-17T00:00:00.000Z',
  membershipEndDate: '',
  membershipBillingCycleDate: '2024-11-16T00:00:00.000Z',
};

// Mock user info
const mockUserInfoObj: { userInfo: IUserInfo } = {
  userInfo: {
    userId: 'user-id',
    type: 'user-type',
    status: '',
    gocardless: {
      mandateStatus: MandateStatus.ACTIVE,
      mandateId: '',
    },
    tagIds: [],
    balance: 0,
    country: UserCountry.DE,
    membership: [mockMembership],
    latestMembership: mockMembership,
  },
};

// Mock wallet subscription
const mockWalletSubscriptionObj = {
  walletSubscriptionData: rawData,
  subscription: {
    billingItemId: '651dce90-2497-4a13-9503-e16cccbb375b',
    planId: 'ac7e03e1-0da2-4515-8412-7ec556f32b8d',
    name: 'Plan A - September 14',
    amount: '0',
    currency: 'EUR',
  },
  totalValue: 7.99,
  currency: 'EUR',
};

// Mock navigation
const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

// Mock providers
jest.mock('../../providers/WalletSubscriptionDataProvider', () => ({
  WalletSubscriptionData: () =>
    jest.fn().mockReturnValue(mockWalletSubscriptionObj)(),
}));

jest.mock('../../providers/Settings', () => ({
  useSettings: () => jest.fn().mockReturnValue(mockUseSettingsObj)(),
}));

jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => jest.fn().mockReturnValue(mockUserInfoObj)(),
}));

jest.mock('@apollo/client', () => ({
  ...jest.requireActual('@apollo/client'),
  useMutation: () =>
    jest
      .fn()
      .mockReturnValue([
        jest.fn(),
        { loading: false, error: undefined, data: undefined },
      ])(),
  useQuery: () => ({
    refetch: jest.fn(),
    loading: false,
    data: mockWalletSubscriptionObj.walletSubscriptionData,
  }),
}));

// Test component renderer
const renderWithTheme = () =>
  render(
    <Theme>
      <MyComfortMembershipActivation />
    </Theme>,
  );

describe('MySubscription rendering', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // mock the date to be fixed
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-01-23'));
  });

  afterEach(() => {
    // restore the date after each test
    jest.useRealTimers();
  });

  it('Should render correctly', () => {
    const result = renderWithTheme();
    expect(result.toJSON()).toMatchSnapshot();
  });

  it('Should contain contract term', async () => {
    const { getByText } = renderWithTheme();

    const contractTerm = getByText(
      mockTranslations.myComfortMembership.membershipDetails.contractTermValue,
    );
    expect(contractTerm).toBeDefined();
  });
});
