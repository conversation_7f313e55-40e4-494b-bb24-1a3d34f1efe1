// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MySubscription rendering Should render correctly 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 0,
    }
  }
>
  <RCTScrollView
    bounces={false}
    style={{}}
  >
    <View>
      <Text
        style={
          {
            "fontSize": 16,
            "marginBottom": 17,
            "marginLeft": 25,
            "marginRight": 25,
            "marginTop": 17,
          }
        }
      >
        Summary
      </Text>
      <View
        style={
          {
            "borderBottomColor": "#dedede",
            "borderBottomWidth": 1,
            "borderTopColor": "#dedede",
            "borderTopWidth": 1,
            "gap": 30,
            "paddingBottom": 20,
            "paddingLeft": 25,
            "paddingRight": 25,
            "paddingTop": 20,
          }
        }
      >
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 2,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
              }
            }
          >
            Activation date
          </Text>
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 3,
                "flexShrink": 1,
                "fontSize": 13,
                "fontWeight": "500",
                "textAlign": "right",
              }
            }
          >
            23/01/2024
          </Text>
        </View>
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 2,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
              }
            }
          >
            Monthly fee
          </Text>
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 3,
                "flexShrink": 1,
                "fontSize": 13,
                "fontWeight": "500",
                "textAlign": "right",
              }
            }
          >
            Free trial month, then €NaN
          </Text>
        </View>
        <View
          style={
            {
              "borderColor": "#ededed",
              "borderStyle": "solid",
              "borderWidth": 0.8,
              "width": "'90%'",
            }
          }
        />
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
            }
          }
        >
          <Text
            fontFamily="Roboto-Medium"
            fontSize="13px"
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 2,
                "flexShrink": 1,
                "fontFamily": "Roboto-Medium",
                "fontSize": 13,
              }
            }
          >
            Current charging rates in Extra Tariff at Aral pulse in Germany ¹ ²
          </Text>
        </View>
        <View
          style={
            {
              "borderColor": "#ededed",
              "borderStyle": "solid",
              "borderWidth": 0.8,
              "width": "'90%'",
            }
          }
        />
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 2,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
              }
            }
          >
            DC (more than 50kW)
          </Text>
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 3,
                "flexShrink": 1,
                "fontSize": 13,
                "fontWeight": "500",
                "textAlign": "right",
              }
            }
          >
            €NaN/kWh
          </Text>
        </View>
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 2,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
              }
            }
          >
            DC (50kW or less)
          </Text>
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 3,
                "flexShrink": 1,
                "fontSize": 13,
                "fontWeight": "500",
                "textAlign": "right",
              }
            }
          >
            €NaN/kWh
          </Text>
        </View>
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 2,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
              }
            }
          >
            AC (22kW or less)
          </Text>
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 3,
                "flexShrink": 1,
                "fontSize": 13,
                "fontWeight": "500",
                "textAlign": "right",
              }
            }
          >
            €NaN/kWh
          </Text>
        </View>
        <View
          style={
            {
              "borderColor": "#ededed",
              "borderStyle": "solid",
              "borderWidth": 0.8,
              "width": "'90%'",
            }
          }
        />
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 2,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
              }
            }
          >
            Contract term
          </Text>
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 3,
                "flexShrink": 1,
                "fontSize": 13,
                "fontWeight": "500",
                "textAlign": "right",
              }
            }
          >
            1 month, automatically extended
          </Text>
        </View>
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 2,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
              }
            }
          >
            Billing
          </Text>
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 3,
                "flexShrink": 1,
                "fontSize": 13,
                "fontWeight": "500",
                "textAlign": "right",
              }
            }
          >
            Monthly on 23rd of each month
          </Text>
        </View>
        <View
          style={
            {
              "display": "flex",
              "flexDirection": "row",
            }
          }
        >
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 2,
                "flexShrink": 1,
                "fontFamily": "Roboto-Light",
                "fontSize": 13,
              }
            }
          >
            Cancellation
          </Text>
          <Text
            style={
              {
                "color": "#111111",
                "display": "flex",
                "flexBasis": 0,
                "flexGrow": 3,
                "flexShrink": 1,
                "fontSize": 13,
                "fontWeight": "500",
                "textAlign": "right",
              }
            }
          >
            Cancel any time before your billing date
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "marginTop": 17,
          }
        }
      >
        <View
          style={
            {
              "flexDirection": "row",
              "marginBottom": 2,
              "marginLeft": 24,
              "marginRight": 24,
              "marginTop": 2,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            ¹
          </Text>
          <Text
            accessibilityHint=""
            accessibilityRole="text"
            accessible={true}
            importantForAccessibility="yes"
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            Your energy price is always at least 10% cheaper than our Classic Tariff.
          </Text>
        </View>
        <View
          style={
            {
              "flexDirection": "row",
              "marginBottom": 2,
              "marginLeft": 24,
              "marginRight": 24,
              "marginTop": 2,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            ²
          </Text>
          <Text
            accessibilityHint=""
            accessibilityRole="text"
            accessible={true}
            importantForAccessibility="yes"
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            Different conditions apply abroad and at third-party providers. 
            <Text
              accessibilityLabel=" Get more information about all tariffs"
              accessibilityRole="button"
              onPress={[Function]}
              style={
                {
                  "alignItems": "flex-start",
                  "color": "#000096",
                  "fontSize": 12,
                  "textAlign": "center",
                  "textDecorationColor": "black",
                  "textDecorationLine": "underline",
                  "textDecorationStyle": "solid",
                }
              }
            >
               Get more information about all tariffs
            </Text>
             on our website
          </Text>
        </View>
        <View
          style={
            {
              "height": 10,
              "width": 1,
            }
          }
        />
        <View
          style={
            {
              "flexDirection": "row",
              "marginBottom": 2,
              "marginLeft": 24,
              "marginRight": 24,
              "marginTop": 2,
            }
          }
        >
          <Text
            accessibilityHint=""
            accessibilityRole="text"
            accessible={true}
            importantForAccessibility="yes"
            style={
              {
                "color": "rgb(17, 17, 17)",
                "fontFamily": "Roboto-Light",
                "fontSize": 11,
                "letterSpacing": 0.25,
                "lineHeight": 21,
                "marginBottom": 2,
                "marginLeft": 2,
                "marginRight": 2,
                "marginTop": 2,
              }
            }
          >
            Prices include VAT. The Extra Tariff cannot be combined with other special tariffs (e.g. ADAC e-Charge Tariff).
          </Text>
        </View>
      </View>
      <Text
        style={
          {
            "fontSize": 16,
            "marginBottom": 10,
            "marginLeft": 25,
            "marginRight": 25,
            "marginTop": 50,
          }
        }
      >
        Linked payment card
      </Text>
      <Text
        style={
          {
            "fontSize": 12,
            "letterSpacing": 0.25,
            "lineHeight": 21,
            "marginBottom": 0,
            "marginLeft": 25,
            "marginRight": 25,
            "marginTop": 0,
          }
        }
      >
        This card will be used to collect payments for your monthly fee and can be changed anytime.
      </Text>
      <Text
        style={
          {
            "fontSize": 16,
            "marginBottom": 10,
            "marginLeft": 25,
            "marginRight": 25,
            "marginTop": 25,
          }
        }
      >
        Amex cards
      </Text>
      <Text
        style={
          {
            "fontSize": 12,
            "letterSpacing": 0.25,
            "lineHeight": 21,
            "marginBottom": 0,
            "marginLeft": 25,
            "marginRight": 25,
            "marginTop": 0,
          }
        }
      >
        Amex cards are not supported for Extra Tariff payments
      </Text>
      <View
        style={
          {
            "height": 80,
            "width": 1,
          }
        }
      />
      <View
        style={
          {
            "gap": 16,
            "marginBottom": 0,
            "marginLeft": 25,
            "marginRight": 25,
            "marginTop": 17,
          }
        }
      >
        <View
          checked={false}
          style={
            {
              "flexDirection": "column",
            }
          }
          testID="CheckBoxInput"
        >
          <View
            alignCenter={true}
            style={
              {
                "alignItems": "center",
                "flexBasis": "auto",
                "flexDirection": "row",
              }
            }
          >
            <View
              accessibilityLabel="I agree to the new terms and conditions"
              accessibilityRole="checkbox"
              accessibilityState={
                {
                  "busy": undefined,
                  "checked": undefined,
                  "disabled": undefined,
                  "expanded": undefined,
                  "selected": undefined,
                }
              }
              accessible={true}
              checked={false}
              focusable={true}
              noBorderRadius={true}
              onClick={[Function]}
              onResponderGrant={[Function]}
              onResponderMove={[Function]}
              onResponderRelease={[Function]}
              onResponderTerminate={[Function]}
              onResponderTerminationRequest={[Function]}
              onStartShouldSetResponder={[Function]}
              style={
                {
                  "backgroundColor": "#FFFFFF",
                  "borderBottomLeftRadius": 0,
                  "borderBottomRightRadius": 0,
                  "borderColor": "#DEDEDE",
                  "borderStyle": "solid",
                  "borderTopLeftRadius": 0,
                  "borderTopRightRadius": 0,
                  "borderWidth": 1,
                  "flexBasis": 24,
                  "height": 24,
                  "width": 24,
                }
              }
              testID="CheckBox"
              type="primary"
            />
            <View
              style={
                {
                  "flexShrink": 1,
                  "fontSize": 14,
                  "lineHeight": 20,
                  "marginBottom": "auto",
                  "marginLeft": 16,
                  "marginRight": 0,
                  "marginTop": "auto",
                }
              }
            >
              <Text
                onPress={[Function]}
                style={
                  {
                    "color": "#212121",
                    "fontSize": 13,
                    "fontWeight": "400",
                    "lineHeight": 23,
                    "textAlign": "left",
                  }
                }
              >
                I agree to the new 
                <Text
                  onPress={[Function]}
                  style={
                    {
                      "alignItems": "flex-start",
                      "color": "#000096",
                      "fontSize": 13,
                      "textAlign": "center",
                      "textDecorationColor": "black",
                      "textDecorationLine": "underline",
                      "textDecorationStyle": "solid",
                    }
                  }
                >
                  terms and conditions
                </Text>
              </Text>
            </View>
          </View>
        </View>
      </View>
      <View
        style={
          {
            "gap": 16,
            "marginBottom": 0,
            "marginLeft": 25,
            "marginRight": 25,
            "marginTop": 17,
          }
        }
      >
        <View
          style={
            {
              "marginBottom": 56,
              "marginTop": "auto",
            }
          }
        >
          <View
            accessibilityHint="You agree to sign up and pay for the extra tariff, this will activate your membership."
            accessibilityLabel="Book and pay"
            accessibilityRole="button"
            accessibilityState={
              {
                "busy": undefined,
                "checked": undefined,
                "disabled": true,
                "expanded": undefined,
                "selected": undefined,
              }
            }
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={true}
            collapsable={false}
            focusable={true}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            style={
              {
                "alignItems": "center",
                "backgroundColor": "#80b2e6",
                "borderBottomLeftRadius": 23,
                "borderBottomRightRadius": 23,
                "borderColor": "transparent",
                "borderStyle": "solid",
                "borderTopLeftRadius": 23,
                "borderTopRightRadius": 23,
                "borderWidth": 0,
                "justifyContent": "center",
                "minHeight": 46,
                "opacity": 1,
                "paddingHorizontal": 25.5,
                "paddingVertical": 12,
              }
            }
            testID="BookAndPayButton"
          >
            <View>
              <Text
                disabled={true}
                inverted={false}
                size="large"
                style={
                  {
                    "color": "#ffffff",
                    "fontFamily": "Roboto-Regular",
                    "fontSize": 15,
                    "letterSpacing": 0.7,
                    "textAlign": "center",
                  }
                }
                type="primary"
              >
                Book and pay
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  </RCTScrollView>
</RCTSafeAreaView>
`;
