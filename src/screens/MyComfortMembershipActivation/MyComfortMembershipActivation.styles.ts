import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
`;

export const ScrollWrapper = styled.ScrollView``;

export const DescriptionWrapper = styled.View`
  padding: 24px 24px 16px 24px;
`;

export const ActionButton = styled.View`
  margin-top: auto;
  margin-bottom: 56px;
`;

export const PaymentContainer = styled.View`
  margin-top: 20px;
  height: 80px;
  background-color: ${({ theme }) => theme.bpCore.colors.appBackground.primary};
`;

export const BottomViewContainer = styled.View`
  justify-content: center;
`;

export const PerkContainer = styled.View`
  flex-direction: row;
`;

export const PerkTitle = styled.Text`
  font-size: 16px;
  font-weight: bold;
  line-height: 28px;
  letter-spacing: 0.1px;
`;

export const TitleText = styled.Text`
  padding-left: 10px;
  text-align: left;
  font-size: 20px;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.banner};
  padding-bottom: 10px;
`;

export const PerkSubtitle = styled.Text`
  font-size: 13px;
  line-height: 23px;
  letter-spacing: 0.2px;
`;

export const PerkTextWrapper = styled.View`
  margin-left: 10px;
  flex: 1;
`;

export const SectionWrapper = styled.View`
  margin: 17px 25px 0px 25px;
  gap: 16px;
`;

export const SectionHeader = styled.Text`
  margin: 17px 25px 17px 25px;
  font-size: 16px;
`;

export const LinkedPaymentHeader = styled.Text`
  margin: 50px 25px 10px 25px;
  font-size: 16px;
`;

export const AmexHeader = styled.Text`
  margin: 25px 25px 10px 25px;
  font-size: 16px;
`;

export const MembershipDetails = styled.View`
  /* margin: 17px 25px 0px 25px; */
  padding: 20px 25px;
  border-top-color: #dedede;
  border-top-width: 1px;
  border-bottom-color: #dedede;
  border-bottom-width: 1px;
  gap: 30px;
`;

export const MembershipDetailsRow = styled.View`
  display: flex;
  flex-direction: row;
`;

export const MembershipDetailsLabel = styled.Text<{
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
}>`
  display: flex;
  font-size: 13px;
  font-family: ${({ fontFamily }) => fontFamily || 'Roboto-Light'};
  color: #111111;
  flex: 2;
`;

export const MembershipDetailsValue = styled.Text`
  display: flex;
  flex: 3;
  font-weight: 500;
  font-size: 13px;
  color: #111111;
  text-align: right;
`;

export const MembershipExtraInfoWrapper = styled.View`
  margin-top: 17px;
`;

export const MembershipExtraInfo = styled.Text`
  font-size: 12px;
  line-height: 21px;
  letter-spacing: 0.25px;
  font-weight: 300;
`;
export const LinkedPaymentCardInfo = styled.Text`
  font-size: 12px;
  line-height: 21px;
  letter-spacing: 0.25px;
  margin: 0 25px;
`;

export const AmexInfo = styled.Text`
  font-size: 12px;
  line-height: 21px;
  letter-spacing: 0.25px;
  margin: 0 25px;
`;

export const CheckboxContainer = styled.View`
  flex-direction: row;
  align-items: flex-start;
  gap: 8px;
`;

export const BottomTandC = styled.TouchableOpacity`
  flex-direction: row;
  justify-content: center;
  align-items: center;
`;

export const LinkText = styled.Text`
  text-align: center;
  font-size: 12px;
  align-items: flex-start;
  color: ${(p: any) => p.theme.subscriptionMfe.buttons.secondary.text};
`;

export const MidLine = styled.View`
  width: '90%';
  border: 0.8px solid #ededed;
`;
