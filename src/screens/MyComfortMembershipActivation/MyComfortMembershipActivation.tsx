import { useMutation, useQuery } from '@apollo/client';
import { IThreeDS, useWallet } from '@bp/bppay-wallet-feature';
import { Button, ButtonAction } from '@bp/ui-components/mobile/core';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Linking, useWindowDimensions } from 'react-native';
import { useTheme } from 'styled-components/native';

import {
  SubsAnalyticsEventSubsConfirmAndAuthoriseClick,
  SubsAnalyticsEventSubsConfirmSubscriptionOpen,
} from '../../analytics/events';
import {
  CREATE_WALLET_SUBSCRIPTION,
  GET_WALLET_SUBSCRIPTION,
} from '../../client/queries';
import {
  MembershipStatus,
  SubscriptionScreenNames,
  UserCountry,
  UserTypeEnum,
} from '../../common/enums';
import {
  ICreateWalletSubscriptionRequest,
  ICreateWalletSubscriptionResponse,
  IGetWalletSubscriptionResponse,
} from '../../common/interfaces';
import { FootnoteDetail, Spacer } from '../../components';
import CheckBoxInput from '../../components/Molecules/CheckBoxInput/CheckBoxInput';
import CustomLinkParagraphComponent from '../../components/Molecules/CustomLinkParagraphComponent/CustomLinkParagraphComponent';
import { Link } from '../../components/Molecules/LinkParagraphComponent/LinkParagraphComponent.style';
import { tariffs } from '../../components/Organisms/MembershipOnboard/MembershipOnboard.functions';
import { SmallText } from '../../components/Organisms/SubscriptionDetails/SubscriptionDetails.styles';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import {
  formatCurrency,
  formatDate,
  getCurrency,
  getDateWithSuffix,
  POLLING_INTERVAL,
} from '../../utils/helpers';
import * as S from './MyComfortMembershipActivation.styles';

const MyComfortMembershipActivation = () => {
  const {
    t,
    cardComponent,
    featureFlags,
    requiredConsents,
    consents,
    emspTermsAndConditions,
    locale,
    tariffPricing,
    updateConsents,
    onAnalyticsEvent,
    isInternetReachable,
  } = useSettings();
  const { pollingTimeout, enableThreeDsInCreateSubscription } = featureFlags;
  const { navigate } = useHostNavigation();
  const theme = useTheme();
  const { userInfo, resubscribed } = useUserInfo();
  const { triggerCVVFlow, selectedCard } = useWallet();

  const [isAgreed, setIsAgreed] = useState(false);
  const [isPolling, setIsPolling] = useState(false);

  const intervalId = useRef<NodeJS.Timeout>();
  const timeoutId = useRef<NodeJS.Timeout>();

  const { fontScale } = useWindowDimensions();
  const scale = Math.min(fontScale, 2);
  const isLargeFont = scale > 1.2;
  const vSpace = isLargeFont ? fontScale * 40 : 8;

  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventSubsConfirmSubscriptionOpen({
        first_time_subscribing: !resubscribed,
      }),
    );
  }, [onAnalyticsEvent, resubscribed, userInfo?.type]);

  const openUrlOnPress = async (url: string) => {
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    } else {
      console.error('error');
    }
  };

  const handleTermsAndConditionsPress = () =>
    openUrlOnPress(emspTermsAndConditions);

  const { refetch, loading: isGetWalletSubscriptionLoading } = useQuery(
    GET_WALLET_SUBSCRIPTION,
    {
      notifyOnNetworkStatusChange: true,
      fetchPolicy: 'network-only',
      variables: {
        userId: userInfo?.userId,
      },
      skip: !isPolling,
      onCompleted: (response: IGetWalletSubscriptionResponse) => {
        getWalletSubscription(response);
      },
      onError: (error) => {
        console.error('GetWalletSubscription failed', error.message);
        return navigate(SubscriptionScreenNames.SubscriptionFailed, {
          onBackPress: SubscriptionScreenNames.MyComfortMembershipActivation,
        });
      },
    },
  );

  const [createWalletSubscriptionMutation] = useMutation<
    ICreateWalletSubscriptionResponse,
    ICreateWalletSubscriptionRequest
  >(CREATE_WALLET_SUBSCRIPTION, {
    onCompleted: (data) => {
      console.log(
        '\x1b[45m\x1b[37mdata createWalletSubscriptionMutation => ',
        data,
      );
      if (data?.createWalletSubscription.status === '200') {
        startPolling();
        navigate(SubscriptionScreenNames.Loading);
      } else {
        navigate(SubscriptionScreenNames.SubscriptionFailed, {
          onBackPress: SubscriptionScreenNames.MyComfortMembershipActivation,
        });
      }
    },
    onError: (error) => {
      console.error('Comfort activation failed: ', error.message);
      navigate(SubscriptionScreenNames.SubscriptionFailed, {
        onBackPress: SubscriptionScreenNames.MyComfortMembershipActivation,
      });
    },
  });

  const getWalletSubscription = useCallback(
    async (response: IGetWalletSubscriptionResponse) => {
      console.log(
        '\x1b[45m\x1b[37mresponse getWalletSubscription => ',
        response,
      );
      const subscriptionData = response.getWalletSubscription;
      if (
        !['ACTIVE', 'INACTIVE', 'CANCELLED'].includes(subscriptionData.status)
      ) {
        return null;
      }
      setIsPolling(false);
      clearInterval(intervalId.current);
      clearTimeout(timeoutId.current);

      return navigate(
        subscriptionData.status === 'ACTIVE'
          ? SubscriptionScreenNames.SubscriptionSuccess
          : SubscriptionScreenNames.SubscriptionFailed,
        {
          onBackPress:
            subscriptionData.status === 'ACTIVE'
              ? null
              : SubscriptionScreenNames.MyComfortMembershipActivation,
        },
      );
    },
    [navigate],
  );

  const startPolling = () => {
    setIsPolling(true);
    intervalId.current = setInterval(() => {
      refetch();
    }, POLLING_INTERVAL);
    timeoutId.current = setTimeout(() => {
      setIsPolling(false);
      clearInterval(intervalId.current);
      navigate(SubscriptionScreenNames.SubscriptionFailed, {
        onBackPress: SubscriptionScreenNames.MyComfortMembershipActivation,
      });
    }, pollingTimeout);
  };

  const handleSubscribe = async () => {
    if (!isAgreed && !isConsentSatisfied) {
      return;
    }
    if (!isConsentSatisfied) {
      await updateConsents([{ ...requiredConsents, accepted: isAgreed }]).catch(
        (err) => {
          console.error('T&C updated failed: ', err);
          navigate(SubscriptionScreenNames.SubscriptionFailed, {
            onBackPress: SubscriptionScreenNames.MyComfortMembershipActivation,
          });
        },
      );
    }
    onAnalyticsEvent(
      SubsAnalyticsEventSubsConfirmAndAuthoriseClick({
        tag_ids: userInfo?.tagIds || [],
        first_time_subscribing: !resubscribed,
        offer_code: null,
      }),
    );
    triggerCVVFlow({
      customNavigationKey: {
        name: SubscriptionScreenNames.MyComfortMembershipActivation,
        key: SubscriptionScreenNames.MyComfortMembershipActivation,
      },

      onSuccess: async (threeDS?: IThreeDS) => {
        try {
          await createWalletSubscriptionMutation({
            variables: {
              userId: userInfo?.userId ?? '',
              paymentMethodId: selectedCard?.paymentMethodId ?? '',
              country: userInfo?.country,
              ...(enableThreeDsInCreateSubscription && threeDS
                ? { threeDS }
                : {}),
            },
          });
        } catch (error) {
          console.error('Extra Tariff activation failed: ', error);
          navigate(SubscriptionScreenNames.SubscriptionFailed, {
            onBackPress: SubscriptionScreenNames.MyComfortMembershipActivation,
          });
        }
      },
      onFailure: () => {
        navigate(SubscriptionScreenNames.SubscriptionFailed, {
          onBackPress: SubscriptionScreenNames.MyComfortMembershipActivation,
        });
      },
    });
  };

  const currentDate = useMemo(() => new Date(), []);

  const hasHadSubscriptionBefore = userInfo?.membership?.some(
    (membership) =>
      membership.userType === UserTypeEnum.SUBS_WALLET &&
      membership.membershipStatus === MembershipStatus.CANCELLED,
  );

  const shouldShowIntroOffer =
    !hasHadSubscriptionBefore && !featureFlags.disableIntroOffer;

  // Convert numeric values to strings where needed

  const formattedSubscriptionAmount = formatCurrency({
    amount: featureFlags.subscriptionAmount,
    currency: getCurrency(userInfo?.country),
    locale,
  });

  const isLoading = isPolling || isGetWalletSubscriptionLoading;
  const isConsentSatisfied = useMemo(() => {
    if (!requiredConsents) {
      return true;
    }

    return consents?.some(
      (consent) =>
        consent.consentType === requiredConsents.consentType &&
        parseInt(consent.version || '0') >=
          parseInt(requiredConsents?.version || '0'),
    );
  }, [requiredConsents, consents]);

  const handleTickBoxPress = () => {
    setIsAgreed((prev) => !prev);
  };

  return (
    <S.ScreenWrapper>
      <S.ScrollWrapper bounces={false}>
        <S.SectionHeader>
          {t.myComfortMembership.myComfortMembershipActivation.summary}
        </S.SectionHeader>
        <S.MembershipDetails>
          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.myComfortMembership.membershipDetails.activationDate}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {formatDate(currentDate.toISOString())}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>
          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.membershipOnboard.table.line1}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {shouldShowIntroOffer
                ? t.myComfortMembership.membershipDetails.monthlyBaseFeeValue.replace(
                    '{{subscriptionAmount}}',
                    formattedSubscriptionAmount,
                  )
                : formattedSubscriptionAmount}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>
          <S.MidLine />
          {tariffs(
            formattedSubscriptionAmount,
            userInfo?.country || UserCountry.DE,
            locale,
            featureFlags['subsChargeRateDC>50kW'],
            featureFlags['subsChargeRateDC<50kW'],
            featureFlags['subsChargeRateAC<22kW'],
            t.myComfortMembership.myComfortMembershipActivation.table,
          ).map(
            (tariff, index) =>
              index > 0 && (
                <>
                  <S.MembershipDetailsRow key={tariff.left.title}>
                    <S.MembershipDetailsLabel {...tariff.left.style}>
                      {tariff.left.title}
                    </S.MembershipDetailsLabel>
                    {tariff.right === '' ? (
                      tariff.right
                    ) : (
                      <S.MembershipDetailsValue>
                        {tariff.right}
                      </S.MembershipDetailsValue>
                    )}
                  </S.MembershipDetailsRow>
                  {tariff.hasEndLine && <S.MidLine />}
                </>
              ),
          )}
          {<S.MidLine />}

          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.myComfortMembership.membershipDetails.contractTerm}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {t.myComfortMembership.membershipDetails.contractTermValue}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>
          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.myComfortMembership.myComfortMembershipActivation.billing}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {t.myComfortMembership.membershipDetails.paymentDueDateValue.replace(
                '{{dueDate}}',
                getDateWithSuffix(currentDate.toString(), locale),
              )}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>
          <S.MembershipDetailsRow>
            <S.MembershipDetailsLabel>
              {t.myComfortMembership.membershipDetails.cancellation}
            </S.MembershipDetailsLabel>
            <S.MembershipDetailsValue>
              {t.myComfortMembership.membershipDetails.cancellationValue}
            </S.MembershipDetailsValue>
          </S.MembershipDetailsRow>
        </S.MembershipDetails>
        <S.MembershipExtraInfoWrapper>
          {userInfo?.country === UserCountry.DE && (
            <>
              <FootnoteDetail
                left={
                  t.myComfortMembership.myComfortMembershipActivation
                    .additionalInfo.firstPointKey
                }
                right={t.myComfortMembership.myComfortMembershipActivation.additionalInfo.firstPointValue.replace(
                  '{{extraTariffDiscountPercentage}}',
                  featureFlags.extraTariffDiscountPercentage.toString(),
                )}
              />
              <FootnoteDetail
                left={
                  t.myComfortMembership.myComfortMembershipActivation
                    .additionalInfo.secondPointKey
                }
                right={
                  <CustomLinkParagraphComponent
                    text={
                      t.myComfortMembership.myComfortMembershipActivation
                        .additionalInfo.hyperlink
                    }
                    links={[
                      {
                        text: t.myComfortMembership
                          .myComfortMembershipActivation.additionalInfo
                          .hyperlink_part_2,
                        url: tariffPricing,
                      },
                    ]}
                  />
                }
              />
            </>
          )}
          <Spacer vSpace={10} />
          <FootnoteDetail
            right={
              t.myComfortMembership.myComfortMembershipActivation.additionalInfo
                .thirdPointValue
            }
          />
        </S.MembershipExtraInfoWrapper>

        <S.LinkedPaymentHeader>
          {
            t.myComfortMembership.myComfortMembershipActivation.paymentDetails
              .header
          }
        </S.LinkedPaymentHeader>
        <S.LinkedPaymentCardInfo>
          {
            t.myComfortMembership.myComfortMembershipActivation.paymentDetails
              .description
          }
        </S.LinkedPaymentCardInfo>
        <S.AmexHeader>
          {t.myComfortMembership.myComfortMembershipActivation.amex.header}
        </S.AmexHeader>
        <S.AmexInfo>
          {t.myComfortMembership.myComfortMembershipActivation.amex.description}
        </S.AmexInfo>
        {cardComponent && (
          <S.PaymentContainer>{cardComponent}</S.PaymentContainer>
        )}
        <Spacer vSpace={vSpace} />

        <S.SectionWrapper>
          {isConsentSatisfied ? (
            <S.BottomTandC onPress={handleTermsAndConditionsPress}>
              <S.LinkText>
                {
                  t.myComfortMembership.myComfortMembershipActivation
                    .termsAndConditionsApply
                }
              </S.LinkText>
            </S.BottomTandC>
          ) : (
            <CheckBoxInput
              noBorderRadius
              alignCenter
              accessibilityLabel={
                t.myComfortMembership.myComfortMembershipActivation
                  .termsAndConditionsAgreement
              }
              defaultChecked={isAgreed}
              onPress={handleTickBoxPress}
              label={
                <SmallText onPress={handleTickBoxPress}>
                  {
                    t.myComfortMembership.myComfortMembershipActivation
                      .termsAndConditions
                  }
                  <Link onPress={handleTermsAndConditionsPress}>
                    {
                      t.myComfortMembership.myComfortMembershipActivation
                        .termsAndConditionsLink
                    }
                  </Link>
                </SmallText>
              }
              color={theme.subscriptionMfe?.color.grey.one}
            />
          )}
        </S.SectionWrapper>

        <S.SectionWrapper>
          <S.ActionButton>
            <Button
              type={ButtonAction.PRIMARY}
              onPress={handleSubscribe}
              loading={isLoading}
              disabled={
                (!isAgreed && !isConsentSatisfied) ||
                !selectedCard ||
                isLoading ||
                !isInternetReachable ||
                selectedCard?.cardType?.toLowerCase() === 'amex'
              }
              accessibilityLabel={
                t.myComfortMembership.myComfortMembershipActivation
                  .accessibility.label
              }
              accessibilityHint={
                t.myComfortMembership.myComfortMembershipActivation
                  .accessibility.hint
              }
              testID="BookAndPayButton"
            >
              {t.adacCancelSubscriptionConfirmation.buttons.BOOK_AND_PAY}
            </Button>
          </S.ActionButton>
        </S.SectionWrapper>
      </S.ScrollWrapper>
    </S.ScreenWrapper>
  );
};

export default MyComfortMembershipActivation;
