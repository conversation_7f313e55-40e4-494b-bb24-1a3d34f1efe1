import {
  Button,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import React from 'react';

import PaymentFailedImage from '../../../assets/images/subscription-failed.png';
import { SubscriptionScreenNames } from '../../common/enums';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import * as S from './PaymentFailed.styles';

const PaymentFailed = () => {
  const { t } = useSettings();
  const { navigate } = useHostNavigation();

  const onPressGoBack = () => {
    navigate(SubscriptionScreenNames.SubscriptionPaused);
  };

  return (
    <S.ScreenWrapper>
      <S.TopContainer>
        <S.HeaderText>{t.gocardless.text.SOMETHING_WENT_WRONG}</S.HeaderText>
        <S.StyledIllustration source={PaymentFailedImage} />

        <S.ErrorMessage>
          {`${t.gocardless.text.PAYMENT_FAILED_ERROR_TEXT} `}
        </S.ErrorMessage>
      </S.TopContainer>

      <S.BottomContainer>
        <S.TroubleShootingText>
          {`${t.gocardless.text.PAYMENT_FAILED_TROUBLESHOOTING_TEXT} `}
        </S.TroubleShootingText>
        <Button
          type={ButtonAction.PRIMARY}
          disabled={false}
          onPress={onPressGoBack}
          size={ButtonSize.DEFAULT}
          testID="GoBackAndTryAgain.Button"
          accessibilityLabel={
            t.gocardless.accessibility.GOBACKANDTRYAGAIN.LABEL
          }
          accessibilityHint={t.gocardless.accessibility.GOBACKANDTRYAGAIN.HINT}
        >
          {t.gocardless.buttons.GOBACKANDTRYAGAIN}
        </Button>
      </S.BottomContainer>
    </S.ScreenWrapper>
  );
};

export default PaymentFailed;
