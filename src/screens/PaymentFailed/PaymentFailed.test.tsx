import { ThemeProvider } from '@bp/ui-components/mobile';
import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';

import { TRANSLATIONS } from '../../translations';
import { updateTranslations } from '../../utils/updateTranslations';
import PaymentFailed from './PaymentFailed';

const mockTranslations = updateTranslations(TRANSLATIONS.en_GB, {
  brand: 'bp',
});
const mockUseSettingsObj = {
  t: mockTranslations,
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

const renderWithTheme = () =>
  render(
    <ThemeProvider>
      <PaymentFailed />
    </ThemeProvider>,
  );

describe('Payment Failed Screen', () => {
  it('renders correctly', () => {
    const PaymentFailedScreen = renderWithTheme();
    expect(PaymentFailedScreen.toJSON()).toMatchSnapshot();
  });

  it('renders button with accessibility label', async () => {
    const { getByLabelText } = renderWithTheme();
    await waitFor(() => {
      const button = getByLabelText(
        mockTranslations.gocardless.buttons.GOBACKANDTRYAGAIN,
      );
      expect(button).toBeDefined();
    });
  });
  it('should trigger navigation when button is pressed', () => {
    const PaymentFailedScreen = renderWithTheme();

    const button = PaymentFailedScreen.getByTestId('GoBackAndTryAgain.Button');

    fireEvent(button, 'onPress');

    expect(mockHostNavigationObj.navigate).toHaveBeenCalled();
  });
});
