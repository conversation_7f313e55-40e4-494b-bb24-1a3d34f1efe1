// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Payment Failed Screen renders correctly 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 0,
      "justifyContent": "space-between",
      "marginBottom": 24,
      "marginLeft": 24,
      "marginRight": 24,
      "marginTop": 42,
      "paddingTop": 0,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "display": "flex",
        "gap": 40,
      }
    }
  >
    <Text
      style={
        {
          "color": "rgb(17, 17, 17)",
          "fontFamily": "Roboto-Light",
          "fontSize": 28,
          "letterSpacing": 0.5,
          "lineHeight": 40,
        }
      }
    >
      Something went wrong
    </Text>
    <Image
      source={
        {
          "testUri": "../../../assets/images/subscription-failed.png",
        }
      }
      style={
        {
          "height": 266,
          "width": 248,
        }
      }
    />
    <Text
      style={
        {
          "color": "rgb(17, 17, 17)",
          "fontFamily": "Roboto-Light",
          "fontSize": 18,
          "letterSpacing": 0.2,
          "lineHeight": 32,
          "textAlign": "center",
        }
      }
    >
      Payment unsuccessful, an error occurred, please try again. 
    </Text>
  </View>
  <View
    style={
      {
        "display": "flex",
        "gap": 24,
      }
    }
  >
    <Text
      style={
        {
          "color": "rgb(17, 17, 17)",
          "fontFamily": "Roboto-Light",
          "fontSize": 13,
          "letterSpacing": 0.25,
          "lineHeight": 23,
        }
      }
    >
      Troubleshooting:
 1. Check your app is up to date
 2. Check your payment method is still valid
 3. Contact customer care from the help tab 
    </Text>
    <View
      accessibilityHint="Navigates to previous screen"
      accessibilityLabel="Go back and try again"
      accessibilityRole="button"
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": false,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "backgroundColor": "#000096",
          "borderBottomLeftRadius": 23,
          "borderBottomRightRadius": 23,
          "borderColor": "transparent",
          "borderStyle": "solid",
          "borderTopLeftRadius": 23,
          "borderTopRightRadius": 23,
          "borderWidth": 0,
          "justifyContent": "center",
          "minHeight": 46,
          "opacity": 1,
          "paddingHorizontal": 25.5,
          "paddingVertical": 12,
        }
      }
      testID="GoBackAndTryAgain.Button"
    >
      <View>
        <Text
          disabled={false}
          inverted={false}
          size="large"
          style={
            {
              "color": "#ffffff",
              "fontFamily": "Roboto-Regular",
              "fontSize": 15,
              "letterSpacing": 0.7,
              "textAlign": "center",
            }
          }
          type="primary"
        >
          Go back and try again
        </Text>
      </View>
    </View>
  </View>
</RCTSafeAreaView>
`;
