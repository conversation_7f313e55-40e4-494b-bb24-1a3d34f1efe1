import { Platform, StatusBar } from 'react-native';
import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
  padding-top: ${Platform.OS === 'android'
    ? `${StatusBar.currentHeight}px`
    : '0px'};
  justify-content: space-between;
  margin-top: 42px;
  margin-left: 24px;
  margin-right: 24px;
  margin-bottom: 24px;
`;

export const TopContainer = styled.View`
  display: flex;
  gap: 40px;
  align-items: center;
`;

export const HeaderText = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 28px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.5px;
  line-height: 40px;
`;

export const StyledIllustration = styled.Image`
  width: 248px;
  height: 266px;
`;

export const ErrorMessage = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 18px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.2px;
  line-height: 32px;
  text-align: center;
`;

export const BottomContainer = styled.View`
  display: flex;
  gap: 24px;
`;

export const TroubleShootingText = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 13px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.25px;
  line-height: 23px;
`;
