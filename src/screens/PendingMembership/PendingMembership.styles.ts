import { styled } from 'styled-components/native';

export const Container = styled.View`
  padding: 48px 10px 0;
  width: 100%;
  height: 100%;
  display: flex;
  background: ${(p: any) => p.theme.subscriptionMfe.color.grey.five};
`;

export const UpperViewContainer = styled.ImageBackground`
  height: 172px;
  width: 86%;
  margin: 0 48px;
  box-shadow: 0 12px 8px ${(p: any) => p.theme.subscriptionMfe.color.grey.one};
  shadow-opacity: 0.5;
`;

export const InnerLogoImageView = styled.View`
  height: 101px;
  width: 100%;
  align-items: center;
`;

export const MessageContainer = styled.View`
  border: 1px solid ${(p: any) => p.theme.subscriptionMfe.color.grey.four};
  z-index: -1;
  width: 90%;
  height: 150px;
  margin: -50px auto;
  background: #fff;
  border-radius: 10px;
  align-items: center;
  padding: 10px 20px 20px;
  justify-content: flex-end;
  box-shadow: 0 2px 25px ${(p: any) => p.theme.subscriptionMfe.color.grey.four};
`;

export const PendingMessage = styled.Text`
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0.7px;
  color: ${(p: any) => p.theme.subscriptionMfe.color.grey.one};
`;
