import React, { useEffect } from 'react';

import Bppulsecard from '../../../assets/images/bppulse_card.png';
import BppulseLogo from '../../../assets/images/bppulse_logo.svg';
import { SubsAnalyticsEventPendingScreenOpen } from '../../analytics/events';
import { SpacerDynamic } from '../../components';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { getAnalyticsPayload } from '../../utils/helpers';
import * as S from './PendingMembership.styles';

const PendingMembership = () => {
  const { t, onAnalyticsEvent } = useSettings();
  const { userInfo } = useUserInfo();

  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventPendingScreenOpen(getAnalyticsPayload(userInfo)),
    );
  }, [onAnalyticsEvent, userInfo]);

  return (
    <S.Container>
      <S.UpperViewContainer
        imageStyle={{ borderRadius: 8.4 }}
        source={Bppulsecard}
        accessibilityLabel={t.gocardless.accessibility.PULSECARD.LABEL}
        accessibilityHint={t.gocardless.accessibility.PULSECARD.HINT}
      >
        <SpacerDynamic vSpace={4} />
        <S.InnerLogoImageView>
          <BppulseLogo />
        </S.InnerLogoImageView>
      </S.UpperViewContainer>
      <S.MessageContainer>
        <S.PendingMessage>
          {t.gocardless.text.PENDING_MEMBERSHIP}
        </S.PendingMessage>
      </S.MessageContainer>
    </S.Container>
  );
};

export default PendingMembership;
