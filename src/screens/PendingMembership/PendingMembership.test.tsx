import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import { updateTranslations } from '../../utils/updateTranslations';
import PendingMembership from './PendingMembership';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'bp' }),
  user: { id: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

describe('Pending membership screen', () => {
  it('should render correctly', () => {
    const { toJSON } = render(
      <Theme>
        <PendingMembership />
      </Theme>,
    );
    expect(toJSON()).toMatchSnapshot();
  });
});
