// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Pending membership screen should render correctly 1`] = `
<View
  style={
    {
      "backgroundColor": "#F5F5F5",
      "display": "flex",
      "height": "100%",
      "paddingBottom": 0,
      "paddingLeft": 10,
      "paddingRight": 10,
      "paddingTop": 48,
      "width": "100%",
    }
  }
>
  <View
    accessibilityIgnoresInvertColors={true}
    style={
      {
        "height": 172,
        "marginBottom": 0,
        "marginLeft": 48,
        "marginRight": 48,
        "marginTop": 0,
        "shadowColor": "#212121",
        "shadowOffset": {
          "height": 12,
          "width": 0,
        },
        "shadowOpacity": 0.5,
        "shadowRadius": 8,
        "width": "86%",
      }
    }
  >
    <Image
      accessibilityHint="bp Pulse branded card"
      accessibilityLabel="bp Pulse Blue Card"
      source={
        {
          "testUri": "../../../assets/images/bppulse_card.png",
        }
      }
      style={
        [
          {
            "bottom": 0,
            "left": 0,
            "position": "absolute",
            "right": 0,
            "top": 0,
          },
          {
            "height": 172,
            "width": "86%",
          },
          {
            "borderRadius": 8.4,
          },
        ]
      }
    />
    <View
      style={
        {
          "height": 53.36,
          "width": 0,
        }
      }
    />
    <View
      style={
        {
          "alignItems": "center",
          "height": 101,
          "width": "100%",
        }
      }
    >
      <SvgMock />
    </View>
  </View>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "#fff",
        "borderBottomLeftRadius": 10,
        "borderBottomRightRadius": 10,
        "borderColor": "#DEDEDE",
        "borderStyle": "solid",
        "borderTopLeftRadius": 10,
        "borderTopRightRadius": 10,
        "borderWidth": 1,
        "height": 150,
        "justifyContent": "flex-end",
        "marginBottom": -50,
        "marginLeft": "auto",
        "marginRight": "auto",
        "marginTop": -50,
        "paddingBottom": 20,
        "paddingLeft": 20,
        "paddingRight": 20,
        "paddingTop": 10,
        "shadowColor": "#DEDEDE",
        "shadowOffset": {
          "height": 2,
          "width": 0,
        },
        "shadowOpacity": 1,
        "shadowRadius": 25,
        "width": "90%",
        "zIndex": -1,
      }
    }
  >
    <Text
      style={
        {
          "color": "#212121",
          "fontSize": 16,
          "letterSpacing": 0.7,
          "lineHeight": 24,
        }
      }
    >
      bp pulse membership card can take up to 14 days to arrive.
    </Text>
  </View>
</View>
`;
