import React, { useContext, useEffect, useState } from 'react';

import { SubsAnalyticsEventRFIDPreferenceScreenOpen } from '../../analytics/events';
import { SubscriptionScreenNames } from '../../common/enums';
import {
  CheckBoxInput,
  DetailIcon,
  DownArrow,
  PulseCredit,
  Spacer,
  Tablerow,
} from '../../components';
import { AddressContext } from '../../providers/AddressProvider';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import theme from '../../themes/Theme.default';
import { getAnalyticsPayload } from '../../utils/helpers';
import * as S from './RFIDForm.styles';

export default ({ route }: any) => {
  const { params } = route;
  const { navigate } = useHostNavigation();
  const { setRFIDType } = useContext(AddressContext);
  const [marketing, setMarketing] = useState(false);
  const { t, onAnalyticsEvent } = useSettings();
  const { userInfo } = useUserInfo();

  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventRFIDPreferenceScreenOpen(getAnalyticsPayload(userInfo)),
    );
  }, [onAnalyticsEvent, userInfo]);

  const cardColor =
    (params && params.fromScreen !== 'replaceCard') || marketing
      ? theme?.subscriptionMfe?.icons.enabled.color
      : theme?.subscriptionMfe?.bottomNavigation.disabled.icon;

  const pulsePointData = [
    {
      id: 'fob',
      icon: <DetailIcon color={cardColor} />,
    },
    {
      id: 'card',
      icon: <PulseCredit color={cardColor} />,
    },
  ];

  const onPress = (item: any) => {
    setRFIDType(item.id);
    navigate(SubscriptionScreenNames.ShippingAddress, {
      fromScreen: params.fromScreen,
      logSubsFlow: true,
    });
  };

  type AccessiblityId = keyof typeof t.gocardless.accessibility;
  type TextId = keyof typeof t.gocardless.text;

  const renderPulsePointItems = () => {
    return pulsePointData.map((item, index) => {
      const { id } = item;
      const accessibilityLabel =
        t.gocardless.accessibility[id.toUpperCase() as AccessiblityId].LABEL;
      const accessibilityHint =
        t.gocardless.accessibility[id.toUpperCase() as AccessiblityId].HINT;
      const text = t.gocardless.text[id.toUpperCase() as TextId];

      return (
        <Tablerow
          key={index}
          accessible
          accessibilityLabel={accessibilityLabel}
          accessibilityHint={accessibilityHint}
          text={text}
          icon={item.icon}
          righticon={
            <DownArrow
              color={
                (params && params.fromScreen !== 'replaceCard') || marketing
                  ? theme?.subscriptionMfe?.color.black
                  : theme?.subscriptionMfe?.bottomNavigation.disabled.icon
              }
            />
          }
          onPress={() => onPress(item)}
          textColor={
            (params && params.fromScreen !== 'replaceCard') || marketing
              ? theme?.subscriptionMfe?.color.black2
              : theme?.subscriptionMfe?.bottomNavigation.disabled.icon
          }
        />
      );
    });
  };

  return (
    <S.ScreenWrapper>
      <S.PulseAccessContainer>
        {params && params.fromScreen !== 'replaceCard' && (
          <S.InnerViewContainer>
            <S.TitleText>{t.gocardless.text.PULSE_POINT_TITLE}</S.TitleText>
            <S.SubText>{t.gocardless.text.PULSE_POINT_SUBTEXT}</S.SubText>
          </S.InnerViewContainer>
        )}
        {params && params.fromScreen === 'replaceCard' && (
          <S.InnerViewContainer>
            <S.TitleText>{t.gocardless.replacecard.HEADER}</S.TitleText>
            <S.SubText>{t.gocardless.replacecard.SUBHEADER}</S.SubText>
            <Spacer vSpace={20} />
            <CheckBoxInput
              defaultChecked={marketing}
              onPress={() =>
                marketing === true ? setMarketing(false) : setMarketing(true)
              }
              label={t.gocardless.replacecard.CHECKBOX_LABEL}
              testID={t.gocardless.replacecard.CHECKBOX_LABEL}
              accessibilityLabel={t.gocardless.replacecard.CHECKBOX_LABEL}
            />
            <Spacer vSpace={30} />
            <S.SubText>{t.gocardless.replacecard.TYPE_PREFERENCE}</S.SubText>
          </S.InnerViewContainer>
        )}
        <S.MiddleViewContainer
          pointerEvents={
            params && params.fromScreen === 'replaceCard' && marketing !== true
              ? 'none'
              : 'auto'
          }
        >
          {renderPulsePointItems()}
        </S.MiddleViewContainer>
      </S.PulseAccessContainer>
    </S.ScreenWrapper>
  );
};
