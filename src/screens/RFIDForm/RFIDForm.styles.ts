import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
`;

export const Text = styled.Text`
  text-align: left;
  color: ${(props: any) =>
    props.theme.subscriptionMfe.bottomNavigation.disabled.text};
`;
export const PulseAccessContainer = styled.ScrollView`
  padding-top: 44px;
  flex: 1;
  background-color: ${(props: any) =>
    props.theme.subscriptionMfe.gocardless.page.background};
`;

export const InnerViewContainer = styled.View`
  padding-left: 18px;
  padding-right: 18px;
`;

export const UpperViewContainer = styled.View`
  padding-left: 16px;
  padding-right: 16px;
  justify-content: center;
  align-items: center;
`;
export const TitleText = styled.Text`
  text-align: left;
  font-size: 20px;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.banner};
  padding-bottom: 10px;
`;

export const SubText = styled.Text`
  text-align: left;
  font-size: 16px;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  padding-bottom: 20px;
`;

export const MiddleViewContainer = styled.View`
  background-color: ${(props: any) => props.theme.subscriptionMfe.color.white};
  border-width: 1px;
  border-color: ${(props: any) => props.theme.subscriptionMfe.table.border};
`;
