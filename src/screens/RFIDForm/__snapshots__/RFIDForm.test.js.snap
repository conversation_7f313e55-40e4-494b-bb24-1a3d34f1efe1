// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RFIDForm component should render correctly 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 0,
    }
  }
>
  <RCTScrollView
    style={
      {
        "backgroundColor": "#F5F5F5",
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "paddingTop": 44,
      }
    }
  >
    <View>
      <View
        pointerEvents="auto"
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#D8D8D8",
            "borderWidth": 1,
          }
        }
      >
        <View
          accessibilityHint="Navigates to Address page"
          accessibilityLabel="Key fob"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "borderBottomWidth": 1,
              "borderColor": "#D8D8D8",
              "borderTopWidth": 0,
              "flexDirection": "row",
              "justifyContent": "space-between",
              "opacity": 1,
              "paddingBottom": 16,
              "paddingTop": 16,
            }
          }
        >
          <View
            style={
              {
                "alignItems": "center",
                "flexDirection": "row",
                "flexShrink": 1,
                "justifyContent": "flex-start",
                "marginBottom": 0,
              }
            }
            type="primary"
          >
            <View
              style={
                {
                  "alignItems": "center",
                  "justifyContent": "center",
                  "paddingLeft": 32,
                  "paddingTop": 0,
                  "width": 20,
                }
              }
              type="primary"
            >
              <RNSVGSvgView
                bbHeight="20"
                bbWidth="16"
                focusable={false}
                height="20"
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": 20,
                      "width": 16,
                    },
                  ]
                }
                width="16"
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGGroup
                    fill={null}
                    name="illustarations-and-Icons"
                    propList={
                      [
                        "fill",
                        "stroke",
                      ]
                    }
                    stroke={null}
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          -592,
                          -3332,
                        ]
                      }
                      name="Icons"
                    >
                      <RNSVGGroup
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        matrix={
                          [
                            1,
                            0,
                            0,
                            1,
                            588,
                            3330,
                          ]
                        }
                        name="atom-/-icon-/-line-/-custom-/-your-details"
                      >
                        <RNSVGPath
                          d="M18,2 C19.1045695,2 20,2.8954305 20,4 L20,20 C20,21.1045695 19.1045695,22 18,22 L6,22 C4.8954305,22 4,21.1045695 4,20 L4,4 C4,2.8954305 4.8954305,2 6,2 L18,2 Z M18,3 L6,3 C5.48716416,3 5.06449284,3.38604019 5.00672773,3.88337887 L5,4 L5,20 C5,20.5128358 5.38604019,20.9355072 5.88337887,20.9932723 L6,21 L18,21 C18.5128358,21 18.9355072,20.6139598 18.9932723,20.1166211 L19,20 L19,4 C19,3.48716416 18.6139598,3.06449284 18.1166211,3.00672773 L18,3 Z M16.5,8 C16.7761424,8 17,8.22385763 17,8.5 C17,8.77614237 16.7761424,9 16.5,9 L7.5,9 C7.22385763,9 7,8.77614237 7,8.5 C7,8.22385763 7.22385763,8 7.5,8 L16.5,8 Z M16.5,5 C16.7761424,5 17,5.22385763 17,5.5 C17,5.77614237 16.7761424,6 16.5,6 L7.5,6 C7.22385763,6 7,5.77614237 7,5.5 C7,5.22385763 7.22385763,5 7.5,5 L16.5,5 Z"
                          fill={
                            {
                              "payload": 4284572517,
                              "type": 0,
                            }
                          }
                          name="Combined-Shape"
                          propList={
                            [
                              "fill",
                            ]
                          }
                        />
                      </RNSVGGroup>
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
            <View
              style={
                {
                  "flexDirection": "column",
                  "flexShrink": 1,
                  "justifyContent": "center",
                  "paddingLeft": 28,
                  "paddingRight": 8,
                }
              }
            >
              <Text
                ellipsizeMode="tail"
                style={
                  {
                    "alignItems": "flex-start",
                    "color": "#616365",
                    "fontSize": 16,
                    "textAlign": "left",
                  }
                }
                textColor="#616365"
              >
                Key fob
              </Text>
            </View>
          </View>
          <View
            style={
              {
                "alignItems": "center",
                "height": 30,
                "justifyContent": "center",
                "paddingBottom": 32,
                "transform": [
                  {
                    "rotate": "-90deg",
                  },
                ],
                "width": 30,
              }
            }
          >
            <RNSVGSvgView
              align="xMidYMid"
              bbHeight="8"
              bbWidth="14"
              focusable={false}
              height="8"
              meetOrSlice={0}
              minX={0}
              minY={0}
              style={
                [
                  {
                    "backgroundColor": "transparent",
                    "borderWidth": 0,
                  },
                  {
                    "flex": 0,
                    "height": 8,
                    "width": 14,
                  },
                ]
              }
              vbHeight={8}
              vbWidth={14}
              width="14"
            >
              <RNSVGGroup
                fill={
                  {
                    "payload": 4278190080,
                    "type": 0,
                  }
                }
              >
                <RNSVGGroup
                  fill={null}
                  name="illustarations-and-Icons"
                  propList={
                    [
                      "fill",
                      "stroke",
                    ]
                  }
                  stroke={null}
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        -593,
                        -1706,
                      ]
                    }
                    name="Icons"
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          588,
                          1698,
                        ]
                      }
                      name="atom-/-icon-/-line-/-custom-/-dropdown"
                    >
                      <RNSVGPath
                        d="M5.64644661,8.64644661 C5.82001296,8.47288026 6.08943736,8.45359511 6.2843055,8.58859116 L6.35355339,8.64644661 L12,14.293 L17.6464466,8.64644661 C17.820013,8.47288026 18.0894374,8.45359511 18.2843055,8.58859116 L18.3535534,8.64644661 C18.5271197,8.82001296 18.5464049,9.08943736 18.4114088,9.2843055 L18.3535534,9.35355339 L12.3535534,15.3535534 C12.179987,15.5271197 11.9105626,15.5464049 11.7156945,15.4114088 L11.6464466,15.3535534 L5.64644661,9.35355339 C5.45118446,9.15829124 5.45118446,8.84170876 5.64644661,8.64644661 Z"
                        fill={
                          {
                            "payload": 4284572517,
                            "type": 0,
                          }
                        }
                        name="Path-8"
                        propList={
                          [
                            "fill",
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGSvgView>
          </View>
        </View>
        <View
          accessibilityHint="Navigates to Address page"
          accessibilityLabel="Card"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "borderBottomWidth": 1,
              "borderColor": "#D8D8D8",
              "borderTopWidth": 0,
              "flexDirection": "row",
              "justifyContent": "space-between",
              "opacity": 1,
              "paddingBottom": 16,
              "paddingTop": 16,
            }
          }
        >
          <View
            style={
              {
                "alignItems": "center",
                "flexDirection": "row",
                "flexShrink": 1,
                "justifyContent": "flex-start",
                "marginBottom": 0,
              }
            }
            type="primary"
          >
            <View
              style={
                {
                  "alignItems": "center",
                  "justifyContent": "center",
                  "paddingLeft": 32,
                  "paddingTop": 0,
                  "width": 20,
                }
              }
              type="primary"
            >
              <RNSVGSvgView
                align="xMidYMid"
                bbHeight="16"
                bbWidth="20"
                focusable={false}
                height="16"
                meetOrSlice={0}
                minX={0}
                minY={0}
                style={
                  [
                    {
                      "backgroundColor": "transparent",
                      "borderWidth": 0,
                    },
                    {
                      "flex": 0,
                      "height": 16,
                      "width": 20,
                    },
                  ]
                }
                vbHeight={16}
                vbWidth={20}
                width="20"
              >
                <RNSVGGroup
                  fill={
                    {
                      "payload": 4278190080,
                      "type": 0,
                    }
                  }
                >
                  <RNSVGGroup
                    fill={null}
                    name="illustarations-and-Icons"
                    propList={
                      [
                        "fill",
                        "stroke",
                      ]
                    }
                    stroke={null}
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          -590,
                          -2614,
                        ]
                      }
                      name="Icons"
                    >
                      <RNSVGGroup
                        fill={
                          {
                            "payload": 4278190080,
                            "type": 0,
                          }
                        }
                        matrix={
                          [
                            1,
                            0,
                            0,
                            1,
                            588,
                            2610,
                          ]
                        }
                        name="atom-/-icon-/-line-/-custom-/-pulse-credit"
                      >
                        <RNSVGRect
                          fill={
                            {
                              "payload": 4278190080,
                              "type": 0,
                            }
                          }
                          height="24"
                          name="Rectangle"
                          width="24"
                          x="0"
                          y="0"
                        />
                        <RNSVGPath
                          d="M20,4 C21.1045695,4 22,4.8954305 22,6 L22,14 C22,15.1045695 21.1045695,16 20,16 L18.9724574,16.0007074 C18.7234136,18.2503421 16.8160341,20 14.5,20 C12.1839659,20 10.2765864,18.2503421 10.0275426,16.0007074 L4,16 C2.8954305,16 2,15.1045695 2,14 L2,6 C2,4.8954305 2.8954305,4 4,4 L20,4 Z M14.5,13.5 C13.3954305,13.5 12.5,14.3954305 12.5,15.5 C12.5,16.6045695 13.3954305,17.5 14.5,17.5 C15.6045695,17.5 16.5,16.6045695 16.5,15.5 C16.5,14.3954305 15.6045695,13.5 14.5,13.5 Z M11.25,14.25 C10.5596441,14.25 10,14.8096441 10,15.5 C10,16.1903559 10.5596441,16.75 11.25,16.75 C11.9403559,16.75 12.5,16.1903559 12.5,15.5 C12.5,14.8096441 11.9403559,14.25 11.25,14.25 Z M20,5 L4,5 C3.48716416,5 3.06449284,5.38604019 3.00672773,5.88337887 L3,6 L3,14 C3,14.5128358 3.38604019,14.9355072 3.88337887,14.9932723 L4,15 L10.0274316,15.000297 C10.2760186,12.7501788 12.1836212,11 14.5,11 C16.8163788,11 18.7239814,12.7501788 18.9725684,15.000297 L20,15 C20.5128358,15 20.9355072,14.6139598 20.9932723,14.1166211 L21,14 L21,6 C21,5.48716416 20.6139598,5.06449284 20.1166211,5.00672773 L20,5 Z M6.5,10 C6.77614237,10 7,10.2238576 7,10.5 C7,10.7761424 6.77614237,11 6.5,11 L5.5,11 C5.22385763,11 5,10.7761424 5,10.5 C5,10.2238576 5.22385763,10 5.5,10 L6.5,10 Z M10.5,7 C10.7761424,7 11,7.22385763 11,7.5 C11,7.77614237 10.7761424,8 10.5,8 L5.5,8 C5.22385763,8 5,7.77614237 5,7.5 C5,7.22385763 5.22385763,7 5.5,7 L10.5,7 Z"
                          fill={
                            {
                              "payload": 4284572517,
                              "type": 0,
                            }
                          }
                          name="Combined-Shape"
                          propList={
                            [
                              "fill",
                            ]
                          }
                        />
                      </RNSVGGroup>
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGSvgView>
            </View>
            <View
              style={
                {
                  "flexDirection": "column",
                  "flexShrink": 1,
                  "justifyContent": "center",
                  "paddingLeft": 28,
                  "paddingRight": 8,
                }
              }
            >
              <Text
                ellipsizeMode="tail"
                style={
                  {
                    "alignItems": "flex-start",
                    "color": "#616365",
                    "fontSize": 16,
                    "textAlign": "left",
                  }
                }
                textColor="#616365"
              >
                Card
              </Text>
            </View>
          </View>
          <View
            style={
              {
                "alignItems": "center",
                "height": 30,
                "justifyContent": "center",
                "paddingBottom": 32,
                "transform": [
                  {
                    "rotate": "-90deg",
                  },
                ],
                "width": 30,
              }
            }
          >
            <RNSVGSvgView
              align="xMidYMid"
              bbHeight="8"
              bbWidth="14"
              focusable={false}
              height="8"
              meetOrSlice={0}
              minX={0}
              minY={0}
              style={
                [
                  {
                    "backgroundColor": "transparent",
                    "borderWidth": 0,
                  },
                  {
                    "flex": 0,
                    "height": 8,
                    "width": 14,
                  },
                ]
              }
              vbHeight={8}
              vbWidth={14}
              width="14"
            >
              <RNSVGGroup
                fill={
                  {
                    "payload": 4278190080,
                    "type": 0,
                  }
                }
              >
                <RNSVGGroup
                  fill={null}
                  name="illustarations-and-Icons"
                  propList={
                    [
                      "fill",
                      "stroke",
                    ]
                  }
                  stroke={null}
                >
                  <RNSVGGroup
                    fill={
                      {
                        "payload": 4278190080,
                        "type": 0,
                      }
                    }
                    matrix={
                      [
                        1,
                        0,
                        0,
                        1,
                        -593,
                        -1706,
                      ]
                    }
                    name="Icons"
                  >
                    <RNSVGGroup
                      fill={
                        {
                          "payload": 4278190080,
                          "type": 0,
                        }
                      }
                      matrix={
                        [
                          1,
                          0,
                          0,
                          1,
                          588,
                          1698,
                        ]
                      }
                      name="atom-/-icon-/-line-/-custom-/-dropdown"
                    >
                      <RNSVGPath
                        d="M5.64644661,8.64644661 C5.82001296,8.47288026 6.08943736,8.45359511 6.2843055,8.58859116 L6.35355339,8.64644661 L12,14.293 L17.6464466,8.64644661 C17.820013,8.47288026 18.0894374,8.45359511 18.2843055,8.58859116 L18.3535534,8.64644661 C18.5271197,8.82001296 18.5464049,9.08943736 18.4114088,9.2843055 L18.3535534,9.35355339 L12.3535534,15.3535534 C12.179987,15.5271197 11.9105626,15.5464049 11.7156945,15.4114088 L11.6464466,15.3535534 L5.64644661,9.35355339 C5.45118446,9.15829124 5.45118446,8.84170876 5.64644661,8.64644661 Z"
                        fill={
                          {
                            "payload": 4284572517,
                            "type": 0,
                          }
                        }
                        name="Path-8"
                        propList={
                          [
                            "fill",
                          ]
                        }
                      />
                    </RNSVGGroup>
                  </RNSVGGroup>
                </RNSVGGroup>
              </RNSVGGroup>
            </RNSVGSvgView>
          </View>
        </View>
      </View>
    </View>
  </RCTScrollView>
</RCTSafeAreaView>
`;
