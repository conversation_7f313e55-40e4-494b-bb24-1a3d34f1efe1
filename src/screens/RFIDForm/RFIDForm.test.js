import { render } from '@testing-library/react-native';
import React from 'react';

import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import RFIDForm from './RFIDForm';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { id: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

describe('RFIDForm component', () => {
  it('should render correctly', () => {
    const params = { from: 'account' };
    const { toJSON } = render(
      <Theme>
        <RFIDForm route={params} />
      </Theme>,
    );
    expect(toJSON()).toMatchSnapshot();
  });
});
