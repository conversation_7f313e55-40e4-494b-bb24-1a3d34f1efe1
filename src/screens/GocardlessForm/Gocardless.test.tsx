import { Mocked<PERSON>rovider } from '@apollo/client/testing';
import { render } from '@testing-library/react-native';
import React from 'react';

import { GET_GOCARDLESS_URL } from '../../client/queries';
import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import Gocardless from './Gocardless';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: {
    userId: '123',
    gocardless: { mandateStatus: 'active' },
    balance: '1.00',
  },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const MOCKS = [
  {
    request: {
      query: GET_GOCARDLESS_URL,
      variables: {
        userId: mockUseSettingsObj.user.userId,
      },
    },
    result: {
      data: {
        userInfo: {
          type: 'PAYG',
        },
        getPaymentUrl: {
          url: 'test.com',
        },
      },
    },
  },
];

const mockUseUserInfoObj = {
  userInfo: {
    userId: '123',
    gocardless: { mandateStatus: 'active' },
    balance: '1.00',
  },
  setUserInfo: jest.fn(),
};
const mockUseUserInfo = jest.fn().mockReturnValue(mockUseUserInfoObj);
jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUseUserInfo(),
}));

jest.mock('react-native-webview', () => {
  const { View } = require('react-native');
  return {
    WebView: View,
  };
});

describe('Gocardless component', () => {
  it('should render correctly', () => {
    const params = { from: 'account' };
    const { toJSON } = render(
      <Theme>
        <MockedProvider mocks={MOCKS}>
          <Gocardless route={params} />
        </MockedProvider>
      </Theme>,
    );
    expect(toJSON()).toMatchSnapshot();
  });
});
