import { useQuery } from '@apollo/client';
import Spinner from '@bp/ui-components/mobile/stages/Atoms/Spinner/Spinner';
import React, { useEffect, useRef, useState } from 'react';
import { WebView } from 'react-native-webview';

import { SubsAnalyticsEventSetupDirectDebitScreenOpen } from '../../analytics/events';
import { GET_GOCARDLESS_URL } from '../../client/queries';
import { SubscriptionScreenNames } from '../../common/enums';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { getAnalyticsPayload } from '../../utils/helpers';

export default ({ route }: any) => {
  const { params } = route;
  const { navigate } = useHostNavigation();
  const fromScreen =
    params && params.from === 'registration' ? 'registration' : 'account';
  const webview: any = useRef(null);
  const [removed, setRemoved] = useState(false);
  const { userInfo } = useUserInfo();
  const { onAnalyticsEvent } = useSettings();

  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventSetupDirectDebitScreenOpen(
        getAnalyticsPayload(userInfo),
      ),
    );
  }, [onAnalyticsEvent, userInfo]);

  const { loading, error, data } = useQuery(GET_GOCARDLESS_URL, {
    variables: {
      userId: userInfo?.userId,
    },
  });

  // Determine how we want to handle errors
  if (error) {
    console.error(error);
  }

  return (
    <>
      <Spinner active={loading} />
      {!removed && !loading && data && (
        <WebView
          source={{
            uri: data.getPaymentUrl.url,
          }}
          ref={webview}
          originWhitelist={['*']}
          javaScriptEnabled
          domStorageEnabled
          scalesPageToFit
          scrollEnabled
          allowFileAccess
          onNavigationStateChange={(navState: any) => {
            const split = navState.url.split('?redirect_flow_id=');
            if (split.length > 1) {
              const flowId = split[1];
              setRemoved(true);
              navigate(SubscriptionScreenNames.WelcomeScreen, {
                flow_id: flowId,
                from: fromScreen,
                logSubsFlow: true,
              });
            }
          }}
          androidHardwareAccelerationDisabled
        />
      )}
    </>
  );
};
