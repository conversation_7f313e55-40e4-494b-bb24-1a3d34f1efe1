import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
`;

export const Text = styled.Text`
  text-align: left;
  color: ${(props: any) =>
    props.theme.subscriptionMfe.bottomNavigation.disabled.text};
`;
export const ShippingAddressContainer = styled.View`
  flex: 1;
  background-color: #f5f5f5;
`;

export const InnerViewContainer = styled.ScrollView.attrs(() => ({
  contentContainerStyle: {
    flexGrow: 1,
  },
}))`
  flex: 1;
  overflow: scroll;
  padding-top: 44px;
  padding-left: 16px;
  padding-right: 16px;
`;

export const TitleText = styled.Text`
  padding-left: 10px;
  text-align: left;
  font-size: 20px;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.banner};
  padding-bottom: 10px;
`;

export const SubText = styled.Text`
  padding-left: 10px;
  text-align: left;
  font-size: 14px;
  line-height: 20px;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
`;
export const AddressText = styled.Text`
  text-align: left;
  font-size: 16px;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  padding-bottom: 5px;
`;

export const MiddleViewContainer = styled.View`
  padding-left: 10px;
  padding-right: 10px;
`;
export const ActionButton = styled.View`
  margin-top: auto;
  margin-bottom: 56px;
`;
export const ConfirmButton = styled.View`
  margin-top: 40%;
  margin-bottom: 16px;
`;
export const UpdateAddress = styled.TouchableOpacity``;
export const UnderlineText = styled.Text`
  height: 24px;
  width: 100%;
  color: ${(p: any) => p.theme.subscriptionMfe.color.primary};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
  text-decoration-line: underline;
`;
export const ErrorText = styled.Text`
  width: 100%;
  color: ${(p: any) => p.theme.subscriptionMfe.text.color.error};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
  padding-bottom: 20px;
`;
export const AddressSelectionBox = styled.View`
  flex-direction: row;
  align-items: center;
  border-radius: 16px;
  background-color: #ffffff;
  padding: 16px;
  border: 1px solid #dfe3e6;
`;
export const AddressBox = styled.View`
  width: 90%;
`;

export const Circle = styled.TouchableOpacity`
  height: 24px;
  width: 24px;
  padding: 5px;
  border: 1px solid #dddddd;
  border-radius: 11px;
`;

export const CheckedCircle = styled.View`
  width: 12px;
  height: 12px;
  border-radius: 11px;
  background-color: #000096;
`;

export const FooterContainer = styled.View`
  flex: 0.1;
  justify-content: flex-end;
`;
