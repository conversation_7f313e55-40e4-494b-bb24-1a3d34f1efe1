import { MockedProvider } from '@apollo/client/testing';
import { render } from '@testing-library/react-native';
import React from 'react';

import { UPDATE_SUBSCRIPTION_PREFERENCE } from '../../client/queries';
import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import ShippingAddress from './ShippingAddress';
import { shippingAddressSchema } from './ShippingAddress.schemas';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { userId: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));
jest.mock('react-native-keyboard-aware-scroll-view');

const MOCKS = [
  {
    request: {
      query: UPDATE_SUBSCRIPTION_PREFERENCE,
      variables: {
        userId: mockUseSettingsObj.user.userId,
        cardPreference: 'fob',
        addressDetails: {
          addressLine: `1000 Street`,
          addressCity: 'town',
          addressPostcode: 'AS 123',
          addressCountry: 'UK',
        },
      },
    },
    result: {},
    error: new Error('aw shucks'),
  },
];

describe('Shipping Address validation', () => {
  const address = {
    Address1: '123 fake street',
    Address2: 'fake street 2',
    Town: 'Fake Town',
    Postcode: 'FA12 9KE',
  };

  it('valid address', async () => {
    const validate =
      await shippingAddressSchema(mockTranslations).isValid(address);
    expect(validate).toEqual(true);
  });

  it('invalid postcode - special characters', async () => {
    const validate = await shippingAddressSchema(mockTranslations).isValid({
      ...address,
      Postcode: 'FA1! 9KE',
    });
    expect(validate).toEqual(false);
  });
});

describe('ShippingAddress component', () => {
  it('should render correctly from account', async () => {
    const params = { fromScreen: 'registration' };
    const { toJSON } = render(
      <Theme>
        <MockedProvider
          mocks={MOCKS}
          addTypename={false}
          defaultOptions={{
            mutate: {
              errorPolicy: 'all',
            },
          }}
        >
          <ShippingAddress route={params} />
        </MockedProvider>
      </Theme>,
    );
    expect(toJSON()).toMatchSnapshot();
  });
});
