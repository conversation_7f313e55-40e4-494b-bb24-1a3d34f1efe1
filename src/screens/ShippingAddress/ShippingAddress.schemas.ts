import * as yup from 'yup';

export const shippingAddressSchema = (t: any) => {
  yup.setLocale({});
  return yup.object().shape({
    Address1: yup
      .string()
      .max(40)
      .trim()
      .required(t.registration.validation.address.REQUIRED),
    Address2: yup.string().max(40).trim(),
    Town: yup
      .string()
      .max(40)
      .trim()
      .required(t.registration.validation.city.REQUIRED),
    Postcode: yup
      .string()
      .max(8)
      .min(5)
      .trim()
      .matches(
        /^[a-z]{1,2}\d[a-z\d]?\s*\d[a-z]{2}$/i,
        t.registration.validation.postcode.VALID,
      )
      .required(t.registration.validation.postcode.REQUIRED),
  });
};
