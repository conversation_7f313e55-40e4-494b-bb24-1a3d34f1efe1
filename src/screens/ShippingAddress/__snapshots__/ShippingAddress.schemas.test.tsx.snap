// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ShippingAddress component should render correctly from account 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 0,
    }
  }
>
  <View
    style={
      {
        "backgroundColor": "#f5f5f5",
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
      }
    }
  >
    <View
      onLayout={[Function]}
      style={
        [
          {
            "flex": 1,
          },
          {
            "paddingBottom": 0,
          },
        ]
      }
    >
      <RCTScrollView
        contentContainerStyle={
          {
            "flexGrow": 1,
          }
        }
        keyboardShouldPersistTaps="always"
        style={
          {
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "overflow": "scroll",
            "paddingLeft": 16,
            "paddingRight": 16,
            "paddingTop": 44,
          }
        }
      >
        <View />
      </RCTScrollView>
    </View>
  </View>
</RCTSafeAreaView>
`;
