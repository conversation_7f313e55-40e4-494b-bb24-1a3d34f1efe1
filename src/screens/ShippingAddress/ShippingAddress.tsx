import { useMutation } from '@apollo/client';
import {
  <PERSON>ton,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import Spinner from '@bp/ui-components/mobile/stages/Atoms/Spinner/Spinner';
import React, { useContext, useEffect, useState } from 'react';
import { KeyboardAvoidingView, Platform } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

import { SubsAnalyticsEventEnterAddressScreenOpen } from '../../analytics/events';
import { UPDATE_SUBSCRIPTION_PREFERENCE } from '../../client/queries';
import { AppRoute, SubscriptionScreenNames } from '../../common/enums';
import {
  IUpdateSubscriptionPrefRequest,
  IUpdateSubscriptionPrefResponse,
} from '../../common/interfaces';
import { Spacer, TextInput } from '../../components';
import {
  AddressContext,
  useAddressContext,
} from '../../providers/AddressProvider';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { getAnalyticsPayload } from '../../utils/helpers';
import runValidation from '../../utils/runValidation';
import { shippingAddressSchema } from './ShippingAddress.schemas';
import * as S from './ShippingAddress.styles';

export default ({ route }: any) => {
  const { navigate } = useHostNavigation();
  const [address1, setAddress1] = useState<string>('');
  const [address1Error, setAddress1Error] = useState(false);
  const [address2, setAddress2] = useState<string>('');
  const [address2Error, setAddress2Error] = useState(false);
  const [town, setTown] = useState<string>('');
  const [townError, setTownError] = useState(false);
  const [postCode, setPostCode] = useState<string>('');
  const [postCodeError, setPostCodeError] = useState(false);
  const { setAddressCtx } = useAddressContext();
  const { params } = route;
  const fromScreen =
    params && params.fromScreen ? params.fromScreen : AppRoute.registration;
  const { t, onAnalyticsEvent } = useSettings();
  const { rfidType } = useContext(AddressContext);
  const { userInfo } = useUserInfo();

  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventEnterAddressScreenOpen(getAnalyticsPayload(userInfo)),
    );
  }, [onAnalyticsEvent, userInfo]);

  // Update user subscription preferences
  const [updateSubscriptionPreferencesMutation, { loading, error }] =
    useMutation<
      IUpdateSubscriptionPrefResponse,
      IUpdateSubscriptionPrefRequest
    >(UPDATE_SUBSCRIPTION_PREFERENCE, {
      onCompleted: () => {
        navigate(SubscriptionScreenNames.VoucherScreen, {
          from: fromScreen,
          logSubsFlow: true,
        });
      },
    });

  const shippingAddressValidation = {
    schema: shippingAddressSchema(t),
    fields: {
      Address1: {
        value: address1,
        setError: setAddress1Error,
      },
      Address2: {
        value: address2,
        setError: setAddress2Error,
      },
      Town: {
        value: town,
        setError: setTownError,
      },
      Postcode: {
        value: postCode,
        setError: setPostCodeError,
      },
    },
  };

  const onPressUpdateAddress = async () => {
    const isValid = await runValidation(shippingAddressValidation);
    if (!isValid) {
      return;
    }
    setAddressCtx(address1, address2, town, postCode);
    await updateSubscriptionPreferencesMutation({
      variables: {
        userId: userInfo?.userId || '',
        cardPreference: rfidType,
        addressDetails: {
          addressLine: `${address1} ${address2}`.trim(),
          addressCity: town,
          addressPostcode: postCode,
          addressCountry: 'UK', //since user is not giving this input, should this be derived in backend based on zipcode?
        },
      },
    });
  };

  const handlePostalCode = (value: string) => {
    if (postCodeError) {
      setPostCodeError(false);
    }
    setPostCode(value);
  };

  return (
    <S.ScreenWrapper>
      <>
        <S.ShippingAddressContainer>
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{ flex: 1 }}
          >
            <S.InnerViewContainer keyboardShouldPersistTaps="always">
              <KeyboardAwareScrollView keyboardShouldPersistTaps="always">
                <S.TitleText>
                  {t.gocardless.text.SHIPPING_ADDRESS_TXT}
                </S.TitleText>
                <S.MiddleViewContainer>
                  <Spacer vSpace={20} />
                  <TextInput
                    label="Address Line 1*"
                    labelTextBold
                    placeholder="e.g. 123 Smith St"
                    onChangeText={(value: string) => setAddress1(value)}
                    value={address1}
                    error={address1Error}
                    autoCapitalize="none"
                    autoCorrect={false}
                    textContentType="streetAddressLine1"
                    accessibilityLabel="AddressLabel1"
                  />
                  <Spacer vSpace={20} />
                  <TextInput
                    label="Address Line 2"
                    labelTextBold
                    placeholder=""
                    onChangeText={(value: string) => setAddress2(value)}
                    value={address2}
                    error={address2Error}
                    autoCapitalize="none"
                    autoCorrect={false}
                    textContentType="streetAddressLine2"
                    accessibilityLabel="AddressLabel2"
                  />
                  <Spacer vSpace={20} />
                  <TextInput
                    label="Town or City*"
                    labelTextBold
                    placeholder="e.g. London"
                    onChangeText={(value: string) => setTown(value)}
                    value={town}
                    error={townError}
                    autoCapitalize="none"
                    autoCorrect={false}
                    textContentType="addressCity"
                    accessibilityLabel="town"
                  />
                  <Spacer vSpace={20} />
                  <TextInput
                    label="Post Code*"
                    labelTextBold
                    placeholder="e.g. W2 5SJ"
                    onChangeText={handlePostalCode}
                    value={postCode}
                    error={postCodeError}
                    autoCapitalize="none"
                    autoCorrect={false}
                    textContentType="postalCode"
                    accessibilityLabel="Postcode"
                  />
                  <Spacer vSpace={20} />
                </S.MiddleViewContainer>
                <S.ActionButton>
                  {error && (
                    <S.ErrorText>{t.gocardless.replacecard.ERROR}</S.ErrorText>
                  )}
                  <Button
                    type={ButtonAction.PRIMARY}
                    onPress={onPressUpdateAddress}
                    disabled={
                      address1.length === 0 ||
                      town.length === 0 ||
                      postCode.length === 0 ||
                      loading
                    }
                    accessibilityLabel="ShippingAddressContinue Button"
                    testID="ShippingAddressContinue"
                    accessibilityHint="ShippingAddressContinue"
                    size={ButtonSize.DEFAULT}
                  >
                    {t.gocardless.buttons.CONTINUE}
                  </Button>
                </S.ActionButton>
              </KeyboardAwareScrollView>
            </S.InnerViewContainer>
          </KeyboardAvoidingView>
        </S.ShippingAddressContainer>
        {<Spinner data-testid="Spinner" size="large" active={loading} />}
      </>
    </S.ScreenWrapper>
  );
};
