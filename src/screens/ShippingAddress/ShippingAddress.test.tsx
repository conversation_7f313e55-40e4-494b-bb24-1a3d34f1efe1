import { MockedProvider } from '@apollo/client/testing';
import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { ScrollView as MockScrollView } from 'react-native';

import {
  GET_USER_INFO,
  UPDATE_SUBSCRIPTION_PREFERENCE,
} from '../../client/queries';
import { AddressContextProvider } from '../../providers/AddressProvider';
import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import ShippingAddress from './ShippingAddress';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { userId: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));
jest.mock('react-native-keyboard-aware-scroll-view', () => ({
  KeyboardAwareScrollView: (props: any) => <MockScrollView {...props} />,
}));

const mockNavigate = jest.fn();
jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => {
    return {
      navigate: mockNavigate,
      navigation: {
        getState: jest.fn(),
      },
    };
  },
}));

const mockUseUserInfoObj = {
  userInfo: {
    userId: '123',
    gocardless: { mandateStatus: 'active' },
    balance: '1.00',
    tagIds: [],
  },
  setUserInfo: jest.fn(),
};
const mockUseUserInfo = jest.fn().mockReturnValue(mockUseUserInfoObj);
jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUseUserInfo(),
}));

const MOCKS = [
  {
    request: {
      query: GET_USER_INFO,
      variables: {
        userId: mockUseSettingsObj.user.userId,
      },
    },
    result: {
      data: {
        userInfo: {
          type: 'PAYG',
        },
      },
    },
  },
  {
    request: {
      query: UPDATE_SUBSCRIPTION_PREFERENCE,
      variables: {
        userId: '123',
        cardPreference: '',
        addressDetails: {
          addressLine: '73 Holburn Street',
          addressCity: 'Aberdeen',
          addressPostcode: 'AB10 6DN',
          addressCountry: 'UK',
        },
      },
    },
    result: {
      data: {
        updateSubscriptionPreference: {
          status: '',
          payload: {
            code: '',
            error: '',
            eventDetails: '',
            eventTime: '',
            message: '',
            salesforceId: '',
          },
        },
      },
    },
  },
];

const renderWithTheme = () => {
  const params = { from: 'registration' };
  return render(
    <Theme>
      <MockedProvider mocks={MOCKS}>
        <AddressContextProvider>
          <ShippingAddress route={params} />
        </AddressContextProvider>
      </MockedProvider>
    </Theme>,
  );
};

describe('Shipping Address', () => {
  it('should render screen', () => {
    const { toJSON } = renderWithTheme();
    expect(toJSON()).toBeTruthy();
  });

  it('should update address on button press', async () => {
    const { getByLabelText, getByText } = renderWithTheme();
    fireEvent.changeText(getByLabelText('AddressLabel1'), '73');
    fireEvent.changeText(getByLabelText('AddressLabel2'), 'Holburn Street');
    fireEvent.changeText(getByLabelText('town'), 'Aberdeen');
    fireEvent.changeText(getByLabelText('Postcode'), 'AB10 6DN');
    fireEvent.press(getByText(mockTranslations.gocardless.buttons.CONTINUE));

    await waitFor(() => expect(mockNavigate).toHaveBeenCalled());
  });
});
