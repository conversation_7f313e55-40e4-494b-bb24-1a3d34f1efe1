import { ThemeProvider } from '@bp/ui-components/mobile';
import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import { TRANSLATIONS } from '../../translations';
import { updateTranslations } from '../../utils/updateTranslations';
import PaymentSuccess from './PaymentSuccess';

const mockTranslations = updateTranslations(TRANSLATIONS.en_GB, {
  brand: 'bp',
});
const mockUseSettingsObj = {
  t: mockTranslations,
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

const renderWithTheme = () =>
  render(
    <ThemeProvider>
      <PaymentSuccess />
    </ThemeProvider>,
  );

describe('Payment Failed Screen', () => {
  it('renders correctly', () => {
    const PaymentSuccessScreen = renderWithTheme();
    expect(PaymentSuccessScreen.toJSON()).toMatchSnapshot();
  });

  it('should trigger navigation when button is pressed', () => {
    const PaymentSuccessScreen = renderWithTheme();

    const button = PaymentSuccessScreen.getByTestId('Finish.Button');

    fireEvent(button, 'onPress');

    expect(mockHostNavigationObj.navigate).toHaveBeenCalled();
  });
});
