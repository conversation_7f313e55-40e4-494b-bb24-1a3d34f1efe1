import {
  Button,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import React from 'react';

import PaymentSuccessImage from '../../../assets/images/subscription-success.png';
import { SubscriptionScreenNames } from '../../common/enums';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { getCountrySpecificText } from '../../utils/helpers';
import * as S from './PaymentSuccess.styles';

const PaymentSuccess = () => {
  const { t, user } = useSettings();
  const { navigate } = useHostNavigation();

  const onPress = () => {
    navigate(
      SubscriptionScreenNames.SubscriptionLanding,
      {
        screen: SubscriptionScreenNames.MyComfortMembership,
      },
      true,
    );
  };

  return (
    <S.ScreenContainer>
      <S.ScreenScrollContainer>
        <S.TopContainer>
          <S.SuccessHeaderText>
            {getCountrySpecificText(
              t,
              'gocardless.text.PAYMENT_SUCCESS_HEADER',
              user?.country,
            )}
          </S.SuccessHeaderText>

          <S.SuccessImage source={PaymentSuccessImage} />

          <S.SuccessMessage>
            {t.gocardless.text.PAYMENT_SUCCESS_MESSAGE}
          </S.SuccessMessage>
        </S.TopContainer>
        <S.ButtonView>
          <Button
            type={ButtonAction.PRIMARY}
            size={ButtonSize.DEFAULT}
            disabled={false}
            onPress={onPress}
            testID="Finish.Button"
            accessibilityLabel={t.gocardless.accessibility.FINISH_BUTTON.LABEL}
            accessibilityHint={t.gocardless.accessibility.FINISH_BUTTON.HINT}
          >
            {t.gocardless.buttons.FINISH_BTN}
          </Button>
        </S.ButtonView>
      </S.ScreenScrollContainer>
    </S.ScreenContainer>
  );
};

export default PaymentSuccess;
