// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Payment Failed Screen renders correctly 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 1,
    }
  }
>
  <RCTScrollView
    contentContainerStyle={
      {
        "flexGrow": 1,
        "justifyContent": "space-between",
        "marginLeft": 24,
        "marginRight": 24,
        "marginTop": 32,
        "paddingTop": 0,
      }
    }
    style={{}}
  >
    <View>
      <View
        style={
          {
            "alignItems": "center",
          }
        }
      >
        <Text
          style={
            {
              "color": "rgb(17, 17, 17)",
              "fontFamily": "Roboto-Light",
              "fontSize": 28,
              "letterSpacing": 0.5,
              "lineHeight": 40,
              "marginBottom": 16,
              "textAlign": "center",
            }
          }
        >
          Success! Subscription payment complete.
        </Text>
        <Image
          source={
            {
              "testUri": "../../../assets/images/subscription-success.png",
            }
          }
          style={
            {
              "height": 266,
              "width": 248,
            }
          }
        />
        <Text
          style={
            {
              "color": "rgb(17, 17, 17)",
              "fontFamily": "Roboto-Light",
              "fontSize": 18,
              "letterSpacing": 0.2,
              "lineHeight": 32,
              "marginTop": 32,
              "textAlign": "center",
            }
          }
        >
          Payment successful!
Enjoy your discounted charging rate.
        </Text>
      </View>
      <View
        style={
          {
            "paddingBottom": 32,
          }
        }
      >
        <View
          accessibilityHint="Finish"
          accessibilityLabel="Finish"
          accessibilityRole="button"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": false,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#000096",
              "borderBottomLeftRadius": 23,
              "borderBottomRightRadius": 23,
              "borderColor": "transparent",
              "borderStyle": "solid",
              "borderTopLeftRadius": 23,
              "borderTopRightRadius": 23,
              "borderWidth": 0,
              "justifyContent": "center",
              "minHeight": 46,
              "opacity": 1,
              "paddingHorizontal": 25.5,
              "paddingVertical": 12,
            }
          }
          testID="Finish.Button"
        >
          <View>
            <Text
              disabled={false}
              inverted={false}
              size="large"
              style={
                {
                  "color": "#ffffff",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 15,
                  "letterSpacing": 0.7,
                  "textAlign": "center",
                }
              }
              type="primary"
            >
              Finish
            </Text>
          </View>
        </View>
      </View>
    </View>
  </RCTScrollView>
</RCTSafeAreaView>
`;
