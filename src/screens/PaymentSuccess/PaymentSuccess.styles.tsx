import { Platform, StatusBar } from 'react-native';
import { styled } from 'styled-components/native';

export const ScreenScrollContainer = styled.ScrollView.attrs(() => ({
  contentContainerStyle: {
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
    marginTop: 32,
    marginLeft: 24,
    marginRight: 24,
    flexGrow: 1,
    justifyContent: 'space-between',
  },
}))``;

export const ButtonView = styled.View`
  padding-bottom: ${Platform.OS === 'ios' ? 32 : 42};
`;

export const TopContainer = styled.View`
  align-items: center;
`;

export const ScreenContainer = styled.SafeAreaView`
  flex: 1;
`;

export const SuccessHeaderText = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 28px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.5px;
  line-height: 40px;
  text-align: center;
  margin-bottom: 16px;
`;

export const SuccessImage = styled.Image`
  width: 248px;
  height: 266px;
`;

export const SuccessMessage = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 18px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.2px;
  line-height: 32px;
  text-align: center;
  margin-top: 32px;
`;
