import { theme as PulseTheme } from '@bp/ui-components/mobile/core/themes/pulse';
import { createStackNavigator } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import { StatusBar } from 'react-native';

import { SubscriptionScreenNames, UserTypeEnum } from '../common/enums';
import { AppStatusBar, BackButton } from '../components';
import HeaderBackButton from '../components/Atoms/HeaderBackButton/HeaderBackButton';
import MultilineHeader from '../components/Molecules/MultilineHeader/MultilineHeader';
import { useHostNavigation } from '../providers/HostNavigationProvider';
import { useSettings } from '../providers/Settings';
import { useSubscriptionContext } from '../providers/SubscriptionProvider';
import { useUserInfo } from '../providers/UserInfoProvider';
import Theme from '../themes/Theme';
import AdacCancelSubscriptionConfirmation from './AdacCancelSubscriptionConfirmation/AdacCancelSubscriptionConfirmation';
import CancelMembership from './CancelMembership/CancelMembership';
import CancelSubscriptionConfirmation from './CancelSubscriptionConfirmation/CancelSubscriptionConfirmation';
import CancelSubscriptionFailed from './CancelSubscriptionFailed/CancelSubscriptionFailed';
import ConfirmCancelComfortMembership from './ConfirmCancelComfortMembership/ConfirmCancelComfortMembership';
import { ConfirmSubscription } from './ConfirmSubscription/ConfirmSubscription';
import ExpiredOffers from './ExpiredOffers/ExpiredOffers';
import GocardlessForm from './GocardlessForm/Gocardless';
import Loading from './Loading/Loading';
import MyComfortMembership from './MyComfortMembership/MyComfortMembership';
import MyComfortMembershipActivation from './MyComfortMembershipActivation/MyComfortMembershipActivation';
import PaymentFailed from './PaymentFailed/PaymentFailed';
import PaymentRequired from './PaymentRequired/PaymentRequired';
import PaymentSuccess from './PaymentSuccess/PaymentSuccess';
import PendingMembership from './PendingMembership/PendingMembership';
import RFIDForm from './RFIDForm/RFIDForm';
import ShippingAddress from './ShippingAddress/ShippingAddress';
import { SubscriptionEnding } from './SubscriptionEnding/SubscriptionEnding';
import SubscriptionFailed from './SubscriptionFailed/SubscriptionFailed';
import SubscriptionLandingScreen from './SubscriptionLanding/SubscriptionLanding';
import SubscriptionSuccess from './SubscriptionSuccess/SubscriptionSuccess';
import VoucherScreen from './VoucherScreen/VoucherScreen';
import WelcomeScreen from './WelcomeScreen/WelcomeScreen';

const Stack = createStackNavigator();

const backButtonFn = () => <BackButton />;

const Screens = ({ route }: any) => {
  const { params } = route ?? {};
  const { navigation, navigate } = useHostNavigation();
  const { userInfo } = useUserInfo();
  const { t, onNavigateToProfile } = useSettings();
  const [currentRoute, setRoute] = useState<string | undefined>(undefined);
  const [prevRoute, setPrevRoute] = useState<string | undefined>(undefined);
  const [initialRouteName, setInitialRouteName] = useState<string>(
    SubscriptionScreenNames.SubscriptionLanding,
  );
  const { isPausedModalVisible, isSubscriptionPaused } =
    useSubscriptionContext();

  const theme = PulseTheme;

  useEffect(() => {
    if (params) {
      const routeName = params?.screen;
      setInitialRouteName(routeName);
    }
  }, [params]);

  useEffect(() => {
    const routes = navigation.getState()?.routes;
    const numberOfRoutes = routes?.length || 1;
    const currentRouteIndex = numberOfRoutes - 1;
    const previousRouteIndex = numberOfRoutes - 2;

    if (previousRouteIndex >= 0) {
      setPrevRoute(routes[previousRouteIndex]?.name);
    }

    setRoute(routes?.[currentRouteIndex]?.name);
  }, [navigation]);

  return (
    <>
      <AppStatusBar
        barStyle={
          currentRoute === SubscriptionScreenNames.JoinGoCardless
            ? 'light-content'
            : 'dark-content'
        }
      />
      <Theme customTheme={theme}>
        <Stack.Navigator
          initialRouteName={initialRouteName}
          screenOptions={{
            cardStyle: { backgroundColor: '#ffffff' },
            headerTitleStyle: {
              fontWeight: 'normal',
              fontFamily: 'arial',
            },
          }}
        >
          <Stack.Screen
            name={SubscriptionScreenNames.SubscriptionLanding}
            component={SubscriptionLandingScreen}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.RFIDForm}
            component={RFIDForm}
            options={{
              title:
                prevRoute === 'replaceCard'
                  ? 'Replace card or key fob'
                  : 'Access method',
              headerBackImage: () => backButtonFn(),
              headerBackTitleVisible: false,
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.PaymentRequired}
            component={PaymentRequired}
            options={{
              title: 'Payment Required',
              headerLeft: () =>
                HeaderBackButton({
                  navigation,
                  accessibilityLabel: t.gocardless.text.GO_BACK,
                }),
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.ShippingAddress}
            component={ShippingAddress}
            options={{
              title: 'Shipping address ',
              headerBackImage: () => backButtonFn(),
              headerBackTitleVisible: false,
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.VoucherScreen}
            component={VoucherScreen}
            options={{
              title: 'Add offer code',
              headerBackImage: () => backButtonFn(),
              headerBackTitleVisible: false,
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.GoCardlessForm}
            component={GocardlessForm}
            options={{
              title: 'Set up direct debit',
              headerBackImage: () => backButtonFn(),
              headerBackTitleVisible: false,
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.WelcomeScreen}
            component={WelcomeScreen}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.CancelMembership}
            component={CancelMembership}
            options={{
              title:
                userInfo?.type === UserTypeEnum.SUBS_WALLET
                  ? t.cancelSubscription.TITLE
                  : t.gocardless.text.CANCEL_SUBS_TITLE,
              headerLeft: () =>
                HeaderBackButton({
                  navigation,
                  setModalVisible: isPausedModalVisible,
                  color: null,
                  fromPaused: isSubscriptionPaused,
                  accessibilityLabel: t.gocardless.text.GO_BACK,
                }),
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.CancelSubscriptionConfirmation}
            component={CancelSubscriptionConfirmation}
            options={{
              headerBackTitleVisible: false,
              headerLeft: () => null,
              headerRight: () => null,
              headerShadowVisible: false,
              title: '',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.PendingMembership}
            component={PendingMembership}
            options={{
              title: 'Pending Membership',
              headerLeft: () =>
                HeaderBackButton({
                  navigation,
                  accessibilityLabel: t.gocardless.text.GO_BACK,
                }),
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.ExpiredOffers}
            component={ExpiredOffers}
            options={{
              title: 'Expired Offers',
              headerLeft: () =>
                HeaderBackButton({
                  navigation,
                  accessibilityLabel: t.gocardless.text.GO_BACK,
                }),
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.ConfirmSubscription}
            component={ConfirmSubscription}
            options={{
              title: 'Confirm',
              headerLeft: () =>
                HeaderBackButton({
                  navigation,
                  accessibilityLabel: t.gocardless.text.GO_BACK,
                }),
              headerTitleAlign: 'center',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.SubscriptionFailed}
            component={SubscriptionFailed}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.CancelSubscriptionFailed}
            component={CancelSubscriptionFailed}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.SubscriptionSuccess}
            component={SubscriptionSuccess}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.SubscriptionEnding}
            component={SubscriptionEnding}
            options={{
              title: t.mySubscription.HEADER,
              headerStyle: {
                backgroundColor: '#10109a',
                shadowColor: 'transparent',
              },
              headerTitleAlign: 'center',
              headerTintColor: '#fff',
              headerLeft: () =>
                HeaderBackButton({
                  navigation,
                  customOnPress: onNavigateToProfile,
                  color: '#fff',
                }),
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.PaymentSuccess}
            component={PaymentSuccess}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.PaymentFailed}
            component={PaymentFailed}
            options={{
              headerShown: false,
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.AdacCancelSubsConfirmation}
            component={AdacCancelSubscriptionConfirmation}
            options={{
              headerBackTitleVisible: false,
              headerLeft: () => null,
              headerRight: () => null,
              headerShadowVisible: false,
              title: '',
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.ConfirmCancelComfortMembership}
            component={ConfirmCancelComfortMembership}
            options={{
              cardStyle: {
                marginTop: StatusBar.currentHeight,
                backgroundColor: '#ffffff',
              },
              header: () =>
                MultilineHeader({
                  title: t.confirmCancelComfortMembership.header,
                  LeftButton: (
                    <HeaderBackButton
                      navigation={navigation}
                      customOnPress={() =>
                        navigate(SubscriptionScreenNames.MyComfortMembership)
                      }
                      accessibilityLabel={t.gocardless.text.GO_BACK}
                    />
                  ),
                }),
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.Loading}
            component={Loading}
            options={() => ({
              headerShown: false,
            })}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.MyComfortMembership}
            component={MyComfortMembership}
            options={{
              title: t.myComfortMembership.header,
              headerStyle: {
                backgroundColor: '#10109a',
                shadowColor: 'transparent',
              },
              headerTitleAlign: 'center',
              headerTintColor: '#fff',
              headerLeft: () =>
                HeaderBackButton({
                  navigation,
                  customOnPress: onNavigateToProfile,
                  color: '#fff',
                }),
            }}
          />
          <Stack.Screen
            name={SubscriptionScreenNames.MyComfortMembershipActivation}
            component={MyComfortMembershipActivation}
            options={{
              headerShadowVisible: false,
              title: t.myComfortMembership.myComfortMembershipActivation.header,
              headerTitleStyle: {
                fontSize: 20,
                fontWeight: 'normal',
              },
              headerBackImage: () => backButtonFn(),
              headerBackTitleVisible: false,
              headerTitleAlign: 'center',
            }}
          />
        </Stack.Navigator>
      </Theme>
    </>
  );
};

export default Screens;
