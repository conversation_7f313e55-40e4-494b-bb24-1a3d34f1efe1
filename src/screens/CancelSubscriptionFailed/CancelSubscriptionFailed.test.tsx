import { SupportedBrand } from '@bp/pulse-shared-types/lib/enums/SupportedBrands';
import { ThemeProvider } from '@bp/ui-components/mobile';
import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';

import * as TRANSLATIONS from '../../translations/messages.json';
import { updateTranslations } from '../../utils/updateTranslations';
import CancelSubscriptionFailed from './CancelSubscriptionFailed';

let mockTranslations = updateTranslations(TRANSLATIONS, {
  brand: 'bp',
});
const mockUseSettingsObj = {
  t: mockTranslations,
  onAnalyticsEvent: () => {},
  brand: SupportedBrand.BP,
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));
jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => ({ userInfo: { country: 'UK' } }),
}));
jest.mock('../../utils/helpers', () => ({
  getPhoneNumber: jest.fn().mockReturnValue('test-number'),
}));
const mockHostNavigationObj = {
  navigate: jest.fn(),
  navigation: { goBack: jest.fn() },
};

jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => mockHostNavigationObj,
}));

const mockRoute = {
  params: {
    message: 'Test error message',
  },
};

const renderWithTheme = () =>
  render(
    <ThemeProvider>
      <CancelSubscriptionFailed route={mockRoute} />
    </ThemeProvider>,
  );

beforeEach(() => jest.clearAllMocks());

describe('Cancel Subscription Failed Screen', () => {
  it('renders without crashing', () => {
    const SubscriptionFailedScreen = renderWithTheme();
    expect(SubscriptionFailedScreen.toJSON()).toMatchSnapshot();
  });

  it('renders button with accessibility label', async () => {
    const { getByLabelText } = renderWithTheme();
    await waitFor(() => {
      const button = getByLabelText(
        mockTranslations.gocardless.buttons.GOBACKANDTRYAGAIN,
      );
      expect(button).toBeDefined();
    });
  });

  it('should render text on the page', () => {
    const { getByText } = renderWithTheme();
    expect(
      getByText(mockTranslations.gocardless.text.SOMETHING_WENT_WRONG),
    ).toBeTruthy();
    expect(
      getByText(mockTranslations.gocardless.text.CANCEL_MEMBERSHIP_ERROR_TEXT),
    ).toBeTruthy();
    expect(
      getByText(
        mockTranslations.gocardless.text.CANCEL_MEMBERSHIP_TROUBLESHOOTING_TEXT,
      ),
    ).toBeTruthy();
  });
  it('should navigate to RFID form', () => {
    const SubscriptionFailedScreen = renderWithTheme();

    const setupButton = SubscriptionFailedScreen.getByTestId(
      'GoBackAndTryAgain.Button',
    );

    fireEvent(setupButton, 'onPress');

    expect(mockHostNavigationObj.navigate).toHaveBeenCalled();
  });

  it('should render Aral text on the page when the brand is ARAL', () => {
    const aralTranslations = updateTranslations(TRANSLATIONS, {
      brand: 'aral',
    });

    mockUseSettings.mockReturnValueOnce({
      t: aralTranslations,
      onAnalyticsEvent: () => {},
      brand: SupportedBrand.ARAL,
    });

    const { getByText } = renderWithTheme();

    expect(
      getByText(aralTranslations.gocardless.text.SOMETHING_WENT_WRONG),
    ).toBeTruthy();
    expect(
      getByText(
        aralTranslations.myComfortMembership.cancelMembershipFailed.description,
      ),
    ).toBeTruthy();
    expect(
      getByText(
        aralTranslations.myComfortMembership.cancelMembershipFailed
          .troubleshooting,
      ),
    ).toBeTruthy();
  });
  it('should navigate back to the previous screen form', () => {
    const SubscriptionFailedScreen = renderWithTheme();

    const setupButton = SubscriptionFailedScreen.getByTestId('CloseButton');

    fireEvent(setupButton, 'onPress');

    expect(mockHostNavigationObj.navigate).toHaveBeenCalled();
  });
});
