import { SupportedBrand } from '@bp/pulse-shared-types/lib/enums/SupportedBrands';
import {
  Button,
  ButtonAction,
  ButtonSize,
  TouchableOpacity,
} from '@bp/ui-components/mobile/core';
import React, { useEffect } from 'react';

import SubscriptionFailedImage from '../../../assets/images/subscription-failed.png';
import {
  SubsAnalyticsEventSubsCancelSubscriptionFailed,
  SubsAnalyticsEventSubsUnpaidSubsDowngradeFailure,
} from '../../analytics/events';
import { SubscriptionScreenNames } from '../../common/enums';
import { CloseIcon } from '../../components';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useSubscriptionContext } from '../../providers/SubscriptionProvider';
import * as S from './CancelSubscriptionFailed.styles';

const CancelSubscriptionFailed = ({ route }: any) => {
  const { t, onAnalyticsEvent, brand } = useSettings();

  const { isSubscriptionPaused } = useSubscriptionContext();
  const { params } = route;
  const { navigate } = useHostNavigation();
  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventSubsCancelSubscriptionFailed({
        error_message: params?.message,
      }),
    );
    if (isSubscriptionPaused) {
      onAnalyticsEvent(
        SubsAnalyticsEventSubsUnpaidSubsDowngradeFailure({
          error_message: params?.message,
        }),
      );
    }
  }, [isSubscriptionPaused, onAnalyticsEvent, params]);

  const onCloseIcon = () => {
    navigate(SubscriptionScreenNames.SubscriptionLanding, {
      screen: SubscriptionScreenNames.MySubscription,
    });
  };
  const onPressGoBack = () => {
    const navigateBackScreen =
      params.navigateBackScreen || SubscriptionScreenNames.CancelMembership;
    navigate(navigateBackScreen, {
      from: SubscriptionScreenNames.CancelSubscriptionFailed,
    });
  };
  return (
    <S.ScreenWrapper>
      <S.CloseButtonContainer>
        <S.CloseButton>
          <TouchableOpacity onPress={onCloseIcon} testID="CloseButton">
            <CloseIcon color="#000" />
          </TouchableOpacity>
        </S.CloseButton>
      </S.CloseButtonContainer>

      <S.IllustrationView>
        <S.HeaderText testID="Header.Text">
          {t.gocardless.text.SOMETHING_WENT_WRONG}
        </S.HeaderText>
      </S.IllustrationView>
      <S.StyledIllustrationView>
        <S.StyledIllustration source={SubscriptionFailedImage} />
      </S.StyledIllustrationView>

      <S.SubscriptionHasNotStartedContainer>
        <S.SubNotStartedText testID="SubscriptionHasNotCancelled.Label">
          {brand === SupportedBrand.ARAL
            ? `${t.myComfortMembership.cancelMembershipFailed.description}`
            : `${t.gocardless.text.CANCEL_MEMBERSHIP_ERROR_TEXT} `}
        </S.SubNotStartedText>
      </S.SubscriptionHasNotStartedContainer>

      <S.TroubleShootingContainer>
        <S.TroubleShootingText testID="Troubleshooting.View">
          {brand === SupportedBrand.ARAL
            ? `${t.myComfortMembership.cancelMembershipFailed.troubleshooting}`
            : `${t.gocardless.text.CANCEL_MEMBERSHIP_TROUBLESHOOTING_TEXT} `}
        </S.TroubleShootingText>
      </S.TroubleShootingContainer>

      <S.ButtonContainer>
        <Button
          type={ButtonAction.PRIMARY}
          disabled={false}
          onPress={onPressGoBack}
          size={ButtonSize.DEFAULT}
          testID="GoBackAndTryAgain.Button"
          accessibilityLabel={
            t.gocardless.accessibility.GOBACKANDTRYAGAIN.LABEL
          }
          accessibilityHint={t.gocardless.accessibility.GOBACKANDTRYAGAIN.HINT}
        >
          {brand === SupportedBrand.ARAL
            ? `${t.myComfortMembership.cancelMembershipFailed.button}`
            : `${t.gocardless.buttons.GOBACKANDTRYAGAIN}`}
        </Button>
      </S.ButtonContainer>
    </S.ScreenWrapper>
  );
};

export default CancelSubscriptionFailed;
