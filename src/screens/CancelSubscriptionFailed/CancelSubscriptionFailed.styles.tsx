import { Platform, StatusBar } from 'react-native';
import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
  padding-top: ${Platform.OS === 'android'
    ? `${StatusBar.currentHeight}px`
    : '0px'};
`;

export const HeaderText = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 28px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.5px;
  line-height: 40px;
`;

export const IllustrationView = styled.View`
  flex: 0.1;
  align-items: center;
  margin-bottom: 26px;
  margin-left: 24px;
  margin-right: 24px;
`;

export const SubNotStartedText = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 18px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.2px;
  line-height: 32px;
  text-align: center;
`;

export const TroubleShootingText = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 13px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.25px;
  line-height: 23px;
`;

export const CloseButtonContainer = styled.View`
  align-items: flex-end;
`;

export const CloseButton = styled.TouchableOpacity`
  padding: 16px;
`;

export const StyledIllustration = styled.Image`
  width: 240px;
  height: 260px;
`;

export const StyledIllustrationView = styled.View`
  flex: 0.5;
  align-items: center;
  margin-left: 87px;
  margin-right: 87px;
`;

export const SubscriptionHasNotStartedContainer = styled.View`
  flex: 0.3;
  margin-left: 24px;
  margin-right: 24px;
`;

export const TroubleShootingContainer = styled.View`
  flex: 0.15;
  margin-top: 10px;
  margin-left: 24px;
  margin-right: 24px;
`;

export const ButtonContainer = styled.View`
  flex: 0.1;
  margin-top: 25px;
  margin-left: 24px;
  margin-right: 24px;
  margin-bottom: 24px;
`;
