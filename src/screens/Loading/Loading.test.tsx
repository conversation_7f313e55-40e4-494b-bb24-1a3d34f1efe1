import { render } from '@testing-library/react-native';
import React from 'react';

import Loading from './Loading';

jest.mock('@bp/ui-components/mobile/core', () => ({
  LoadingIndicator: 'LoadingIndicator',
}));

jest.mock('./Loading.styles', () => ({
  Container: 'View',
}));

describe('Loading Component', () => {
  it('renders correctly', () => {
    const { toJSON } = render(<Loading />);
    expect(toJSON()).toMatchSnapshot();
  });
});
