import { Platform, StatusBar } from 'react-native';
import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
  padding-top: ${Platform.OS === 'android'
    ? `${StatusBar.currentHeight}px`
    : '0px'};
`;

export const BannerContainer = styled.View`
  flex: 0.85;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
  padding-left: 16px;
  padding-right: 16px;
`;

export const SubBannerText = styled.Text`
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: left;
`;

export const BottomViewContainer = styled.View`
  flex: 0.15;
  padding-left: 16px;
  padding-right: 16px;
  justify-content: center;
  padding-bottom: 16px;
`;

export const VoucherText = styled.Text`
  height: 24px;
  width: 100%;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 0;
  line-height: 24px;
`;

export const TextWrapper = styled.View`
  font-size: 16px;
  margin-right: 16px;
  width: 100%;
`;

export const TextContainer = styled.View`
  width: 100%;
  justify-content: flex-start;
  align-items: flex-start;
`;

export const WhatButton = styled.TouchableOpacity``;

export const WhatText = styled.Text`
  height: 24px;
  color: ${(props: any) => props.theme.subscriptionMfe.voucher.color.grey};
  font-size: 14px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: right;
`;

export const VoucherTextView = styled.View`
  flex-direction: row;
  justify-content: space-around;
  padding-left: 24px;
  padding-right: 24px;
`;

export const ModalView = styled.View`
  height: 300px;
  width: 100%;
  padding-left: 16px;
  padding-right: 16px;
`;

export const ModalTitleText = styled.Text`
  height: 24px;
  width: 100%;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.banner};
  font-size: 20px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const ModalText = styled.Text`
  height: 144px;
  width: 100%;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;
