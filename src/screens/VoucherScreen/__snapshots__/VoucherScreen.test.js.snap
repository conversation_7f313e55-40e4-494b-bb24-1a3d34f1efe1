// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VoucherScreen without userId should display error for invalid vouchers 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 0,
      "paddingTop": 0,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "flexBasis": 0,
        "flexGrow": 0.85,
        "flexShrink": 1,
        "justifyContent": "flex-start",
        "paddingLeft": 16,
        "paddingRight": 16,
        "width": "100%",
      }
    }
  >
    <View
      style={
        {
          "height": 8,
          "width": 1,
        }
      }
    />
    <Text
      style={
        {
          "color": "#1D1D26",
          "fontSize": 16,
          "letterSpacing": 0,
          "lineHeight": 24,
          "textAlign": "left",
        }
      }
    >
      Enter your discount code here if you've got one. If you have multiple codes, we'll apply the best offer for you.
    </Text>
    <View
      style={
        {
          "height": 24,
          "width": 1,
        }
      }
    />
    <View
      style={
        {
          "height": 15,
          "width": 1,
        }
      }
    />
    <View
      style={
        {
          "alignItems": "flex-start",
          "justifyContent": "flex-start",
          "width": "100%",
        }
      }
    >
      <View
        style={
          {
            "flexDirection": "row",
            "justifyContent": "space-around",
            "paddingLeft": 24,
            "paddingRight": 24,
          }
        }
      >
        <Text
          accessibilityLabel="Voucher Code"
          style={
            {
              "color": "#1D1D26",
              "fontSize": 14,
              "fontWeight": "bold",
              "height": 24,
              "letterSpacing": 0,
              "lineHeight": 24,
              "width": "100%",
            }
          }
          testID="VoucherCode"
        >
          Offer code
        </Text>
        <View
          accessibilityLabel="What's this Code"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "opacity": 1,
            }
          }
          testID="WhatButton"
        >
          <Text
            style={
              {
                "color": "#8E8E92",
                "fontSize": 14,
                "height": 24,
                "letterSpacing": 0,
                "lineHeight": 24,
                "textAlign": "right",
              }
            }
          >
            What is this?
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "height": 8,
            "width": 1,
          }
        }
      />
      <View
        style={
          {
            "fontSize": 16,
            "marginRight": 16,
            "width": "100%",
          }
        }
      >
        <View
          style={
            {
              "flexBasis": 0,
              "flexGrow": 1,
              "flexShrink": 1,
              "minHeight": 57,
            }
          }
        >
          <View
            style={
              {
                "flexDirection": "row",
                "flexWrap": "wrap",
              }
            }
          />
          <View
            style={
              {
                "justifyContent": "center",
                "minHeight": 48,
              }
            }
          >
            <TextInput
              accessibilityLabel="Enter Enter Voucher Code"
              accessible={true}
              active={false}
              clearButtonEnabled={false}
              editable={true}
              error={false}
              invalid={false}
              maxLength={50}
              onBlur={[Function]}
              onChangeText={[Function]}
              onFocus={[Function]}
              placeholderTextColor="rgba(17, 17, 17, 0.65)"
              shadow={false}
              style={
                {
                  "backgroundColor": "#FFFFFF",
                  "borderBottomLeftRadius": 0,
                  "borderBottomRightRadius": 0,
                  "borderColor": "#DEDEDE",
                  "borderStyle": "solid",
                  "borderTopLeftRadius": 0,
                  "borderTopRightRadius": 0,
                  "borderWidth": 1,
                  "color": "#000000",
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "flexShrink": 1,
                  "fontSize": 16,
                  "maxHeight": 90,
                  "minHeight": 48,
                  "paddingBottom": 5,
                  "paddingLeft": 10,
                  "paddingRight": 10,
                  "paddingTop": 5,
                }
              }
              success={false}
              testID="VoucherCodeInput"
              value="fakeVoucher"
            />
          </View>
        </View>
      </View>
    </View>
  </View>
  <View
    style={
      {
        "flexBasis": 0,
        "flexGrow": 0.15,
        "flexShrink": 1,
        "justifyContent": "center",
        "paddingBottom": 16,
        "paddingLeft": 16,
        "paddingRight": 16,
      }
    }
  >
    <View
      accessibilityHint="AddVoucherSuccess"
      accessibilityLabel="AddVoucherSuccess"
      accessibilityRole="button"
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": true,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "backgroundColor": "#80b2e6",
          "borderBottomLeftRadius": 28,
          "borderBottomRightRadius": 28,
          "borderColor": "transparent",
          "borderStyle": "solid",
          "borderTopLeftRadius": 28,
          "borderTopRightRadius": 28,
          "borderWidth": 0,
          "justifyContent": "center",
          "minHeight": 56,
          "opacity": 1,
          "paddingHorizontal": 29.5,
          "paddingVertical": 15,
        }
      }
      testID="AddVoucherSuccess"
    >
      <View>
        <Text
          disabled={true}
          inverted={false}
          size="xlarge"
          style={
            {
              "color": "#ffffff",
              "fontFamily": "Roboto-Regular",
              "fontSize": 16,
              "letterSpacing": 0.7,
              "textAlign": "center",
            }
          }
          type="primary"
        >
          Apply
        </Text>
      </View>
    </View>
    <View
      style={
        {
          "height": 16,
          "width": 1,
        }
      }
    />
    <View
      accessibilityHint="Skip Button"
      accessibilityLabel="Skip Button"
      accessibilityRole="button"
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": false,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "backgroundColor": "transparent",
          "borderBottomLeftRadius": 23,
          "borderBottomRightRadius": 23,
          "borderColor": "transparent",
          "borderStyle": "solid",
          "borderTopLeftRadius": 23,
          "borderTopRightRadius": 23,
          "borderWidth": 0,
          "justifyContent": "center",
          "minHeight": 46,
          "opacity": 1,
          "paddingHorizontal": 0,
          "paddingVertical": 12,
        }
      }
      testID="SkipBtn"
    >
      <View>
        <Text
          disabled={false}
          inverted={false}
          size="large"
          style={
            {
              "color": "#0164cc",
              "fontFamily": "Roboto-Regular",
              "fontSize": 15,
              "letterSpacing": 0.7,
              "textAlign": "center",
            }
          }
          type="senary"
        >
          Skip this step
        </Text>
        <View
          style={
            {
              "paddingTop": 4,
            }
          }
        >
          <View
            backgroundColor="#0164cc"
            borderRadius={0}
            height={1}
            style={
              {
                "backgroundColor": "#0164cc",
                "borderBottomLeftRadius": 0,
                "borderBottomRightRadius": 0,
                "borderTopLeftRadius": 0,
                "borderTopRightRadius": 0,
                "height": 1,
                "width": "100%",
              }
            }
          />
        </View>
      </View>
    </View>
    <View
      style={
        {
          "height": 16,
          "width": 1,
        }
      }
    />
  </View>
</RCTSafeAreaView>
`;
