import { useLazyQuery } from '@apollo/client';
import {
  <PERSON><PERSON>,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import Spinner from '@bp/ui-components/mobile/stages/Atoms/Spinner/Spinner';
import React, { useEffect, useState } from 'react';
import { Dimensions } from 'react-native';

import {
  SubsAnalyticsEventAddOfferCodeApplyError,
  SubsAnalyticsEventAddOfferCodeApplySuccess,
  SubsAnalyticsEventAddOfferCodeScreenOpen,
} from '../../analytics/events';
import { GET_GOCARDLESS_VOUCHERS_STATUS } from '../../client/queries';
import { SubscriptionScreenNames } from '../../common/enums';
import {
  IGetVoucherStatusRequest,
  IGetVoucherStatusResponse,
} from '../../common/interfaces';
import { Modal, Spacer, TextInput } from '../../components';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { getAnalyticsPayload } from '../../utils/helpers';
import * as S from './VoucherScreen.styles';

const VoucherScreen = ({ route }: any) => {
  const { params } = route;
  const [modalVisible, setModalVisible] = useState(false);
  const [voucherCode, setVoucherCode] = useState('');
  const [VoucherError, setVoucherError] = useState<string>();
  const [voucherSuccess, setVoucherSuccess] = useState<boolean>();
  const fromScreen = params && params.from;
  const { navigate } = useHostNavigation();
  const { t, onAnalyticsEvent, isInternetReachable } = useSettings();
  const { userInfo } = useUserInfo();
  const [deactivateButton, setDeactivateButton] = useState<boolean>(true);

  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventAddOfferCodeScreenOpen(getAnalyticsPayload(userInfo)),
    );
  }, [onAnalyticsEvent, userInfo]);

  useEffect(() => {
    if (isInternetReachable) {
      setDeactivateButton(false);
    } else {
      setDeactivateButton(true);
    }
  }, [isInternetReachable]);

  const [getGocardlessVoucherStatus, { loading, data }] = useLazyQuery<
    IGetVoucherStatusResponse,
    IGetVoucherStatusRequest
  >(GET_GOCARDLESS_VOUCHERS_STATUS, {
    onCompleted: () => {
      if (data) {
        const { getVoucherStatus } = data;
        if (
          getVoucherStatus &&
          (getVoucherStatus.status === '200' || getVoucherStatus.status === 200)
        ) {
          setVoucherCode('');
          setVoucherError(undefined);
          setVoucherSuccess(true);
          onAnalyticsEvent(
            SubsAnalyticsEventAddOfferCodeApplySuccess(
              getAnalyticsPayload(userInfo),
            ),
          );
          if (fromScreen !== SubscriptionScreenNames.GoCardlessMembership) {
            navigate(SubscriptionScreenNames.GoCardlessForm, {
              from: fromScreen,
              logSubsFlow: true,
            });
          }
        } else {
          const key =
            getVoucherStatus.voucherStatus as keyof typeof t.gocardless.text;
          key
            ? setVoucherError(t.gocardless.text[key])
            : setVoucherError(t.gocardless.text.BAD_MISSING_SFID);
          setVoucherSuccess(false);
          onAnalyticsEvent(
            SubsAnalyticsEventAddOfferCodeApplyError({
              errorMessage: t.gocardless.text[key],
            }),
          );
        }
      }
    },
    onError: (error) =>
      onAnalyticsEvent(
        SubsAnalyticsEventAddOfferCodeApplyError({
          errorMessage: error.message,
        }),
      ),
  });

  return (
    <S.ScreenWrapper>
      <S.BannerContainer>
        <Spacer vSpace={8} />
        <S.SubBannerText>{t.gocardless.text.VOUCHER_BANNER}</S.SubBannerText>
        <Spacer vSpace={24} />
        <Spacer vSpace={15} />
        <S.TextContainer>
          <S.VoucherTextView>
            <S.VoucherText
              accessibilityLabel="Voucher Code"
              testID="VoucherCode"
            >
              {t.gocardless.text.OFFER_CODE}
            </S.VoucherText>
            <S.WhatButton
              onPress={() => setModalVisible(true)}
              accessibilityLabel="What's this Code"
              testID="WhatButton"
            >
              <S.WhatText>{t.gocardless.buttons.WHATISTHIS}</S.WhatText>
            </S.WhatButton>
          </S.VoucherTextView>
          <Spacer vSpace={8} />
          <S.TextWrapper>
            <TextInput
              maxLength={50}
              onChangeText={(text: string) =>
                setVoucherCode(text.replace(/[^a-zA-Z0-9]/g, ''))
              }
              value={voucherCode}
              testID="VoucherCodeInput"
              accessibilityLabel="Enter Voucher Code"
              editable={!loading}
              error={VoucherError}
              success={voucherSuccess}
              successText={
                voucherSuccess
                  ? 'Offer code has been successfully applied'
                  : undefined
              }
            />
          </S.TextWrapper>
        </S.TextContainer>
        <Spinner active={loading} />
      </S.BannerContainer>
      <S.BottomViewContainer>
        <Button
          type={ButtonAction.PRIMARY}
          disabled={!voucherCode || loading || deactivateButton}
          testID="AddVoucherSuccess"
          accessibilityLabel="AddVoucherSuccess"
          accessibilityHint="AddVoucherSuccess"
          size={ButtonSize.XLARGE}
          onPress={() =>
            getGocardlessVoucherStatus({
              variables: {
                userId: userInfo?.userId || '',
                voucherId: voucherCode,
              },
            })
          }
        >
          {t.gocardless.buttons.APPLY}
        </Button>
        {fromScreen !== SubscriptionScreenNames.GoCardlessMembership && (
          <>
            <Spacer vSpace={Dimensions.get('screen').height > 568 ? 16 : 8} />
            <Button
              type={ButtonAction.SENARY}
              testID="SkipBtn"
              accessibilityLabel="Skip Button"
              accessibilityHint="Skip Button"
              onPress={() =>
                navigate(SubscriptionScreenNames.GoCardlessForm, {
                  from: fromScreen,
                  logSubsFlow: true,
                })
              }
            >
              {t.gocardless.buttons.SKIPTHISSTEP}
            </Button>
          </>
        )}
        <Spacer vSpace={Dimensions.get('screen').height > 568 ? 16 : 8} />
      </S.BottomViewContainer>
      {!!modalVisible && (
        <Modal visible={modalVisible} setVisibleState={setModalVisible}>
          <S.ModalView>
            <Spacer vSpace={35} />
            <S.ModalTitleText>
              {t.gocardless.buttons.WHATISTHIS}
            </S.ModalTitleText>
            <Spacer />
            <S.ModalText>{t.gocardless.text.WHATISTHIS_TXT}</S.ModalText>
            <Spacer vSpace={25} />
            <Button
              type={ButtonAction.PRIMARY}
              disabled={false}
              onPress={() => {
                setModalVisible(false);
              }}
              size={ButtonSize.DEFAULT}
              testID="GotitBtn"
              accessibilityLabel="Got it Button"
              accessibilityHint="Got it Button"
            >
              {t.gocardless.buttons.GOTIT}
            </Button>
          </S.ModalView>
        </Modal>
      )}
    </S.ScreenWrapper>
  );
};
export default VoucherScreen;
