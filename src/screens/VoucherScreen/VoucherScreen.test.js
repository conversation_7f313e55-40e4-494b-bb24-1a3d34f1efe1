import { MockedProvider } from '@apollo/client/testing';
import {
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react-native';
import React from 'react';

import { GET_GOCARDLESS_VOUCHERS_STATUS } from '../../client/queries';
import { SubscriptionScreenNames } from '../../common/enums.ts';
import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import VoucherScreen from './VoucherScreen.tsx';

jest.mock('@react-navigation/native');

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { userId: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));
jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => ({ navigate: jest.fn() }),
}));

const renderWithTheme = (from, mock) =>
  render(
    <Theme>
      <MockedProvider mocks={mock} addTypename={false}>
        <VoucherScreen route={{ params: { from } }} />
      </MockedProvider>
    </Theme>,
  );

const MOCKS = [
  {
    request: {
      query: GET_GOCARDLESS_VOUCHERS_STATUS,
      variables: {
        userId: '123',
        voucherId: 'fakeVoucher',
      },
    },
    result: {
      data: {
        getVoucherStatus: {
          status: '406',
          voucherStatus: 'BAD_MISSING_SFID',
          voucherDesc: '',
          userPays: '',
          duration: '',
        },
      },
    },
  },
  {
    request: {
      query: GET_GOCARDLESS_VOUCHERS_STATUS,
      variables: {
        userId: '',
        voucherId: 'fakeVoucher',
      },
    },
    result: {
      data: {
        getVoucherStatus: {
          status: '406',
          voucherStatus: 'BAD_MISSING_SFID',
          voucherDesc: '',
          userPays: '',
          duration: '',
        },
      },
    },
  },
  {
    request: {
      query: GET_GOCARDLESS_VOUCHERS_STATUS,
      variables: {
        userId: '123',
        voucherId: 'validVoucher',
      },
    },
    result: {
      data: {
        getVoucherStatus: {
          status: '200',
          voucherStatus: 'VALID',
          voucherDesc: '',
          userPays: '',
          duration: '',
        },
      },
    },
  },
];

describe('VoucherScreen', () => {
  describe('with userId', () => {
    it('should show modal', () => {
      renderWithTheme('account', MOCKS);
      const { queryByText } = screen;
      expect(
        queryByText(mockUseSettingsObj.t.gocardless.text.WHATISTHIS_TXT),
      ).toBeNull();
      fireEvent(
        queryByText(mockUseSettingsObj.t.gocardless.buttons.WHATISTHIS),
        'onPress',
      );
      expect(
        queryByText(mockUseSettingsObj.t.gocardless.text.WHATISTHIS_TXT),
      ).toBeTruthy();
      fireEvent(
        queryByText(mockUseSettingsObj.t.gocardless.buttons.GOTIT),
        'onPress',
      );
      expect(
        queryByText(mockUseSettingsObj.t.gocardless.text.WHATISTHIS_TXT),
      ).toBeNull();
    });
    it('should get voucher status', () => {
      renderWithTheme('account', MOCKS);
      const { queryByTestId } = screen;
      const getGocardlessVoucherButton = queryByTestId('AddVoucherSuccess');
      fireEvent(getGocardlessVoucherButton, 'onPress');
      expect(getGocardlessVoucherButton).toBeTruthy();
    });
    it('should render voucher', () => {
      renderWithTheme('account', MOCKS);
      const { queryByTestId } = screen;
      expect(queryByTestId('VoucherCode')).toBeTruthy();
    });
    it('should display error for invalid vouchers', async () => {
      renderWithTheme('account', MOCKS);
      fireEvent.changeText(
        screen.getByTestId('VoucherCodeInput'),
        'fakeVoucher',
      );
      fireEvent.press(screen.getByTestId('AddVoucherSuccess'));

      await waitFor(() => {
        expect(
          screen.queryByText(
            mockUseSettingsObj.t.gocardless.text.BAD_MISSING_TAGPLAN,
          ),
        ).toBeDefined();
      });
    });

    it('should navigate to GoCardlessForm screen when Skip This Step button is pressed', () => {
      const mockNavigate = jest.fn();
      jest
        .spyOn(
          require('../../providers/HostNavigationProvider'),
          'useHostNavigation',
        )
        .mockReturnValue({ navigate: mockNavigate });

      renderWithTheme('account', MOCKS);

      fireEvent.press(screen.getByTestId('SkipBtn'));

      expect(mockNavigate).toHaveBeenCalledWith(
        SubscriptionScreenNames.GoCardlessForm,
        {
          from: 'account',
          logSubsFlow: true,
        },
      );
    });
  });
  describe('without userId', () => {
    it('should render voucher', () => {
      renderWithTheme('account', MOCKS);
      const { queryByTestId } = screen;
      expect(queryByTestId('VoucherCode')).toBeTruthy();
    });
    it('should display error for invalid vouchers', async () => {
      renderWithTheme('account', MOCKS);
      fireEvent.changeText(
        screen.getByTestId('VoucherCodeInput'),
        'fakeVoucher',
      );
      fireEvent.press(screen.getByTestId('AddVoucherSuccess'));

      await waitFor(() => {
        expect(
          screen.queryByText(
            mockUseSettingsObj.t.gocardless.text.BAD_MISSING_TAGPLAN,
          ),
        ).toBeDefined();
      });

      expect(screen.toJSON()).toMatchSnapshot();

      expect(
        screen.queryByText(
          mockUseSettingsObj.t.gocardless.text.BAD_MISSING_TAGPLAN,
        ),
      ).toBeDefined();
    });
    it('should show modal', () => {
      renderWithTheme('account', MOCKS);
      const { queryByText } = screen;
      expect(
        queryByText(mockUseSettingsObj.t.gocardless.text.WHATISTHIS_TXT),
      ).toBeNull();
      fireEvent(
        queryByText(mockUseSettingsObj.t.gocardless.buttons.WHATISTHIS),
        'onPress',
      );
      expect(
        queryByText(mockUseSettingsObj.t.gocardless.text.WHATISTHIS_TXT),
      ).toBeTruthy();
      fireEvent(
        queryByText(mockUseSettingsObj.t.gocardless.buttons.GOTIT),
        'onPress',
      );
      expect(
        queryByText(mockUseSettingsObj.t.gocardless.text.WHATISTHIS_TXT),
      ).toBeNull();
    });
    it('should get voucher status', () => {
      renderWithTheme('account', MOCKS);
      const { queryByTestId } = screen;
      const getGocardlessVoucherButton = queryByTestId('AddVoucherSuccess');
      fireEvent(getGocardlessVoucherButton, 'onPress');
      expect(getGocardlessVoucherButton).toBeTruthy();
    });
  });
});
