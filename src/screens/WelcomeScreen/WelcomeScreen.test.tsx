import { MockedProvider } from '@apollo/client/testing';
import { render, screen } from '@testing-library/react-native';
import React from 'react';

import { GET_GOCARDLESS_COMPLETE, REQUEST_RFID } from '../../client/queries';
import { AddressContext } from '../../providers/AddressProvider';
import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import WelcomeScreen from './WelcomeScreen';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { userId: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const ADDRESS_MOCKS = {
  cardPreference: 'fob',
  address_line: `Mock Add`,
  address_city: 'town',
  address_postcode: 'postalCode',
  address_country: 'country',
};

const testAddress = {
  rfidType: ADDRESS_MOCKS.cardPreference,
  address1: ADDRESS_MOCKS.address_line.split(' ', 2)[0],
  address2: ADDRESS_MOCKS.address_line.split(' ', 2)[1],
  town: ADDRESS_MOCKS.address_city,
  postalCode: ADDRESS_MOCKS.address_postcode,
  country: ADDRESS_MOCKS.address_country,
};

const mockUseUserInfoObj = {
  userInfo: {
    userId: '123',
  },
  setUserInfo: jest.fn(),
};
const mockUseUserInfo = jest.fn().mockReturnValue(mockUseUserInfoObj);
jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUseUserInfo(),
}));

describe('Welcome Screen', () => {
  const route = { params: { flow_id: 'flow-id-test' } };

  const createMocks = (userId: any) => [
    {
      request: {
        query: GET_GOCARDLESS_COMPLETE,
        variables: {
          userId: userId,
          flowId: 'flow-id-test',
        },
      },
      result: {
        data: {
          completePayment: {
            success: true,
          },
        },
      },
    },
    {
      request: {
        query: REQUEST_RFID,
        variables: {
          userId: userId,
          cardPreference: ADDRESS_MOCKS.cardPreference,
          addressDetails: {
            address_line: ADDRESS_MOCKS.address_line,
            address_city: ADDRESS_MOCKS.address_city,
            address_postcode: ADDRESS_MOCKS.address_postcode,
            address_country: ADDRESS_MOCKS.address_country,
          },
        },
      },
      result: {
        data: {
          requestRFID: {
            status: '200',
            data: {},
          },
        },
      },
    },
  ];

  const renderWelcomeScreen = (mocks: any) => {
    return render(
      <Theme>
        <MockedProvider mocks={mocks} addTypename={false}>
          <AddressContext.Provider value={testAddress}>
            <WelcomeScreen route={route} />
          </AddressContext.Provider>
        </MockedProvider>
      </Theme>,
    );
  };

  describe('with non-empty UserId', () => {
    const MOCKS = createMocks(mockUseUserInfoObj.userInfo.userId);

    it('renders welcome screen with data', async () => {
      const { toJSON } = renderWelcomeScreen(MOCKS);
      expect(screen.queryAllByTestId('Spinner')).toBeTruthy();
      expect(toJSON()).toMatchSnapshot();
      expect(screen.queryAllByTestId('primaryButton')).toBeTruthy();
    });

    it('renders welcome screen with error', async () => {
      let errorMocks = MOCKS.map((a) => ({ ...a }));
      // @ts-expect-error
      errorMocks.find(
        (a) => a?.result?.data?.completePayment?.success === true,
      ).result.data.completePayment.success = false;

      const { toJSON } = renderWelcomeScreen(errorMocks);
      expect(toJSON()).toMatchSnapshot();
      expect(screen.queryByTestId('primaryButton')).toBeFalsy();
    });
  });

  describe('with empty UserId', () => {
    const MOCKS = createMocks('');

    it('renders welcome screen with data', async () => {
      const { toJSON } = renderWelcomeScreen(MOCKS);
      expect(screen.queryAllByTestId('Spinner')).toBeTruthy();
      expect(toJSON()).toMatchSnapshot();
      expect(screen.queryAllByTestId('primaryButton')).toBeTruthy();
    });

    it('renders welcome screen with error', async () => {
      let errorMocks = MOCKS.map((a) => ({ ...a }));
      // @ts-expect-error
      errorMocks.find(
        (a) => a?.result?.data?.completePayment?.success === true,
      ).result.data.completePayment.success = false;

      const { toJSON } = renderWelcomeScreen(errorMocks);
      expect(toJSON()).toMatchSnapshot();
      expect(screen.queryByTestId('primaryButton')).toBeFalsy();
    });
  });
});
