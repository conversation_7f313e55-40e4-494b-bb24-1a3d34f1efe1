import { Dimensions } from 'react-native';
import { styled } from 'styled-components/native';

const phoneWidth = Dimensions.get('screen').width > 320 ? 15 : 2;
export const ContentContainer = styled.View`
  justify-content: space-between;
  align-items: center;
`;

export const Inline = styled.View`
  flex-direction: row;
`;

export const SvgWrapper = styled.View`
  flex-shrink: 2;
`;
export const ScreenWrapper = styled.SafeAreaView`
  flex: 1 0 0;
`;

export const WelcomeScreenContainer = styled.ScrollView`
  flex-direction: column;
`;

export const HeaderContainer = styled.View`
  justify-content: center;
  align-items: center;
`;

export const HeaderText = styled.Text`
  height: 24px;
  width: 161px;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const BannerContainer = styled.View`
  width: 100%;
  justify-content: center;
  align-items: center;
  padding-left: 16px;
  padding-right: 16px;
`;

export const BannerText = styled.Text`
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.banner};
  font-size: 20px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const SubBannerText = styled.Text`
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const BottomText = styled.Text`
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 14px;
  text-align: center;
  line-height: 20px;
`;

export const ButtonContainer = styled.View`
  padding: 32px 16px;
  margin-top: ${phoneWidth}%;
  background: #fff;
`;
