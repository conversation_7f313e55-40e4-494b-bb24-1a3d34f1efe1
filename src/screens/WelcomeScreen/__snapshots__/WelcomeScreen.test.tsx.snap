// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Welcome Screen with empty UserId renders welcome screen with data 1`] = `
<ActivityIndicator
  color="#000096"
  size="large"
  style={
    {
      "alignItems": "center",
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 1,
      "height": "100%",
      "justifyContent": "center",
      "position": "absolute",
      "width": "100%",
      "zIndex": 999,
    }
  }
  testID="Spinner"
/>
`;

exports[`Welcome Screen with empty UserId renders welcome screen with error 1`] = `
<ActivityIndicator
  color="#000096"
  size="large"
  style={
    {
      "alignItems": "center",
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 1,
      "height": "100%",
      "justifyContent": "center",
      "position": "absolute",
      "width": "100%",
      "zIndex": 999,
    }
  }
  testID="Spinner"
/>
`;

exports[`Welcome Screen with non-empty UserId renders welcome screen with data 1`] = `
<ActivityIndicator
  color="#000096"
  size="large"
  style={
    {
      "alignItems": "center",
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 1,
      "height": "100%",
      "justifyContent": "center",
      "position": "absolute",
      "width": "100%",
      "zIndex": 999,
    }
  }
  testID="Spinner"
/>
`;

exports[`Welcome Screen with non-empty UserId renders welcome screen with error 1`] = `
<ActivityIndicator
  color="#000096"
  size="large"
  style={
    {
      "alignItems": "center",
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 1,
      "height": "100%",
      "justifyContent": "center",
      "position": "absolute",
      "width": "100%",
      "zIndex": 999,
    }
  }
  testID="Spinner"
/>
`;
