import { useQuery } from '@apollo/client';
import {
  <PERSON>ton,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import Spinner from '@bp/ui-components/mobile/stages/Atoms/Spinner/Spinner';
import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useContext } from 'react';

import AccountSetupSvg from '../../../assets/images/Hands.svg';
import { SubsAnalyticsEventSetupComplete } from '../../analytics/events';
import { GET_GOCARDLESS_COMPLETE, REQUEST_RFID } from '../../client/queries';
import { AsyncStorageKey, TemporaryMandateStatus } from '../../common/enums';
import {
  IGoCardlessCompleteRequest,
  IGoCardlessCompleteResponse,
  IRequestRFIDRequest,
  IRequestRFIDResponse,
} from '../../common/interfaces';
import { SpacerDynamic, SvgImageContainer } from '../../components';
import { AddressContext } from '../../providers/AddressProvider';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { getAnalyticsPayload } from '../../utils/helpers';
import * as S from './WelcomeScreen.styles';

const WelcomeScreen = ({ route }: any) => {
  const { params } = route;
  const flowId = params && params.flow_id;
  const { navigate } = useHostNavigation();
  const { t, onAnalyticsEvent } = useSettings();
  const { userInfo } = useUserInfo();
  const { rfidType, address1, address2, town, postalCode, country } =
    useContext(AddressContext);

  const setTempMandate = async () => {
    await AsyncStorage.setItem(
      AsyncStorageKey.DIRECT_DEBIT_REQUESTED,
      JSON.stringify({
        temporaryMandate: TemporaryMandateStatus.TEMPORARY_PENDING,
        user_id: userInfo?.userId,
        date: new Date(),
      }),
    );
  };

  const {
    loading: goCardlessLoading,
    error: goCardlessError,
    data: goCardlessData,
  } = useQuery<IGoCardlessCompleteResponse, IGoCardlessCompleteRequest>(
    GET_GOCARDLESS_COMPLETE,
    {
      variables: {
        userId: userInfo?.userId || '',
        flowId,
      },
      onCompleted: async () => {
        setTempMandate();
        onAnalyticsEvent(
          SubsAnalyticsEventSetupComplete(getAnalyticsPayload(userInfo)),
        );
      },
    },
  );

  const {
    loading: rfidLoading,
    error: rfidError,
    data: rfidData,
  } = useQuery<IRequestRFIDResponse, IRequestRFIDRequest>(REQUEST_RFID, {
    variables: {
      userId: userInfo?.userId || '',
      cardPreference: rfidType,
      addressDetails: {
        address_line: `${address1} ${address2}`,
        address_city: town,
        address_postcode: postalCode,
        address_country: country,
      },
    },
  });

  if (goCardlessLoading || rfidLoading) {
    return <Spinner active />;
  }

  return (
    <S.ScreenWrapper>
      <S.WelcomeScreenContainer>
        {rfidError ||
        goCardlessError ||
        goCardlessData?.completePayment.success === false ||
        rfidData?.requestRFID.status === 400 ? (
          <S.BannerText>{t.gocardless.text.GENERIC_ERROR_TXT}</S.BannerText>
        ) : (
          <>
            <S.SvgWrapper>
              <SvgImageContainer>
                <AccountSetupSvg />
              </SvgImageContainer>
            </S.SvgWrapper>
            <S.BannerContainer>
              <SpacerDynamic vSpace={2.3} />
              <S.BannerText>{t.gocardless.text.WELCOME_BANNER}</S.BannerText>
              <SpacerDynamic vSpace={2.5} />
              <S.SubBannerText>
                {t.gocardless.text.WELCOME_SUBBANNER}
              </S.SubBannerText>
              <SpacerDynamic vSpace={1.4} />
            </S.BannerContainer>
            <S.ButtonContainer>
              <Button
                type={ButtonAction.PRIMARY}
                accessibilityLabel="Set up gocard account"
                accessibilityHint="Process your go card payment"
                onPress={() => navigate('ProfileMFE.Profile')}
                size={ButtonSize.XLARGE}
              >
                {t.gocardless.text.WELCOME_FINISH_BUTTON}
              </Button>
              <SpacerDynamic vSpace={1.4} />
              <S.BottomText>
                {t.gocardless.text.WELCOME_BOTTOM_TEXT}
              </S.BottomText>
            </S.ButtonContainer>
          </>
        )}
      </S.WelcomeScreenContainer>
    </S.ScreenWrapper>
  );
};

export default WelcomeScreen;
