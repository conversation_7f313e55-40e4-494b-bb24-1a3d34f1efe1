import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { render } from '@testing-library/react-native';
import React from 'react';

import { SubscriptionScreenNames } from '../common/enums';
import Theme from '../themes/Theme';
import { TRANSLATIONS } from '../translations';
import SubscriptionLanding from './SubscriptionLanding/SubscriptionLanding';

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

// Mock BackIcon component for simplicity
jest.mock('../components', () => {
  return {
    BackIcon: jest.fn(({ color, testID }) => (
      <div data-testid={testID} style={{ color }} />
    )),
  };
});

const mockNavigate = jest.fn();
const mockGoBack = jest.fn();
jest.mock('../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => {
    return {
      navigate: mockNavigate,
      navigation: {
        getState: jest.fn(),
        goBack: mockGoBack,
      },
    };
  },
}));

const Stack = createStackNavigator();
const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { id: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
  isInternetReachable: true,
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const RenderWithTheme = ({ children }: any) => <Theme>{children}</Theme>;

describe('Index component', () => {
  it('should render screen correctly', () => {
    const result = render(
      <RenderWithTheme>
        <NavigationContainer>
          <Stack.Navigator>
            <Stack.Screen
              name={SubscriptionScreenNames.SubscriptionLanding}
              component={SubscriptionLanding}
            />
          </Stack.Navigator>
        </NavigationContainer>
      </RenderWithTheme>,
    ).toJSON();
    expect(result).toMatchSnapshot();
  });
});

jest.mock('react-native-webview', () => {
  const { View } = require('react-native');
  return {
    WebView: View,
  };
});
