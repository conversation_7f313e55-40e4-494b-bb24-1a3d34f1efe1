import { Dimensions } from 'react-native';
import { styled } from 'styled-components/native';
const window = Dimensions.get('window');
export const CancelMembershipContainer = styled.View<{
  grayBackgorund: boolean;
}>`
  flex: 1;
  display: flex;
  background-color: ${(props) => (props.grayBackgorund ? '#f5f5f5' : '#fff')};
`;
export const ScrollWrapper = styled.ScrollView`
  flex: ${window.height < 800 ? 0.7 : 0.8};
  flex-direction: column;
  top: 0;
`;
export const TopViewContainer = styled.View`
  justify-content: center;
`;
export const BottomViewContainer = styled.View`
  background-color: white;
  width: 100%;
  padding: 16px 16px;
  margin-bottom: 0;
  position: absolute;
  bottom: 0;
  box-shadow: 0px 50px 40px ${(p) => p.theme.subscriptionMfe.color.primary};
`;

export const BottomText = styled.Text`
  height: 40px;
  width: 343px;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 14px;
  letter-spacing: 0;
  line-height: 20px;
  text-align: center;
`;

export const DateText = styled.Text`
  text-align: left;
  font-size: 16px;
  align-items: flex-start;
  color: ${(p: any) => p.theme.subscriptionMfe.color.primary};
`;

export const SubText = styled.Text`
  text-align: left;
  font-size: 16px;
`;

export const InnerContainer = styled.View`
  background-color: rgba(0, 0, 150, 0.8);
`;

export const ModalView = styled.View`
  width: 100%;
  justify-content: center;
  align-items: center;
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 20px;
`;

export const ModalTitleText = styled.Text`
  width: 100%;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.banner};
  font-size: 20px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const ModalText = styled.Text`
  width: 100%;
  color: ${(props: any) => props.theme.subscriptionMfe.gocardless.text.header};
  font-size: 16px;
  letter-spacing: 0;
  line-height: 24px;
  text-align: center;
`;

export const HeaderPosition = styled.SafeAreaView``;
