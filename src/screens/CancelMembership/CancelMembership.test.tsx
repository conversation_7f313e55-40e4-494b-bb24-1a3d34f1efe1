import { MockedProvider } from '@apollo/client/testing';
import { fireEvent, render, screen } from '@testing-library/react-native';
import React from 'react';

import { CANCEL_SUBSCRIPTION, GET_GOCARDLESS_URL } from '../../client/queries';
import { BpcmGeneralResponseCode } from '../../common/enums';
import Theme from '../../themes/Theme';
import TRANSLATIONS from '../../translations/messages.json';
import { rawData } from '../../utils/mockData';
import { updateTranslations } from '../../utils/updateTranslations';
import CancelMembership from './CancelMembership';

const mockWalletSubscriptionObj = {
  walletSubscriptionData: rawData,
  subscription: {
    billingItemId: '651dce90-2497-4a13-9503-e16cccbb375b',
    planId: 'ac7e03e1-0da2-4515-8412-7ec556f32b8d',
    name: 'Plan A - September 14',
    amount: '0',
    currency: 'GBP',
  },
  totalValue: 7.99,
  currency: 'GBP',
  featureFlags: {
    subsChargingRatesPromo: 0.44,
  },
};
const mockWalletSubscriptionData = jest
  .fn()
  .mockReturnValue(mockWalletSubscriptionObj);
jest.mock('../../providers/WalletSubscriptionDataProvider', () => ({
  WalletSubscriptionData: () => mockWalletSubscriptionData(),
}));

jest.mock('@react-navigation/stack', () => ({
  createStackNavigator: () => ({
    Navigator: jest.fn(),
    Screen: jest.fn(),
  }),
}));

const mockNavigate = jest.fn();
jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => {
    return {
      navigate: mockNavigate,
      navigation: {
        getState: jest.fn(),
      },
    };
  },
}));
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useRoute: () => ({
    params: {
      from: 'CancelSubscriptionFailed',
    },
  }),
}));

const mockTranslations = TRANSLATIONS;
const mockUseSettingsObj = {
  t: updateTranslations(mockTranslations, { brand: 'bp' }),
  user: { userId: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
  featureFlags: {
    subsChargingRatesPromo: 0.44,
  },
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockUserInfoObj = {
  userInfo: {
    country: 'UK',
  },
};
const mockUserInfo = jest.fn().mockReturnValue(mockUserInfoObj);
jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfo(),
}));

const MOCKS = [
  {
    request: {
      query: GET_GOCARDLESS_URL,
      variables: {
        userId: mockUseSettingsObj.user.userId,
      },
    },
    result: {
      data: {
        userInfo: {
          type: 'PAYG',
        },
      },
    },
  },
  {
    request: {
      query: CANCEL_SUBSCRIPTION,
      variables: {
        userId: '',
      },
    },
    result: {
      data: {
        cancelSubscription: {
          status: '',
          payload: {
            code: BpcmGeneralResponseCode.NOT_FOUND,
          },
        },
      },
    },
  },
];

const renderWithTheme = () =>
  render(
    <Theme>
      <MockedProvider mocks={MOCKS}>
        <CancelMembership />
      </MockedProvider>
    </Theme>,
  );

describe('Cancel Membership component', () => {
  it('should render correctly', () => {
    const { toJSON } = renderWithTheme();
    expect(toJSON()).toMatchSnapshot();
  });

  it('should cancel membership', () => {
    renderWithTheme();
    const { queryByText, queryByHintText } = screen;
    const cancelMembershipButton = queryByText(
      mockUseSettingsObj.t.gocardless.buttons.CANCEL_BTN,
    );

    fireEvent(cancelMembershipButton, 'onPress');

    expect(mockUseSettingsObj.t.gocardless.text.CANCEL_TITLE).toBeTruthy();
    const cancelMembershipButton2 = queryByHintText(
      mockUseSettingsObj.t.gocardless.accessibility.CANCEL_MODAL.HINT,
    );
    fireEvent(cancelMembershipButton2, 'onPress');
    expect(cancelMembershipButton).toBeTruthy();
  });

  it('should click to I want to stay buton on cancel membership model', () => {
    renderWithTheme();
    const { queryByText, getAllByText } = screen;
    const cancelMembershipButton = queryByText(
      mockUseSettingsObj.t.gocardless.buttons.CANCEL_BTN,
    );

    fireEvent(cancelMembershipButton, 'onPress');

    expect(mockUseSettingsObj.t.gocardless.text.CANCEL_TITLE).toBeTruthy();
    const wantToStayButton = getAllByText(
      mockUseSettingsObj.t.gocardless.buttons.IWANTTOSTAY,
    )[1];

    fireEvent(wantToStayButton, 'onPress');
    expect(wantToStayButton).toBeTruthy();
  });

  it('should click to I want to stay buton', () => {
    renderWithTheme();
    const { queryByText } = screen;
    const wantToStayButton = queryByText(
      mockUseSettingsObj.t.gocardless.buttons.IWANTTOSTAY,
    );

    fireEvent(wantToStayButton, 'onPress');
    expect(wantToStayButton).toBeTruthy();
  });
});

describe('Cancel Membership component without UserId', () => {
  it('should render correctly', () => {
    const { toJSON } = renderWithTheme();
    expect(toJSON()).toMatchSnapshot();
  });

  it('should cancel membership', () => {
    renderWithTheme();

    const { queryByText, queryByHintText } = screen;
    const cancelMembershipButton = queryByText(
      mockUseSettingsObj.t.gocardless.buttons.CANCEL_BTN,
    );

    fireEvent(cancelMembershipButton, 'onPress');

    expect(mockUseSettingsObj.t.gocardless.text.CANCEL_TITLE).toBeTruthy();
    const cancelMembershipButton2 = queryByHintText(
      mockUseSettingsObj.t.gocardless.accessibility.CANCEL_MODAL.HINT,
    );
    fireEvent(cancelMembershipButton2, 'onPress');
    expect(cancelMembershipButton).toBeTruthy();
  });
});
