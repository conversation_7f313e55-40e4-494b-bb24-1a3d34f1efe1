import { useMutation } from '@apollo/client';
import { <PERSON><PERSON>, ButtonAction } from '@bp/ui-components/mobile/core';
import React, { useState } from 'react';
import { useTheme } from 'styled-components/native';

import { SubsAnalyticsEventCancelSubscriptionComplete } from '../../analytics/events';
import {
  CANCEL_SUBSCRIPTION,
  CANCEL_WALLET_SUBSCRIPTION,
} from '../../client/queries';
import {
  BpcmGeneralResponseCode,
  StatusReason,
  SubscriptionScreenNames,
  UserTypeEnum,
} from '../../common/enums';
import {
  ICancelSubscriptionMutationResponse,
  ICancelSubscriptionRequest,
  ICancelWalletSubscriptionMutationResponse,
} from '../../common/interfaces';
import {
  CancelButton,
  LinkParagraphComponent,
  Modal,
  Spacer,
} from '../../components';
import { CancelSubscriptionButtons } from '../../components/Organisms/CancelSubscriptionButtons/CancelSubscriptionButtons';
import { CancelSubscriptionContent } from '../../components/Organisms/CancelSubscriptionContent/CancelSubscriptionContent';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useSubscriptionContext } from '../../providers/SubscriptionProvider';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { WalletSubscriptionData } from '../../providers/WalletSubscriptionDataProvider';
import {
  formatDate,
  getAnalyticsPayloadForCancel,
  hasInactiveMembership,
  isValidDate,
} from '../../utils/helpers';
import * as S from './CancelMembership.styles';

export default () => {
  const { t, onAnalyticsEvent, onExitMFE, isInternetReachable } = useSettings();
  const { navigate } = useHostNavigation();
  const { userInfo } = useUserInfo();

  const { offerCode } = useSubscriptionContext();
  const { walletSubscriptionData, refetchWalletSubscription } =
    WalletSubscriptionData();
  const theme = useTheme();
  const [modalVisible, setModalVisible] = useState(false);

  const [cancelMembershipMutation, { loading: loadingLegacy }] = useMutation<
    ICancelSubscriptionMutationResponse,
    ICancelSubscriptionRequest
  >(CANCEL_SUBSCRIPTION, {
    onCompleted: async (data: ICancelSubscriptionMutationResponse) => {
      if (data.cancelSubscription.payload.code === BpcmGeneralResponseCode.OK) {
        onAnalyticsEvent(
          SubsAnalyticsEventCancelSubscriptionComplete(
            getAnalyticsPayloadForCancel(userInfo, offerCode),
          ),
        );
        return onExitMFE();
      }

      return navigate(
        SubscriptionScreenNames.CancelSubscriptionFailed,
        { message: t.gocardless.text.CANCELSUBSCRIPTION_ERROR },
        true,
      );
    },
    onError: () => {
      return navigate(
        SubscriptionScreenNames.CancelSubscriptionFailed,
        { message: t.gocardless.text.CANCELSUBSCRIPTION_ERROR },
        true,
      );
    },
  });

  const [cancelWalletSubscriptionMutation, { loading: loadingWallet }] =
    useMutation<
      ICancelWalletSubscriptionMutationResponse,
      ICancelSubscriptionRequest
    >(CANCEL_WALLET_SUBSCRIPTION, {
      onCompleted: async (data: ICancelWalletSubscriptionMutationResponse) => {
        refetchWalletSubscription();
        if (
          data?.cancelWalletSubscription.statusReason ===
            StatusReason.PENDING_CANCELLATION ||
          data?.cancelWalletSubscription.statusReason === StatusReason.CANCELLED
        ) {
          onAnalyticsEvent(
            SubsAnalyticsEventCancelSubscriptionComplete(
              getAnalyticsPayloadForCancel(userInfo, offerCode),
            ),
          );
          const cancelledOnDate = new Date(
            data?.cancelWalletSubscription.cancelledOn,
          );
          cancelledOnDate.setDate(cancelledOnDate.getDate() - 1);

          return navigate(
            SubscriptionScreenNames.CancelSubscriptionConfirmation,
            {
              cancelledOn: formatDate(cancelledOnDate.toISOString()),
            },
            true,
          );
        }

        return navigate(
          SubscriptionScreenNames.CancelSubscriptionFailed,
          { message: t.gocardless.text.CANCELSUBSCRIPTION_ERROR },
          true,
        );
      },
      onError: () => {
        return navigate(
          SubscriptionScreenNames.CancelSubscriptionFailed,
          { message: t.gocardless.text.CANCELSUBSCRIPTION_ERROR },
          true,
        );
      },
    });

  let nextBillingdate: string | undefined;

  const nextBillingDateString =
    walletSubscriptionData?.getWalletSubscription?.nextBillingDate;

  if (!hasInactiveMembership(userInfo?.membership)) {
    if (nextBillingDateString && isValidDate(nextBillingDateString)) {
      const originalNextBillingDate = new Date(nextBillingDateString);

      const previousBillingDate = new Date(originalNextBillingDate);
      previousBillingDate.setDate(originalNextBillingDate.getDate() - 1);

      nextBillingdate = formatDate(previousBillingDate.toISOString());
    } else {
      console.error('Invalid or missing nextBillingDate');
      nextBillingdate = 'DD/MM/YYYY';
    }
  }

  const loading = loadingLegacy || loadingWallet;

  const cancelMembership = async () => {
    if (userInfo?.type === UserTypeEnum.SUBS_WALLET) {
      await cancelWalletSubscriptionMutation({
        variables: {
          userId: userInfo?.userId || '',
        },
      });
    } else {
      await cancelMembershipMutation({
        variables: {
          userId: userInfo?.userId || '',
        },
      });
    }
  };

  const onPressCancellation = async () => {
    setModalVisible(false);
    await cancelMembership();
  };

  return (
    <S.CancelMembershipContainer
      grayBackgorund={userInfo?.type !== UserTypeEnum.SUBS_WALLET}
    >
      <S.ScrollWrapper>
        <CancelSubscriptionContent
          userType={userInfo?.type}
          userCountry={userInfo?.country}
          theme={theme}
        />
        <S.TopViewContainer>
          <LinkParagraphComponent />
        </S.TopViewContainer>
      </S.ScrollWrapper>
      <S.BottomViewContainer>
        <CancelSubscriptionButtons
          theme={theme}
          nextBillingdate={nextBillingdate}
          userType={userInfo?.type}
          setModalVisible={setModalVisible}
          cancelMembership={cancelMembership}
          isInternetReachable={isInternetReachable}
          navigate={navigate}
          loading={loading}
        />
        <Spacer vSpace={16} />
      </S.BottomViewContainer>
      <Spacer vSpace={16} />
      <S.InnerContainer>
        <Modal visible={modalVisible} setVisibleState={setModalVisible}>
          <S.ModalView>
            <Spacer vSpace={35} />
            <S.ModalTitleText>
              {t.gocardless.text.CANCEL_TITLE}
            </S.ModalTitleText>
            <Spacer />
            <S.ModalText>{t.gocardless.text.CANCEL_TEXT}</S.ModalText>
            <Spacer vSpace={20} />
            <CancelButton
              accessibilityLabel={t.gocardless.accessibility.CANCEL_MODAL.LABEL}
              accessibilityHint={t.gocardless.accessibility.CANCEL_MODAL.HINT}
              onPress={onPressCancellation}
            >
              {t.gocardless.buttons.CANCEL_BTN}
            </CancelButton>
            <Spacer vSpace={16} />
            <Button
              type={ButtonAction.SENARY}
              onPress={() => {
                setModalVisible(false);
                navigate(SubscriptionScreenNames.GoCardlessMembership);
              }}
            >
              {t.gocardless.buttons.IWANTTOSTAY}
            </Button>
          </S.ModalView>
        </Modal>
      </S.InnerContainer>
    </S.CancelMembershipContainer>
  );
};
