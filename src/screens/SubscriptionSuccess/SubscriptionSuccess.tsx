import { But<PERSON>, ButtonAction } from '@bp/ui-components/mobile/core';
import React, { useEffect, useMemo } from 'react';
import { ScrollView } from 'react-native';

import SubscriptionSuccessImage from '../../../assets/images/subscription-success.png';
import {
  SubsAnalyticsEventSetupComplete,
  SubsAnalyticsEventSubsSuccessScreenOrderRFIDClick,
  SubsAnalyticsEventSubsSuccessScreenStartChargingClick,
} from '../../analytics/events';
import { MandateStatus, UserCountry } from '../../common/enums';
import { useSettings } from '../../providers/Settings';
import { useSubscriptionContext } from '../../providers/SubscriptionProvider';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { getCountrySpecificText, hasRfidTag } from '../../utils/helpers';
import * as S from './SubscriptionSuccess.styles';

const SubscriptionSuccess = ({ route }: any) => {
  const {
    t,
    onRequestToStartCharge,
    onNavigateToRFID,
    onAnalyticsEvent,
    user,
  } = useSettings();
  const { userInfo, resubscribed } = useUserInfo();
  const { offerCode, currency, totalAmount, subsDiscount } =
    useSubscriptionContext();

  const hasRfidCard = useMemo(() => hasRfidTag(userInfo), [userInfo]);

  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventSetupComplete({
        tag_ids: userInfo?.tagIds || [],
        account_balance: userInfo?.balance || 0,
        gocardless_mandate_status:
          userInfo?.country === UserCountry.UK
            ? userInfo?.gocardless?.mandateStatus || MandateStatus.INACTIVE
            : 'N/A',
        first_time_subscribing: !resubscribed,
        offer_code: offerCode,
        currency: currency,
        value: totalAmount,
        discount: subsDiscount,
        subscription_id: route?.params?.subscriptionData?.id,
      }),
    );
  }, [
    route?.params?.subscriptionData?.id,
    subsDiscount,
    currency,
    offerCode,
    onAnalyticsEvent,
    resubscribed,
    totalAmount,
    userInfo?.balance,
    userInfo?.gocardless?.mandateStatus,
    userInfo?.tagIds,
    userInfo?.country,
  ]);

  return (
    <S.ScreenContainer>
      <ScrollView
        contentContainerStyle={{
          flexGrow: 1,
          justifyContent: 'space-between',
        }}
      >
        <S.SuccessHeaderText>
          {!hasRfidCard
            ? t.gocardless.text.SUBSCRIPTION_SET_UP_SUCCESS_HEADER
            : t.gocardless.text.SUBSCRIPTION_SET_UP_SUCCESS_HEADER_WITH_RFID}
        </S.SuccessHeaderText>

        <S.SuccessImageContainer>
          <S.SuccessImage source={SubscriptionSuccessImage} />
        </S.SuccessImageContainer>

        <S.SuccessMessage>
          {getCountrySpecificText(
            t,
            'gocardless.text.SUBSCRIPTION_SET_UP_SUCCESS_MESSAGE',
            user?.country,
          )}
        </S.SuccessMessage>

        <S.ButtonContainer>
          {!hasRfidCard && (
            <Button
              testID="SubscriptionSuccess.GetChargeCardButton"
              type={ButtonAction.PRIMARY}
              onPress={() => {
                onAnalyticsEvent(
                  SubsAnalyticsEventSubsSuccessScreenOrderRFIDClick({
                    first_time_subscribing: !resubscribed,
                    offer_code: offerCode,
                  }),
                );
                onNavigateToRFID();
              }}
            >
              {t.gocardless.buttons.GET_CHARGE_CARD}
            </Button>
          )}

          <Button
            testID="SubscriptionSuccess.StartChargingButton"
            type={hasRfidCard ? ButtonAction.PRIMARY : ButtonAction.SECONDARY}
            styleOverride={{ marginTop: 16 }}
            onPress={() => {
              onAnalyticsEvent(
                SubsAnalyticsEventSubsSuccessScreenStartChargingClick({
                  tag_ids: userInfo?.tagIds || [],
                  first_time_subscribing: !resubscribed,
                  offer_code: offerCode,
                }),
              );
              onRequestToStartCharge();
            }}
          >
            {t.gocardless.buttons.START_CHARGING}
          </Button>
        </S.ButtonContainer>
      </ScrollView>
    </S.ScreenContainer>
  );
};

export default SubscriptionSuccess;
