import { ThemeProvider } from '@bp/ui-components/mobile';
import { render, screen } from '@testing-library/react-native';
import React from 'react';

import {
  MandateStatus,
  MembershipStatus,
  UserCountry,
  UserTypeEnum,
} from '../../common/enums';
import { IMmembership, IUserInfo } from '../../common/interfaces';
import * as UserInfo from '../../providers/UserInfoProvider';
import { TRANSLATIONS } from '../../translations';
import SubscriptionSuccess from './SubscriptionSuccess';

const rfidTag = {
  tagId: 'tag-id',
  tagStatus: 'ACTIVE',
  tagNotes: 'physical-RFID',
};

const mockMembership: IMmembership = {
  userType: UserTypeEnum.SUBS_WALLET,
  membershipStatus: MembershipStatus.CANCELLED,
  membershipRequestCancelDate: null,
  membershipStartDate: '2024-10-16T00:00:00.000Z',
  membershipEndDate: '',
  membershipBillingCycleDate: '2024-11-16T00:00:00.000Z',
};

const userInfoMock: IUserInfo = {
  userId: 'user-id',
  type: 'user-type',
  status: '',
  gocardless: {
    mandateStatus: MandateStatus.ACTIVE,
    mandateId: '',
  },
  tagIds: [rfidTag],
  balance: 0,
  country: UserCountry.UK,
  membership: [mockMembership],
  latestMembership: mockMembership,
};

const mockUseSettingsObj = {
  t: TRANSLATIONS.en_GB,
  onAnalyticsEvent: () => jest.fn(),
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const useUserInfoSpy = jest.spyOn(UserInfo, 'useUserInfo').mockReturnValue({
  userInfo: userInfoMock,
});

const renderComponent = () =>
  render(
    <ThemeProvider>
      <SubscriptionSuccess />
    </ThemeProvider>,
  );

describe('<SubscriptionSuccess />', () => {
  it('should match snapshot', () => {
    renderComponent();
    expect(screen.toJSON()).toMatchSnapshot();
  });

  it('should render text on the page', () => {
    renderComponent();

    expect(
      screen.getByText(
        TRANSLATIONS.en_GB.gocardless.text
          .SUBSCRIPTION_SET_UP_SUCCESS_HEADER_WITH_RFID,
      ),
    ).toBeTruthy();

    expect(
      screen.getByText(
        TRANSLATIONS.en_GB.gocardless.text.SUBSCRIPTION_SET_UP_SUCCESS_MESSAGE,
      ),
    ).toBeTruthy();
  });

  it('should show get charge card button when user does NOT have rfid card', () => {
    useUserInfoSpy.mockReturnValueOnce({
      userInfo: {
        ...userInfoMock,
        tagIds: [],
      },
    });

    renderComponent();

    const button = screen.queryByTestId(
      'SubscriptionSuccess.GetChargeCardButton',
    );

    expect(
      screen.getByText(
        TRANSLATIONS.en_GB.gocardless.text.SUBSCRIPTION_SET_UP_SUCCESS_HEADER,
      ),
    ).toBeTruthy();

    expect(button).not.toBeNull();
  });

  it('should hide get charge card button when user has rfid card', () => {
    renderComponent();

    const button = screen.queryByTestId(
      'SubscriptionSuccess.GetChargeCardButton',
    );

    expect(button).toBeNull();
  });
});
