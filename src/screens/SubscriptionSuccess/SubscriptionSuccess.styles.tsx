import { styled } from 'styled-components/native';

export const ScreenContainer = styled.SafeAreaView`
  flex: 1;
`;

export const SuccessHeaderText = styled.Text`
  padding-top: 40px;
  font-family: 'Roboto-Light';
  font-size: 28px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.5px;
  line-height: 40px;
  margin-bottom: 26px;
  margin-left: 24px;
  margin-right: 24px;
  text-align: center;
`;

export const SuccessImageContainer = styled.View`
  align-items: center;
`;

export const SuccessImage = styled.Image`
  width: 248px;
  height: 266px;
`;

export const SuccessMessage = styled.Text`
  font-family: 'Roboto-Light';
  font-size: 18px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.2px;
  line-height: 32px;
  text-align: center;
  margin-top: 8px;
  margin-left: 24px;
  margin-right: 24px;
`;

export const ButtonContainer = styled.View`
  flex: 1;
  margin: 24px;
  justify-content: flex-end;
`;
