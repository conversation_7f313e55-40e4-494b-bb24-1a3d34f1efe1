// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SubscriptionSuccess /> should match snapshot 1`] = `
<RCTSafeAreaView
  style={
    {
      "flexBasis": 0,
      "flexGrow": 1,
      "flexShrink": 1,
    }
  }
>
  <RCTScrollView
    contentContainerStyle={
      {
        "flexGrow": 1,
        "justifyContent": "space-between",
      }
    }
  >
    <View>
      <Text
        style={
          {
            "color": "rgb(17, 17, 17)",
            "fontFamily": "Roboto-Light",
            "fontSize": 28,
            "letterSpacing": 0.5,
            "lineHeight": 40,
            "marginBottom": 26,
            "marginLeft": 24,
            "marginRight": 24,
            "paddingTop": 40,
            "textAlign": "center",
          }
        }
      >
        Success! Subscription setup complete.
      </Text>
      <View
        style={
          {
            "alignItems": "center",
          }
        }
      >
        <Image
          source={
            {
              "testUri": "../../../assets/images/subscription-success.png",
            }
          }
          style={
            {
              "height": 266,
              "width": 248,
            }
          }
        />
      </View>
      <Text
        style={
          {
            "color": "rgb(17, 17, 17)",
            "fontFamily": "Roboto-Light",
            "fontSize": 18,
            "letterSpacing": 0.2,
            "lineHeight": 32,
            "marginLeft": 24,
            "marginRight": 24,
            "marginTop": 8,
            "textAlign": "center",
          }
        }
      >
        Subscription set up successfully! Enjoy your discounted rate starting today.
      </Text>
      <View
        style={
          {
            "flexBasis": 0,
            "flexGrow": 1,
            "flexShrink": 1,
            "justifyContent": "flex-end",
            "marginBottom": 24,
            "marginLeft": 24,
            "marginRight": 24,
            "marginTop": 24,
          }
        }
      >
        <View
          accessibilityRole="button"
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": false,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#000096",
              "borderBottomLeftRadius": 23,
              "borderBottomRightRadius": 23,
              "borderColor": "transparent",
              "borderStyle": "solid",
              "borderTopLeftRadius": 23,
              "borderTopRightRadius": 23,
              "borderWidth": 0,
              "justifyContent": "center",
              "marginTop": 16,
              "minHeight": 46,
              "opacity": 1,
              "paddingHorizontal": 25.5,
              "paddingVertical": 12,
            }
          }
          testID="SubscriptionSuccess.StartChargingButton"
        >
          <View>
            <Text
              disabled={false}
              inverted={false}
              size="large"
              style={
                {
                  "color": "#ffffff",
                  "fontFamily": "Roboto-Regular",
                  "fontSize": 15,
                  "letterSpacing": 0.7,
                  "textAlign": "center",
                }
              }
              type="primary"
            >
              Start charging
            </Text>
          </View>
        </View>
      </View>
    </View>
  </RCTScrollView>
</RCTSafeAreaView>
`;
