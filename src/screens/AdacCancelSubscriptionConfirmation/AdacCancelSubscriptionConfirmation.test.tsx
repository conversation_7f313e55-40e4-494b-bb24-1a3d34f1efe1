import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import AdacCancelSubscriptionConfirmation from './AdacCancelSubscriptionConfirmation';

const mockOnNavigateToProfile = jest.fn();

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  onNavigateToProfile: mockOnNavigateToProfile,
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);

jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const renderComponent = () => {
  return render(
    <Theme>
      <SafeAreaProvider>
        <AdacCancelSubscriptionConfirmation
          route={{
            params: {
              endOfCurrentBillingCycle: '2024-11-11T00:00:00.000Z',
              cancelledOn: '2024-11-11T00:00:00.000Z',
            },
          }}
        />
      </SafeAreaProvider>
    </Theme>,
  );
};

const ACTION_BUTTON = 'adac-cancel-subscription-confirmation-finish-button';

describe('<AdacCancelSubscriptionConfirmation />', () => {
  afterEach(() => jest.clearAllMocks());

  it('should correctly render screen contents', () => {
    const container = renderComponent();

    expect(container).toMatchSnapshot();
  });
  it('should navigate profile screen when clicked on okay action button', () => {
    const { getByTestId } = renderComponent();

    expect(getByTestId(ACTION_BUTTON)).toBeDefined();

    fireEvent.press(getByTestId(ACTION_BUTTON));

    expect(mockOnNavigateToProfile).toHaveBeenCalled();
  });
});
