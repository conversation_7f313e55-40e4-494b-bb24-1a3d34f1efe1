import {
  Button,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import React from 'react';

import CalendarConfirmed from '../../../assets/svg/calendarConfirmed.svg';
import { SubscriptionCancelledWrapper } from '../../components';
import { useSettings } from '../../providers/Settings';
import { formatDate } from '../../utils/helpers';

const AdacCancelSubscriptionConfirmation = ({ route }: any) => {
  const { t, onNavigateToProfile } = useSettings();

  const { params } = route;

  const cancelledOn = formatDate(params?.cancelledOn);

  return (
    <SubscriptionCancelledWrapper
      title={t.adacCancelSubscriptionConfirmation.title}
      subtitle={t.adacCancelSubscriptionConfirmation.subtitle.replace(
        '{{date}}',
        cancelledOn,
      )}
      image={<CalendarConfirmed />}
      testId="adac-cancel-subscription-confirmation"
    >
      <Button
        accessibilityLabel={
          t.adacCancelSubscriptionConfirmation.finish.accessibility.label
        }
        accessibilityHint={
          t.adacCancelSubscriptionConfirmation.finish.accessibility.hint
        }
        type={ButtonAction.PRIMARY}
        size={ButtonSize.XLARGE}
        onPress={onNavigateToProfile}
        testID="adac-cancel-subscription-confirmation-finish-button"
      >
        {t.adacCancelSubscriptionConfirmation.finish.label}
      </Button>
    </SubscriptionCancelledWrapper>
  );
};

export default AdacCancelSubscriptionConfirmation;
