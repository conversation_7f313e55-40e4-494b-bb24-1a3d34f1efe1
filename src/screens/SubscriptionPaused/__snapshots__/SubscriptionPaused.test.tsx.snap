// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Subscription Paused Screen renders without crashing 1`] = `
[
  <RCTSafeAreaView
    style={
      {
        "flexBasis": 0,
        "flexGrow": 1,
        "flexShrink": 1,
        "paddingTop": 0,
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexBasis": 0,
          "flexGrow": 0.6,
          "flexShrink": 1,
          "marginLeft": 87,
          "marginRight": 87,
        }
      }
    >
      <Image
        source={
          {
            "testUri": "../../../assets/images/pausedImage.png",
          }
        }
        style={
          {
            "height": 285,
            "width": 285,
          }
        }
      />
    </View>
    <View
      style={
        {
          "flexBasis": 0,
          "flexGrow": 0.5,
          "flexShrink": 1,
          "marginLeft": 24,
          "marginRight": 24,
          "marginTop": 8,
        }
      }
    >
      <Text
        style={
          {
            "color": "rgb(17, 17, 17)",
            "fontFamily": "Roboto-Regular",
            "fontSize": 18,
            "letterSpacing": 0.2,
            "lineHeight": 32,
            "textAlign": "center",
          }
        }
        testID="SubscriptionPaused.Label"
      >
        Your subscription payment hasn’t gone through!
      </Text>
      <Text
        style={
          {
            "color": "rgb(17, 17, 17)",
            "fontFamily": "Roboto-Regular",
            "fontSize": 16,
            "letterSpacing": 0.1,
            "lineHeight": 28,
            "marginTop": 8,
            "textAlign": "center",
          }
        }
      >
        To continue receiving our discounted charging rate, check your payment information is correct and retry the payment.
      </Text>
    </View>
    <View
      style={
        {
          "paddingLeft": 16,
        }
      }
    >
      <View
        style={
          {
            "display": "flex",
          }
        }
      >
        <Text
          needsPaddingBottom={true}
          style={
            {
              "color": "#212121",
              "fontSize": 16,
              "fontWeight": "400",
              "lineHeight": 28,
              "paddingBottom": 8,
              "paddingLeft": 0,
            }
          }
        >
          Subscription payment card
        </Text>
      </View>
    </View>
    <View
      style={
        {
          "flexBasis": 0,
          "flexGrow": 0.1,
          "flexShrink": 1,
          "marginBottom": 24,
          "marginLeft": 24,
          "marginRight": 24,
          "marginTop": 25,
        }
      }
    >
      <View
        accessibilityHint="Retry payment now"
        accessibilityLabel="Retry payment now"
        accessibilityRole="button"
        accessibilityState={
          {
            "busy": undefined,
            "checked": undefined,
            "disabled": true,
            "expanded": undefined,
            "selected": undefined,
          }
        }
        accessibilityValue={
          {
            "max": undefined,
            "min": undefined,
            "now": undefined,
            "text": undefined,
          }
        }
        accessible={true}
        collapsable={false}
        focusable={true}
        onClick={[Function]}
        onResponderGrant={[Function]}
        onResponderMove={[Function]}
        onResponderRelease={[Function]}
        onResponderTerminate={[Function]}
        onResponderTerminationRequest={[Function]}
        onStartShouldSetResponder={[Function]}
        style={
          {
            "alignItems": "center",
            "backgroundColor": "#80b2e6",
            "borderBottomLeftRadius": 23,
            "borderBottomRightRadius": 23,
            "borderColor": "transparent",
            "borderStyle": "solid",
            "borderTopLeftRadius": 23,
            "borderTopRightRadius": 23,
            "borderWidth": 0,
            "justifyContent": "center",
            "minHeight": 46,
            "opacity": 1,
            "paddingHorizontal": 25.5,
            "paddingVertical": 12,
          }
        }
        testID="RetryPaymentNow.Button"
      >
        <View>
          <Text
            disabled={true}
            inverted={false}
            loadingText="Retry payment now"
            size="large"
            style={
              {
                "color": "#ffffff",
                "fontFamily": "Roboto-Regular",
                "fontSize": 15,
                "letterSpacing": 0.7,
                "textAlign": "center",
              }
            }
            type="primary"
          >
            Retry payment now
          </Text>
        </View>
      </View>
    </View>
    <Modal
      animationType="fade"
      hardwareAccelerated={false}
      onRequestClose={[Function]}
      style={
        {
          "flexBasis": 0,
          "flexGrow": 1,
          "flexShrink": 0,
          "width": "100%",
        }
      }
      transparent={true}
      visible={
        {
          "isModalVisible": true,
        }
      }
    />
  </RCTSafeAreaView>,
  ",",
]
`;
