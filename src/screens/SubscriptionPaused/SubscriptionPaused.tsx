import { useMutation } from '@apollo/client';
import { useWallet } from '@bp/bppay-wallet-feature';
import {
  ActionConfirmationModal,
  Button,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import React, { useCallback, useEffect } from 'react';
import { View } from 'react-native';

import PausedImage from '../../../assets/images/pausedImage.png';
import {
  SubsAnalyticsEventSubsUnpaidSubscriptionScreenOpen,
  SubsAnalyticsEventSubsUnpaidSubscriptionScreenRetryClick,
  SubsAnalyticsEventSubsUnpaidSubsRetryPaymentFailure,
  SubsAnalyticsEventSubsUnpaidSubsRetryPaymentSuccess,
} from '../../analytics/events';
import { RETRY_WALLET_SUBS_PAYMENT } from '../../client/queries';
import { Countries, SubscriptionScreenNames } from '../../common/enums';
import {
  IRetryWalletSubsPaymentRequest,
  IRetryWalletSubsPaymentResponse,
} from '../../common/interfaces';
import { CardDescription } from '../../components/Atoms/CardDescription/CardDescription';
import { Notifications } from '../../components/Molecules/Notifications/Notifications';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { WalletSubscriptionData } from '../../providers/WalletSubscriptionDataProvider';
import { getCountrySpecificText } from '../../utils/helpers';
import * as S from './SubscriptionPaused.styles';

const SubscriptionPaused = () => {
  const { t, isInternetReachable, cardComponent, onAnalyticsEvent, user } =
    useSettings();
  const { navigate, setParams, getParams } = useHostNavigation();
  const isModalVisible = getParams('isModalVisible') ?? false;
  const { userInfo } = useUserInfo();
  const { walletSubscriptionData } = WalletSubscriptionData();
  const {
    subsDefaultNotificationTrigger,
    setSubsDefaultNotificationTrigger,
    subsDefaultCardError,
    setSubsDefaultCardError,
  } = useWallet();

  const onComplete = useCallback(() => {
    setSubsDefaultCardError(false);
    setSubsDefaultNotificationTrigger(false);
  }, [setSubsDefaultCardError, setSubsDefaultNotificationTrigger]);

  const onRetryPaymentFailure = (error: string) => {
    onAnalyticsEvent(
      SubsAnalyticsEventSubsUnpaidSubsRetryPaymentFailure({
        error_message: error,
      }),
    );
    return navigate(SubscriptionScreenNames.PaymentFailed);
  };

  const [retryWalletSubsPayment, { loading }] = useMutation<
    IRetryWalletSubsPaymentResponse,
    IRetryWalletSubsPaymentRequest
  >(RETRY_WALLET_SUBS_PAYMENT, {
    onCompleted: (data) => {
      const { error } = data.retryWalletSubsPayment;
      if (error) {
        return onRetryPaymentFailure(error);
      }

      onAnalyticsEvent(
        SubsAnalyticsEventSubsUnpaidSubsRetryPaymentSuccess({
          tag_ids: userInfo?.tagIds || [],
        }),
      );
      return navigate(SubscriptionScreenNames.PaymentSuccess);
    },
    onError: (error) => {
      onRetryPaymentFailure(error.message);
    },
  });

  const onPressRetry = async () => {
    onAnalyticsEvent(
      SubsAnalyticsEventSubsUnpaidSubscriptionScreenRetryClick({
        tag_ids: userInfo?.tagIds || [],
      }),
    );
    await retryWalletSubsPayment({
      variables: {
        userId: userInfo?.userId ?? '',
        membershipId: walletSubscriptionData?.getWalletSubscription.id ?? '',
        country: userInfo?.country,
      },
    });
  };

  const onHandleClick = (shouldNavigate: boolean) => {
    setParams({ isModalVisible: false });
    if (shouldNavigate) {
      if (userInfo?.country?.toString() === Countries.DE) {
        navigate(SubscriptionScreenNames.ConfirmCancelComfortMembership);
      } else {
        navigate(SubscriptionScreenNames.CancelMembership);
      }
    }
  };

  useEffect(() => {
    onAnalyticsEvent(
      SubsAnalyticsEventSubsUnpaidSubscriptionScreenOpen({
        tag_ids: userInfo?.tagIds || [],
      }),
    );
  }, [onAnalyticsEvent, userInfo?.tagIds]);
  return (
    <S.ScreenWrapper>
      <S.StyledIllustrationView>
        <S.StyledIllustration source={PausedImage} />
      </S.StyledIllustrationView>

      <S.SubscriptionPausedContainer>
        <S.SubPausedText testID="SubscriptionPaused.Label">
          {getCountrySpecificText(
            t,
            'pausedSubscription.PAUSE_TITLE',
            user?.country,
          )}
        </S.SubPausedText>
        <S.SubPausedSubtext>
          {getCountrySpecificText(
            t,
            'pausedSubscription.PAUSE_DESCRIPTION',
            user?.country,
          )}
        </S.SubPausedSubtext>
      </S.SubscriptionPausedContainer>
      <View style={{ paddingLeft: 16 }}>
        <CardDescription
          title={getCountrySpecificText(
            t,
            'pausedSubscription.SUBSCRIPTION_CARD',
            user?.country,
          )}
        />
      </View>
      {cardComponent && (
        <S.PaymentContainer>{cardComponent}</S.PaymentContainer>
      )}
      <S.ButtonContainer>
        <Button
          type={ButtonAction.PRIMARY}
          disabled={!isInternetReachable || loading}
          loading={loading}
          loadingText={t.pausedSubscription.RETRY_PAYMENT}
          loadingColor="white"
          onPress={onPressRetry}
          size={ButtonSize.DEFAULT}
          testID="RetryPaymentNow.Button"
          accessibilityLabel={t.pausedSubscription.RETRY_PAYMENT}
          accessibilityHint={t.pausedSubscription.RETRY_PAYMENT}
        >
          {t.pausedSubscription.RETRY_PAYMENT}
        </Button>
      </S.ButtonContainer>
      <ActionConfirmationModal
        isVisible={isModalVisible}
        titleText={getCountrySpecificText(
          t,
          'pausedSubscription.CANCEL_MODAL.TITLE',
          user?.country,
        )}
        titleMessage={getCountrySpecificText(
          t,
          'pausedSubscription.CANCEL_MODAL.DESCRIPTION',
          user?.country,
        )}
        primaryButtonText={getCountrySpecificText(
          t,
          'pausedSubscription.CANCEL_MODAL.KEEP',
          user?.country,
        )}
        accessibilityLabel={getCountrySpecificText(
          t,
          'pausedSubscription.CANCEL_MODAL.KEEP',
          user?.country,
        )}
        primaryButtonOnPress={() => onHandleClick(false)}
        secondaryButtonText={getCountrySpecificText(
          t,
          'pausedSubscription.CANCEL_MODAL.LOSE',
          user?.country,
        )}
        secondaryAccessibilityLabel={getCountrySpecificText(
          t,
          'pausedSubscription.CANCEL_MODAL.LOSE',
          user?.country,
        )}
        secondaryButtonOnPress={() => onHandleClick(true)}
      />
      {(subsDefaultNotificationTrigger || subsDefaultCardError) && (
        <Notifications
          success={!subsDefaultCardError}
          onComplete={onComplete}
        />
      )}
    </S.ScreenWrapper>
  );
};

export default SubscriptionPaused;
