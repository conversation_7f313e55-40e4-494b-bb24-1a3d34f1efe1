import { MockedProvider } from '@apollo/client/testing';
import { NavigationContainer } from '@react-navigation/native';
import { render, waitFor } from '@testing-library/react-native';

import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import SubscriptionPaused from './SubscriptionPaused';

const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  onAnalyticsEvent: () => jest.fn(),
  featureFlags: {
    introPercentageDiscount: 20,
  },
};

const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));
jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => ({
    navigate: jest.fn(),
    setParams: jest.fn(),
    getParams: jest.fn(() => ({ isModalVisible: true })),
  }),
}));

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useRoute: () => ({
      params: { isModalVisible: true },
    }),
  };
});

const renderWithTheme = (mock?: any) =>
  render(
    <NavigationContainer>
      <Theme>
        <MockedProvider mocks={mock} addTypename={false}>
          <SubscriptionPaused />
        </MockedProvider>
      </Theme>
      ,
    </NavigationContainer>,
  );

describe('Subscription Paused Screen', () => {
  it('renders without crashing', () => {
    const { toJSON } = renderWithTheme();
    expect(toJSON()).toMatchSnapshot();
  });

  it('renders button with accessibility label', async () => {
    const { getByLabelText } = renderWithTheme();
    await waitFor(() => {
      const button = getByLabelText(
        mockTranslations.pausedSubscription.RETRY_PAYMENT,
      );
      expect(button).toBeDefined();
    });
  });

  it('should render text on the page', () => {
    const { getByText } = renderWithTheme();
    expect(
      getByText(mockTranslations.pausedSubscription.PAUSE_TITLE),
    ).toBeTruthy();
    expect(
      getByText(mockTranslations.pausedSubscription.PAUSE_DESCRIPTION),
    ).toBeTruthy();
  });
});
