import { Platform, StatusBar } from 'react-native';
import { styled } from 'styled-components/native';

export const ScreenWrapper = styled.SafeAreaView`
  flex: 1;
  padding-top: ${Platform.OS === 'android'
    ? `${StatusBar.currentHeight}px`
    : '0px'};
`;

export const StyledIllustration = styled.Image`
  width: 285px;
  height: 285px;
`;

export const StyledIllustrationView = styled.View`
  flex: 0.6;
  align-items: center;
  margin-left: 87px;
  margin-right: 87px;
`;

export const SubscriptionPausedContainer = styled.View`
  flex: 0.5;
  margin-top: 8px;
  margin-left: 24px;
  margin-right: 24px;
`;

export const SubPausedText = styled.Text`
  font-family: 'Roboto-Regular';
  font-size: 18px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.2px;
  line-height: 32px;
  text-align: center;
`;

export const SubPausedSubtext = styled.Text`
  font-family: 'Roboto-Regular';
  margin-top: 8px;
  font-size: 16px;
  color: rgb(17, 17, 17);
  letter-spacing: 0.1px;
  line-height: 28px;
  text-align: center;
`;

export const ButtonContainer = styled.View`
  flex: 0.1;
  margin-top: 25px;
  margin-left: 24px;
  margin-right: 24px;
  margin-bottom: 24px;
`;

export const PaymentContainer = styled.View`
  height: 80px;
  background-color: ${({ theme }) => theme.bpCore.colors.appBackground.primary};
`;
