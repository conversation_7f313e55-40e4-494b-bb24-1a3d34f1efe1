import { LoadingIndicator, Modal } from '@bp/ui-components/mobile';
import {
  Button,
  ButtonAction,
  ButtonSize,
} from '@bp/ui-components/mobile/core';
import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useEffect, useState } from 'react';

import { SubsAnalyticsEventUpgradePaymentRequiredOpen } from '../../analytics/events';
import { SubscriptionScreenNames } from '../../common/enums';
import OverscrollContainer from '../../components/Atoms/OverscrollContainer/OverscrollContainer';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useUserInfo } from '../../providers/UserInfoProvider';
import { getAnalyticsPayload } from '../../utils/helpers';
import * as S from './PaymentRequired.styles';

const PaymentNeededIllustration = require('../../../assets/images/paymentsNeededIcon.png');

const PaymentRequired = () => {
  const { onPaymentRequired, t } = useSettings();
  const [isButtonFloating, setIsButtonFloating] = useState(true);
  const { userInfo, refetchUserInfo } = useUserInfo();
  const { navigate } = useHostNavigation();
  const [loading, setLoading] = useState(false);

  const fetchData = useCallback(async () => {
    if (refetchUserInfo) {
      setLoading(true);
      await refetchUserInfo();
    }
    if ((userInfo?.balance ?? 0) >= 0) {
      navigate(SubscriptionScreenNames.SubscriptionLanding);
    }
    setLoading(false);
  }, [navigate, refetchUserInfo, userInfo?.balance]);

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [fetchData]),
  );
  useEffect(() => {
    SubsAnalyticsEventUpgradePaymentRequiredOpen(getAnalyticsPayload(userInfo));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onPressCallback = useCallback(() => {
    onPaymentRequired();
  }, [onPaymentRequired]);

  return (
    <S.ScreenWrapper>
      {loading ? (
        <Modal visible={loading} fullWidthVisible={false} shadow={false}>
          <LoadingIndicator color="black" />
        </Modal>
      ) : (
        <>
          <OverscrollContainer willButtonFloat={setIsButtonFloating}>
            <S.StyledImage
              source={PaymentNeededIllustration}
              resizeMode="center"
            />
            <S.MessageText>{t.paymentRequired.MESSAGE_ONE}</S.MessageText>
            <S.MessageText>{t.paymentRequired.MESSAGE_TWO}</S.MessageText>
          </OverscrollContainer>
          <S.BottomFloatingButtonWrapper isFloating={isButtonFloating}>
            <Button
              accessibilityLabel={t.paymentRequired.BUTTON_TEXT}
              type={ButtonAction.PRIMARY}
              size={ButtonSize.DEFAULT}
              onPress={onPressCallback}
            >
              {t.paymentRequired.BUTTON_TEXT}
            </Button>
          </S.BottomFloatingButtonWrapper>
        </>
      )}
    </S.ScreenWrapper>
  );
};

export default PaymentRequired;
