import { Dimensions } from 'react-native';
import { styled } from 'styled-components/native';

const WIDTH = Dimensions.get('window').width;
const RATIO = WIDTH / 1077;

interface FloatingButton {
  isFloating: boolean;
}

export const ScreenWrapper = styled.View`
  flex: 1;
  width: 100%;
  overflow: hidden;
  background-color: white;
`;

export const StyledImage = styled.Image`
  width: ${WIDTH}px;
  height: ${380 * RATIO}px;
  margin-top: 32px;
  margin-bottom: 10px;
`;

export const MessageText = styled.Text`
  padding-horizontal: 24px;
  padding-top: 24px;
  font-size: 16px;
  text-align: left;
  line-height: 24px;
  letter-spacing: 0.7px;
  color: #111;
`;

export const BottomFloatingButtonWrapper = styled.View<FloatingButton>(
  ({ isFloating }) => `
  width: 100%;
  padding: 24px;
  justify-content: center;
  background-color: white;
  shadow-color: black;
  shadow-offset: ${isFloating ? '0px -10px' : '0px'};
  shadow-opacity: ${isFloating ? '0.11' : '0'};
  shadow-radius: ${isFloating ? '7px' : '0px'};
  elevation: ${isFloating ? '20' : '0'};
`,
);
