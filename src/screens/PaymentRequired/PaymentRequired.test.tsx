import { ThemeProvider } from '@bp/ui-components/mobile';
import { act, fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import { SubscriptionScreenNames } from '../../common/enums';
import PaymentRequired from './PaymentRequired';

const testBtnTxt = 'Test Button';
const mockedOnPaymentRequired = jest.fn();

jest.mock('../../providers/Settings', () => ({
  useSettings: () => ({
    t: {
      paymentRequired: {
        MESSAGE_ONE: 'Test Message One',
        MESSAGE_TWO: 'Test Message Two',
        BUTTON_TEXT: testBtnTxt,
      },
    },
    onPaymentRequired: mockedOnPaymentRequired,
  }),
}));

let focusEffectCallback: (() => any) | null;

jest.mock('@react-navigation/native', () => ({
  useFocusEffect: (callback: any) => {
    focusEffectCallback = callback;
  },
}));

const defaultUserInfoProvider = {
  useUserInfo: () => ({
    userInfo: { balance: -10 },
    refetchUserInfo: jest.fn(),
  }),
};

const defaultHostNavigationProvider = {
  useHostNavigation: () => ({
    navigate: jest.fn(),
  }),
};

jest.mock('../../providers/UserInfoProvider', () => defaultUserInfoProvider);
jest.mock(
  '../../providers/HostNavigationProvider',
  () => defaultHostNavigationProvider,
);

describe('PaymentRequired', () => {
  beforeEach(() => {
    focusEffectCallback = null;
  });

  it('renders correctly', () => {
    const { getByText } = render(
      <ThemeProvider>
        <PaymentRequired />
      </ThemeProvider>,
    );

    expect(getByText('Test Message One')).toBeTruthy();
    expect(getByText('Test Message Two')).toBeTruthy();
    expect(getByText(testBtnTxt)).toBeTruthy();
  });

  it('calls onPaymentRequired when button is pressed', async () => {
    const { getByText } = render(
      <ThemeProvider>
        <PaymentRequired />
      </ThemeProvider>,
    );
    const button = getByText('Test Button');

    await act(async () => {
      fireEvent.press(button);
    });

    expect(mockedOnPaymentRequired).toHaveBeenCalled();
  });

  it('fetchData updates the user balance and navigates when balance is >= 0', async () => {
    const refetchUserInfo = jest.fn().mockResolvedValue(null);
    const navigate = jest.fn();

    jest
      .spyOn(defaultUserInfoProvider, 'useUserInfo')
      .mockImplementation(() => ({
        userInfo: { balance: 10 },
        refetchUserInfo,
      }));

    jest
      .spyOn(defaultHostNavigationProvider, 'useHostNavigation')
      .mockImplementation(() => ({
        navigate,
      }));

    render(
      <ThemeProvider>
        <PaymentRequired />
      </ThemeProvider>,
    );

    await act(async () => {
      // @ts-expect-error
      await focusEffectCallback();
    });

    expect(refetchUserInfo).toHaveBeenCalled();
    expect(navigate).toHaveBeenCalledWith(
      SubscriptionScreenNames.SubscriptionLanding,
    );
  });
});
