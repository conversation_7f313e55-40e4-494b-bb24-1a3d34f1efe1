// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SubscriptionLanding component should render correctly from subscription 1`] = `
<View
  style={
    {
      "alignItems": "center",
      "display": "flex",
      "flexBasis": "50%",
      "flexDirection": "column",
      "justifyContent": "flex-end",
    }
  }
>
  <Text
    style={
      {
        "color": "rgb(17, 17, 17)",
        "fontSize": 18,
        "marginBottom": 24,
        "textAlign": "center",
      }
    }
  >
    Loading
  </Text>
  <View
    collapsable={false}
    color="black"
    size={36}
    spinningColor="white"
    style={
      {
        "backgroundColor": "transparent",
        "borderBottomColor": "white",
        "borderBottomLeftRadius": 18,
        "borderBottomRightRadius": 18,
        "borderLeftColor": "black",
        "borderRightColor": "black",
        "borderStyle": "solid",
        "borderTopColor": "black",
        "borderTopLeftRadius": 18,
        "borderTopRightRadius": 18,
        "borderWidth": 3,
        "height": 36,
        "transform": [
          {
            "rotate": "0deg",
          },
        ],
        "width": 36,
      }
    }
    testID="LoadingIndicator"
    thickness={3}
  />
</View>
`;
