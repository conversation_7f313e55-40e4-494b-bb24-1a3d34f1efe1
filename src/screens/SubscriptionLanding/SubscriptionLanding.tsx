import {
  CommonActions,
  NavigationContainerRef,
  NavigationProp,
} from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import React, { useEffect, useState } from 'react';
import { StatusBar, TouchableOpacity } from 'react-native';

import { RootStackParamList } from '../../../types/types';
import { SubsAnalyticsEventSubsUnpaidSubscriptionScreenExitClick } from '../../analytics/events';
import { SubscriptionScreenNames } from '../../common/enums';
import { BackIcon } from '../../components';
import HeaderBackButton from '../../components/Atoms/HeaderBackButton/HeaderBackButton';
import HeaderCloseButton from '../../components/Atoms/HeaderCloseButton/HeaderCloseButton';
import AppStatusBar from '../../components/Molecules/AppStatusBar/AppStatusBar';
import MultilineHeader from '../../components/Molecules/MultilineHeader/MultilineHeader';
import GoCardlessMembership from '../../components/Organisms/GocardlessMembership/GocardlessMembership';
import JoinGocardless from '../../components/Organisms/JoinGocardless/JoinGocardless';
import { LoadingPage } from '../../components/Organisms/LoadingPage/LoadingPage';
import MembershipOnboard from '../../components/Organisms/MembershipOnboard/MembershipOnboard';
import { NetworkConnectionErrorPage } from '../../components/Organisms/NetworkConnectionErrorPage/NetworkConnectionErrorPage';
import { useHostNavigation } from '../../providers/HostNavigationProvider';
import { useSettings } from '../../providers/Settings';
import { useSubscriptionContext } from '../../providers/SubscriptionProvider';
import { useUserInfo } from '../../providers/UserInfoProvider';
import {
  getCountrySpecificText,
  getInitialRouteName,
} from '../../utils/helpers';
import MyComfortMembership from '../MyComfortMembership/MyComfortMembership';
import { MySubscription } from '../MySubscription/MySubscription';
import PendingMembership from '../PendingMembership/PendingMembership';
import { SubscriptionEnding } from '../SubscriptionEnding/SubscriptionEnding';
import SubscriptionPaused from '../SubscriptionPaused/SubscriptionPaused';

const Stack = createStackNavigator();

export const handleBackPress = (
  navigation: NavigationContainerRef<{}>,
  onNavigateToProfile: () => void,
) => {
  const screensToFilter: string[] = [
    SubscriptionScreenNames.CancelMembership,
    SubscriptionScreenNames.CancelSubscriptionFailed,
    SubscriptionScreenNames.SubscriptionFailed,
    SubscriptionScreenNames.Loading,
  ];
  const state = navigation.getState();

  type Route = (typeof state.routes)[number];

  const routes: Route[] = state.routes.map((route) => {
    const nestedState = route.state as typeof state;
    if (
      (route.name === 'SubscriptionScreens' || route.name === 'Subscription') &&
      nestedState
    ) {
      // Filter out the screens we don't want to go back to
      const nestedRoutes = nestedState.routes.filter(
        (nestedRoute) => !screensToFilter.includes(nestedRoute.name),
      );
      return {
        ...route,
        state: {
          ...nestedState,
          routes: nestedRoutes,
          index: nestedRoutes.length - 1,
        },
      };
    }
    return route;
  });
  navigation.dispatch(
    CommonActions.reset({ ...state, routes, index: routes.length - 1 }),
  );
  if (navigation.canGoBack()) {
    navigation.goBack();
  } else {
    onNavigateToProfile();
  }
};

export const headerLeftFn = (
  navigation: NavigationContainerRef<{}>,
  onNavigateToProfile: () => void,
  color?: string | null,
  accessibilityLabel?: string,
) => {
  return (
    <TouchableOpacity
      accessibilityLabel={accessibilityLabel}
      style={{ padding: 16 }}
      onPress={() => handleBackPress(navigation, onNavigateToProfile)}
    >
      <BackIcon color={color ?? '#000'} />
    </TouchableOpacity>
  );
};

export default ({ route }: any) => {
  const { params } = route ?? {};
  const { navigation } = useHostNavigation();
  const [currentRoute, setRoute] = useState<string | undefined>(undefined);
  const [initialRouteName, setInitialRouteName] = useState<string>();
  const {
    isPausedModalVisible,
    setIsPausedModalVisible,
    setIsSubscriptionPaused,
  } = useSubscriptionContext();
  const { userInfo, isLoading, error } = useUserInfo();
  const {
    t,
    isInternetReachable,
    onNavigateToProfile,
    onAnalyticsEvent,
    user,
  } = useSettings();
  const handlePaused =
    (nav: NavigationProp<RootStackParamList, 'SubscriptionPaused'>) => () => {
      onAnalyticsEvent(
        SubsAnalyticsEventSubsUnpaidSubscriptionScreenExitClick({
          tag_ids: userInfo?.tagIds || [],
        }),
      );
      setIsPausedModalVisible(false);
      setIsSubscriptionPaused(true);
      nav.setParams({ isModalVisible: true });
    };

  useEffect(() => {
    if (!userInfo?.type) {
      return;
    }
    const routeName = params?.screen ?? getInitialRouteName(userInfo);
    setInitialRouteName(routeName);
  }, [userInfo, params]);

  useEffect(() => {
    const routes = navigation.getState()?.routes;
    const numberOfRoutes = routes?.length || 1;
    const currentRouteIndex = numberOfRoutes - 1;

    setRoute(routes?.[currentRouteIndex]?.name);
  }, [navigation]);

  if (isInternetReachable && (isLoading || !initialRouteName)) {
    return <LoadingPage text={t.gocardless.text.LOADING} />;
  }

  if (!isInternetReachable || error) {
    return <NetworkConnectionErrorPage />;
  }

  return (
    <>
      <AppStatusBar
        barStyle={
          currentRoute &&
          currentRoute === SubscriptionScreenNames.JoinGoCardless
            ? 'light-content'
            : 'dark-content'
        }
      />

      <Stack.Navigator
        initialRouteName={initialRouteName}
        screenOptions={{
          cardStyle: { backgroundColor: '#ffffff' },
          headerTitleStyle: {
            fontWeight: 'normal',
            fontFamily: 'arial',
          },
        }}
      >
        <Stack.Screen
          name={SubscriptionScreenNames.JoinGoCardless}
          component={JoinGocardless}
          options={{
            cardStyle: {
              marginTop: StatusBar.currentHeight,
            },
            header: () =>
              MultilineHeader({
                title: getCountrySpecificText(
                  t,
                  'gocardless.text.SUBSCRIBE_HEADER',
                  user?.country,
                ),
                LeftButton: (
                  <HeaderBackButton
                    navigation={navigation}
                    customOnPress={() => {
                      handleBackPress(navigation, onNavigateToProfile);
                    }}
                    accessibilityLabel={t.gocardless.text.GO_BACK}
                    color="#fff"
                  />
                ),
                color: '#fff',
                backgroundColor: '#10109a',
              }),
          }}
        />
        <Stack.Screen
          name={SubscriptionScreenNames.MembershipOnboard}
          component={MembershipOnboard}
          options={{
            title: t.membershipOnboard.screenTitle,
            headerStyle: {
              backgroundColor: '#10109a',
              shadowColor: 'transparent',
            },
            headerTitleAlign: 'center',
            headerTintColor: '#fff',
            headerLeft: () =>
              headerLeftFn(
                navigation,
                onNavigateToProfile,
                '#fff',
                t.gocardless.text.GO_BACK,
              ),
          }}
        />
        <Stack.Screen
          name={SubscriptionScreenNames.GoCardlessMembership}
          component={GoCardlessMembership}
          options={{
            title: t.gocardless.text.MEMBERSHIP_HEADER,
            headerTitleAlign: 'center',
            headerLeft: () =>
              headerLeftFn(
                navigation,
                onNavigateToProfile,
                null,
                t.gocardless.text.GO_BACK,
              ),
          }}
        />
        <Stack.Screen
          name={SubscriptionScreenNames.MyComfortMembership}
          component={MyComfortMembership}
          options={{
            cardStyle: {
              marginTop: StatusBar.currentHeight,
            },
            header: () =>
              MultilineHeader({
                title: t.myComfortMembership.header,
                LeftButton: (
                  <HeaderBackButton
                    navigation={navigation}
                    customOnPress={() => {
                      handleBackPress(navigation, onNavigateToProfile);
                    }}
                    accessibilityLabel={t.gocardless.text.GO_BACK}
                    color="#fff"
                  />
                ),
                color: '#fff',
                backgroundColor: '#10109a',
              }),
          }}
        />

        <Stack.Screen
          name={SubscriptionScreenNames.PendingMembership}
          component={PendingMembership}
          options={{
            title: 'Pending Membership',
            headerLeft: () =>
              headerLeftFn(
                navigation,
                onNavigateToProfile,
                null,
                t.gocardless.text.GO_BACK,
              ),
            headerTitleAlign: 'center',
          }}
        />

        <Stack.Screen
          name={SubscriptionScreenNames.MySubscription}
          component={MySubscription}
          options={{
            cardStyle: {
              marginTop: StatusBar.currentHeight,
            },
            header: () =>
              MultilineHeader({
                title: getCountrySpecificText(
                  t,
                  'mySubscription.HEADER',
                  user?.country,
                ),
                LeftButton: (
                  <HeaderBackButton
                    navigation={navigation}
                    customOnPress={() => {
                      handleBackPress(navigation, onNavigateToProfile);
                    }}
                    accessibilityLabel={t.gocardless.text.GO_BACK}
                    color="#fff"
                  />
                ),
                color: '#fff',
                backgroundColor: '#10109a',
              }),
          }}
        />
        <Stack.Screen
          name={SubscriptionScreenNames.SubscriptionPaused}
          component={SubscriptionPaused}
          options={({ navigation: appNav }) => ({
            title: getCountrySpecificText(
              t,
              'pausedSubscription.HEADER',
              user?.country,
            ),
            headerRight: () => (
              <HeaderCloseButton
                onPress={handlePaused(appNav)}
                color={'#000'}
              />
            ),
            headerTitleAlign: 'center',
          })}
          initialParams={{ isModalVisible: isPausedModalVisible }}
        />
        <Stack.Screen
          name={SubscriptionScreenNames.SubscriptionEnding}
          component={SubscriptionEnding}
          options={{
            title: getCountrySpecificText(
              t,
              'mySubscription.HEADER',
              user?.country,
            ),
            headerStyle: {
              backgroundColor: '#10109a',
              shadowColor: 'transparent',
            },
            headerTitleAlign: 'center',
            headerTintColor: '#fff',
            headerLeft: () =>
              HeaderBackButton({
                navigation,
                customOnPress: onNavigateToProfile,
                color: '#fff',
              }),
          }}
        />
      </Stack.Navigator>
    </>
  );
};
