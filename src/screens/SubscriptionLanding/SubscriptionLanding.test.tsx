import { createNavigationContainerRef } from '@react-navigation/native';
import { act, render } from '@testing-library/react-native';
import React from 'react';

import { SubscriptionScreenNames, UserTypeEnum } from '../../common/enums';
import Theme from '../../themes/Theme';
import { TRANSLATIONS } from '../../translations';
import * as helpers from '../../utils/helpers';
import SubscriptionLanding, { headerLeftFn } from './SubscriptionLanding';

jest.mock('react-native-webview', () => 'WebView');

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

jest.mock('@react-navigation/stack', () => ({
  createStackNavigator: () => ({
    Navigator: jest.fn(),
    Screen: jest.fn(),
  }),
}));

const mockNavigate = jest.fn();
jest.mock('../../providers/HostNavigationProvider', () => ({
  useHostNavigation: () => {
    return {
      navigate: mockNavigate,
      navigation: {
        getState: jest.fn(),
      },
    };
  },
}));
const mockTranslations = TRANSLATIONS.en_GB;
const mockUseSettingsObj = {
  t: mockTranslations,
  user: { id: '123' },
  getToken: () => jest.fn().mockReturnValue('mock'),
  country: 'NL',
  onAnalyticsEvent: () => {},
  isInternetReachable: true,
};
const mockUseSettings = jest.fn().mockReturnValue(mockUseSettingsObj);
jest.mock('../../providers/Settings', () => ({
  useSettings: () => mockUseSettings(),
}));

const mockUserInfoObj = {
  userInfo: {
    gocardless: { mandateStatus: 'mock', mandateId: 'mock' },
  },
  isLoading: true,
  refetchUserInfo: jest.fn(),
};

const mockUserInfo = jest.fn().mockReturnValue(mockUserInfoObj);
jest.mock('../../providers/UserInfoProvider', () => ({
  useUserInfo: () => mockUserInfo(),
}));

const getInitialRouteSpy = jest.spyOn(helpers, 'getInitialRouteName');

const params = { from: 'account' };

const renderWithTheme = () =>
  render(
    <Theme>
      <SubscriptionLanding route={params} />
    </Theme>,
  );

describe('headerLeftFn', () => {
  it('should render the BackIcon component with correct onPress behavior', () => {
    const mockNavigation = createNavigationContainerRef();
    mockNavigation.getState = jest.fn().mockReturnValue({
      routes: [],
    });
    mockNavigation.dispatch = jest.fn();
    mockNavigation.goBack = jest.fn();
    const onNavigateToProfile = jest.fn();

    const color = '#123456';

    const headerLeftComponent = headerLeftFn(
      mockNavigation,
      onNavigateToProfile,
      color,
    );

    expect(headerLeftComponent.type.displayName).toBe('TouchableOpacity');

    headerLeftComponent.props.onPress();
    expect(mockNavigation.dispatch).toHaveBeenCalled();
    expect(onNavigateToProfile).toHaveBeenCalled();

    const backIconComponent = headerLeftComponent.props.children;
    expect(backIconComponent.props.color).toBe(color);
  });

  it('should render the BackIcon component with correct onPress behavior when route.name is SubscriptionScreens', () => {
    const mockNavigation = createNavigationContainerRef();
    mockNavigation.getState = jest.fn().mockReturnValue({
      routes: [
        {
          name: 'SubscriptionScreens',
          state: {
            routes: [
              { name: SubscriptionScreenNames.CancelMembership },
              { name: SubscriptionScreenNames.JoinGoCardless },
            ],
          },
        },
      ],
    });
    mockNavigation.dispatch = jest.fn();
    mockNavigation.goBack = jest.fn();
    const onNavigateToProfile = jest.fn();

    const color = '#123456';

    const headerLeftComponent = headerLeftFn(
      mockNavigation,
      onNavigateToProfile,
      color,
    );

    expect(headerLeftComponent.type.displayName).toBe('TouchableOpacity');

    headerLeftComponent.props.onPress();
    expect(mockNavigation.dispatch).toHaveBeenCalled();
    expect(onNavigateToProfile).toHaveBeenCalled();

    const backIconComponent = headerLeftComponent.props.children;
    expect(backIconComponent.props.color).toBe(color);
  });
});

describe('SubscriptionLanding component', () => {
  it('should render correctly from subscription', () => {
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: { ...mockUserInfoObj.userInfo, type: UserTypeEnum.PAYG },
    });
    const { toJSON } = render(
      <Theme>
        <SubscriptionLanding />
      </Theme>,
    );
    expect(toJSON()).toMatchSnapshot();
  });

  it('should return spinner when loading', () => {
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: { ...mockUserInfoObj.userInfo, type: UserTypeEnum.PAYG },
      isLoading: true,
    });

    const { queryAllByTestId } = renderWithTheme();

    expect(queryAllByTestId('Spinner')).toBeTruthy();
  });

  it('should navigate to GoCardless screen', () => {
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: { ...mockUserInfoObj.userInfo, type: UserTypeEnum.PAYG },
      isLoading: true,
    });

    act(() => {
      jest.useFakeTimers();
    });
    render(
      <Theme>
        <SubscriptionLanding />
      </Theme>,
    );

    expect(getInitialRouteSpy).toHaveReturnedWith(
      SubscriptionScreenNames.JoinGoCardless,
    );
  });

  it('should navigate to GoCardlessMembership screen ', () => {
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: { ...mockUserInfoObj.userInfo, type: UserTypeEnum.SUBS },
      isLoading: true,
    });

    render(
      <Theme>
        <SubscriptionLanding />
      </Theme>,
    );

    expect(getInitialRouteSpy).toHaveReturnedWith(
      SubscriptionScreenNames.GoCardlessMembership,
    );
    mockUserInfo.mockReturnValue(mockUserInfoObj);
    expect(mockUserInfoObj.isLoading).toEqual(true);
  });

  it('should show the No Signal Error if there is no internet connection', () => {
    mockUseSettings.mockReturnValue({
      ...mockUseSettingsObj,
      isInternetReachable: false,
    });
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: {
        ...mockUserInfoObj.userInfo,
        type: UserTypeEnum.SUBS,
      },
      isLoading: false,
    });

    const { getByText } = render(
      <Theme>
        <SubscriptionLanding />
      </Theme>,
    );

    expect(
      getByText(
        mockUseSettingsObj.t.networkConnectionError.text.SUB_HEADING_TEXT,
      ),
    ).toBeDefined();
    mockUserInfo.mockReturnValue(mockUserInfoObj);
    expect(mockUserInfoObj.isLoading).toEqual(true);
  });

  it('should show the No Signal Error if there is API error', async () => {
    mockUseSettings.mockReturnValue({
      ...mockUseSettingsObj,
      isInternetReachable: true,
    });
    mockUserInfo.mockReturnValue({
      ...mockUserInfoObj,
      userInfo: { ...mockUserInfoObj.userInfo, type: UserTypeEnum.SUBS },
      isLoading: false,
      error: 'Error',
    });

    const { getByText } = render(
      <Theme>
        <SubscriptionLanding />
      </Theme>,
    );

    act(() => {
      jest.useFakeTimers();
    });

    expect(
      getByText(
        mockUseSettingsObj.t.networkConnectionError.text.SUB_HEADING_TEXT,
      ),
    ).toBeDefined();
    mockUseSettings.mockReturnValue(mockUseSettingsObj);
    expect(mockUseSettingsObj.isInternetReachable).toEqual(true);
  });
});
