// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Index component should render screen correctly 1`] = `
<View
  style={
    {
      "flex": 1,
    }
  }
>
  <View
    style={
      [
        {
          "backgroundColor": "rgb(242, 242, 242)",
          "flex": 1,
        },
        undefined,
      ]
    }
  >
    <View
      collapsable={false}
      pointerEvents="box-none"
      style={
        {
          "zIndex": 1,
        }
      }
    >
      <View
        accessibilityElementsHidden={false}
        importantForAccessibility="auto"
        onLayout={[Function]}
        pointerEvents="box-none"
        style={null}
      >
        <View
          collapsable={false}
          pointerEvents="box-none"
          style={
            {
              "bottom": 0,
              "left": 0,
              "opacity": 1,
              "position": "absolute",
              "right": 0,
              "top": 0,
              "zIndex": 0,
            }
          }
        >
          <View
            collapsable={false}
            style={
              {
                "backgroundColor": "rgb(255, 255, 255)",
                "borderBottomColor": "rgb(216, 216, 216)",
                "flex": 1,
                "shadowColor": "rgb(216, 216, 216)",
                "shadowOffset": {
                  "height": 0.5,
                  "width": 0,
                },
                "shadowOpacity": 0.85,
                "shadowRadius": 0,
              }
            }
          />
        </View>
        <View
          collapsable={false}
          pointerEvents="box-none"
          style={
            {
              "height": 44,
              "maxHeight": undefined,
              "minHeight": undefined,
              "opacity": undefined,
              "transform": undefined,
            }
          }
        >
          <View
            pointerEvents="none"
            style={
              {
                "height": 0,
              }
            }
          />
          <View
            pointerEvents="box-none"
            style={
              {
                "alignItems": "stretch",
                "flex": 1,
                "flexDirection": "row",
              }
            }
          >
            <View
              collapsable={false}
              pointerEvents="box-none"
              style={
                {
                  "alignItems": "flex-start",
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "justifyContent": "center",
                  "marginStart": 0,
                  "opacity": 1,
                }
              }
            />
            <View
              collapsable={false}
              pointerEvents="box-none"
              style={
                {
                  "justifyContent": "center",
                  "marginHorizontal": 16,
                  "maxWidth": 288,
                  "opacity": 1,
                }
              }
            >
              <Text
                accessibilityRole="header"
                aria-level="1"
                collapsable={false}
                numberOfLines={1}
                onLayout={[Function]}
                style={
                  {
                    "color": "rgb(28, 28, 30)",
                    "fontSize": 17,
                    "fontWeight": "600",
                  }
                }
              >
                Subscription
              </Text>
            </View>
            <View
              collapsable={false}
              pointerEvents="box-none"
              style={
                {
                  "alignItems": "flex-end",
                  "flexBasis": 0,
                  "flexGrow": 1,
                  "justifyContent": "center",
                  "marginEnd": 0,
                  "opacity": 1,
                }
              }
            />
          </View>
        </View>
      </View>
    </View>
    <RNSScreenContainer
      onLayout={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <RNSScreen
        activityState={2}
        collapsable={false}
        gestureResponseDistance={
          {
            "bottom": -1,
            "end": -1,
            "start": -1,
            "top": -1,
          }
        }
        onGestureCancel={[Function]}
        pointerEvents="box-none"
        sheetAllowedDetents="large"
        sheetCornerRadius={-1}
        sheetExpandsWhenScrolledToEdge={true}
        sheetGrabberVisible={false}
        sheetLargestUndimmedDetent="all"
        style={
          {
            "bottom": 0,
            "left": 0,
            "position": "absolute",
            "right": 0,
            "top": 0,
          }
        }
      >
        <View
          collapsable={false}
          style={
            {
              "opacity": 1,
            }
          }
        />
        <View
          accessibilityElementsHidden={false}
          closing={false}
          gestureVelocityImpact={0.3}
          importantForAccessibility="auto"
          onClose={[Function]}
          onGestureBegin={[Function]}
          onGestureCanceled={[Function]}
          onGestureEnd={[Function]}
          onOpen={[Function]}
          onTransition={[Function]}
          pointerEvents="box-none"
          style={
            [
              {
                "display": "flex",
                "overflow": undefined,
              },
              {
                "bottom": 0,
                "left": 0,
                "position": "absolute",
                "right": 0,
                "top": 0,
              },
            ]
          }
          transitionSpec={
            {
              "close": {
                "animation": "spring",
                "config": {
                  "damping": 500,
                  "mass": 3,
                  "overshootClamping": true,
                  "restDisplacementThreshold": 10,
                  "restSpeedThreshold": 10,
                  "stiffness": 1000,
                },
              },
              "open": {
                "animation": "spring",
                "config": {
                  "damping": 500,
                  "mass": 3,
                  "overshootClamping": true,
                  "restDisplacementThreshold": 10,
                  "restSpeedThreshold": 10,
                  "stiffness": 1000,
                },
              },
            }
          }
        >
          <View
            collapsable={false}
            pointerEvents="box-none"
            style={
              {
                "flex": 1,
              }
            }
          >
            <View
              collapsable={false}
              handlerTag={1}
              handlerType="PanGestureHandler"
              needsOffscreenAlphaCompositing={false}
              onGestureHandlerEvent={[Function]}
              onGestureHandlerStateChange={[Function]}
              style={
                {
                  "flex": 1,
                  "transform": [
                    {
                      "translateX": 0,
                    },
                    {
                      "translateX": 0,
                    },
                  ],
                }
              }
            >
              <View
                pointerEvents="box-none"
                style={
                  [
                    {
                      "flex": 1,
                      "overflow": "hidden",
                    },
                    [
                      {
                        "backgroundColor": "rgb(242, 242, 242)",
                      },
                      undefined,
                    ],
                  ]
                }
              >
                <View
                  style={
                    {
                      "flex": 1,
                      "flexDirection": "column-reverse",
                    }
                  }
                >
                  <View
                    style={
                      {
                        "flex": 1,
                      }
                    }
                  >
                    <View
                      style={
                        {
                          "alignItems": "center",
                          "display": "flex",
                          "flexBasis": "50%",
                          "flexDirection": "column",
                          "justifyContent": "flex-end",
                        }
                      }
                    >
                      <Text
                        style={
                          {
                            "color": "rgb(17, 17, 17)",
                            "fontSize": 18,
                            "marginBottom": 24,
                            "textAlign": "center",
                          }
                        }
                      >
                        Loading
                      </Text>
                      <View
                        collapsable={false}
                        color="black"
                        size={36}
                        spinningColor="white"
                        style={
                          {
                            "backgroundColor": "transparent",
                            "borderBottomColor": "white",
                            "borderBottomLeftRadius": 18,
                            "borderBottomRightRadius": 18,
                            "borderLeftColor": "black",
                            "borderRightColor": "black",
                            "borderStyle": "solid",
                            "borderTopColor": "black",
                            "borderTopLeftRadius": 18,
                            "borderTopRightRadius": 18,
                            "borderWidth": 3,
                            "height": 36,
                            "transform": [
                              {
                                "rotate": "0deg",
                              },
                            ],
                            "width": 36,
                          }
                        }
                        testID="LoadingIndicator"
                        thickness={3}
                      />
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>
      </RNSScreen>
    </RNSScreenContainer>
  </View>
</View>
`;
