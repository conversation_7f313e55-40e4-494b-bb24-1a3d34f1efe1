import { DefaultTheme } from 'styled-components/native';

const white = '#FFFFFF';
const voucherStatus = '#CCCCEA';
const voucherText = '#353535';

// Primary
const pulseBlue = '#000096';
const bpGreen = '#009900';

// Secondary
const bpLightBlue = '#E6E9FA';

// Blacks
const black = '#000000';
const black2 = '#1D1D26';

// Greys
const grey1 = '#212121';
const grey2 = '#5B5B5B';
const grey3 = '#AFAFAF';
const grey4 = '#DEDEDE';
const grey5 = '#F5F5F5';
const grey6 = '#8E8E92';
const grey7 = '#D8D8D8';
const grey8 = '#DDDDDD';
const grey9 = '#616365';
const grey10 = '#FAFAFA';
const grey11 = '#D6D6D6';
const grey12 = '#959595';
const grey13 = '#111111';
const grey14 = '#dbd9db';
const red = '#e64949';

// States
const success = '#007F00';
const warning = '#FFBC3D';
const error = '#CF0001';

const successPale = '#E5F4E5';
const errorPale = '#FAE5E5';

const primary = pulseBlue;
const secondary = bpLightBlue;

const theme: Partial<DefaultTheme> = {
  // Add your own theme properties here
  subscriptionMfe: {
    color: {
      primary,
      black,
      black2,
      white,
      grey: {
        one: grey1,
        two: grey2,
        three: grey3,
        four: grey4,
        five: grey5,
        six: grey6,
        seven: grey7,
        eight: grey8,
        nine: grey9,
        ten: grey10,
        eleven: grey11,
        twelve: grey12,
        thirteen: grey13,
        fourteen: grey14,
      },
      state: {
        success,
        successPale,
        warning,
        error,
        errorPale,
      },
    },
    text: {
      color: {
        primary: black,
        secondary: grey9,
        tertiary: primary,
        error,
        strongSecondary: grey9,
        white,
      },
    },
    icons: {
      default: {
        color: black,
      },
      enabled: {
        color: primary,
      },
      disabled: {
        color: black,
      },
    },
    bottomNavigation: {
      background: white,
      enabled: {
        text: primary,
        icon: primary,
      },
      disabled: {
        text: grey9,
        icon: grey9,
      },
    },
    buttons: {
      primary: {
        background: primary,
        text: white,
        disabledBackground: '#7F7FCB',
        disabledText: white,
        borderColor: primary,
        disabledBorderColor: '#7F7FCB',
      },
      secondary: {
        background: secondary,
        text: 'rgba(43, 71, 230, 0.7)',
        disabledBackground: secondary,
        disabledText: grey12,
        borderColor: secondary,
        disabledBorderColor: secondary,
      },
      ghost: {
        background: 'rgba(100, 100, 100, 0)',
        text: white,
        disabledBackground: grey11,
        disabledText: grey12,
        borderColor: white,
        disabledBorderColor: white,
      },
      text: {
        background: white,
        text: primary,
        disabledBackground: grey11,
        disabledText: grey12,
        borderColor: white,
        disabledBorderColor: white,
      },
      small: {
        background: white,
        text: primary,
        disabledBackground: grey11,
        disabledText: grey12,
        borderColor: white,
        disabledBorderColor: white,
      },
      cancel: {
        background: '#F5CCCC',
        text: error,
        disabledBackground: grey11,
        disabledText: grey12,
        borderColor: '#F5CCCC',
        disabledBorderColor: '#F5CCCC',
      },
      secondaryGhost: {
        background: white,
        text: grey13,
        borderColor: primary,
        disabledBackground: grey11,
        disabledText: grey12,
        disabledBorderColor: grey11,
      },
      fullWidth: {
        background: white,
        text: grey13,
        borderColor: grey4,
        disabledBackground: grey11,
        disabledText: grey12,
        disabledBorderColor: grey4,
      },
    },
    card: {
      background: '#F4F4F4',
      border: '#EAEAEA',
      title: {
        size: '16px',
        lineHeight: '21px',
      },
      'text-large': {
        size: '16px',
        lineHeight: '24px',
      },
      'text-xl': {
        size: '18px',
        lineHeight: '19px',
      },
      'text-xxl': {
        size: '20px',
        lineHeight: '21px',
      },
      text: {
        size: '14px',
        lineHeight: '15px',
      },
      'text-small': {
        size: '10px',
        lineHeight: '12px',
      },
      'text-tc': {
        size: '12px',
        lineHeight: '18px',
      },
      'text-medium': {
        size: '14px',
        lineHeight: '20px',
      },
      textPressable: {
        size: '16px',
        lineHeight: '19px',
      },
    },
    checkBox: {
      primary: {
        backgroundColor: white,
        tick: primary,
        borderColor: grey4,
        checked: {
          backgroundColor: white,
          borderColor: primary,
        },
      },
      secondary: {
        tick: primary,
        backgroundColor: primary,
        borderColor: white,
        checked: {
          backgroundColor: white,
          borderColor: grey4,
        },
      },
    },
    shadow: {
      default: {
        color: '#000000',
        opacity: 0.2,
        radius: '7px',
        elevation: 2,
      },
    },
    table: {
      border: grey7,
    },
    header: {
      background: white,
      border: '#f2f2f2',
      color: black,
    },
    gocardless: {
      text: {
        header: black2,
        banner: grey1,
        border: '#f2f2f2',
        cardtext: grey8,
      },
      page: {
        background: grey5,
      },
    },
    textInput: {
      color: black,
      backgroundColor: white,
      borderColor: grey4,
      clearButtonColor: grey1,
      labelTextColor: voucherText,
      assistiveTextColor: grey6,
      placeholderTextColor: 'rgba(17, 17, 17, 0.8)',
      disabled: {
        color: voucherText,
        backgroundColor: grey5,
      },
      error: {
        backgroundColor: errorPale,
        color: error,
      },
      success: {
        backgroundColor: successPale,
        color: success,
      },
    },
    voucher: {
      color: {
        grey: grey6,
        status: voucherStatus,
        text: voucherText,
        active: bpGreen,
        activeuntil: black,
        expired: error,
      },
    },
    cancelSubscription: {
      color: {
        primary: red,
      },
    },
  },
};

export default theme;
