import { ThemeProvider } from '@bp/ui-components/mobile';
import defaultsDeep from 'lodash.defaultsdeep';
import React from 'react';

import theme from './Theme.default';

interface ThemeProps {
  children: React.ReactNode;
  customTheme?: any;
}

const Theme = ({ customTheme, children }: ThemeProps) => {
  const providedTheme = defaultsDeep(customTheme, theme);
  // @ts-ignore
  return <ThemeProvider customTheme={providedTheme}>{children}</ThemeProvider>;
};

export default Theme;
