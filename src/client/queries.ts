import { gql } from 'graphql-tag';

export const GET_GOCARDLESS_URL = gql`
  query getPaymentUrl($userId: String) {
    getPaymentUrl(userId: $userId) {
      url
    }
  }
`;

export const GET_GOCARDLESS_COMPLETE = gql`
  query completePayment($userId: String, $flowId: String) {
    completePayment(userId: $userId, flowId: $flowId) {
      success
    }
  }
`;

export const GET_GOCARDLESS_VOUCHERS_STATUS = gql`
  query getVoucherStatus($userId: String, $voucherId: String) {
    getVoucherStatus(userId: $userId, voucherId: $voucherId) {
      status
      voucherStatus
      voucherDesc
      userPays
      duration
    }
  }
`;

export const REQUEST_RFID = gql`
  query requestRFID(
    $userId: String
    $cardPreference: String
    $addressDetails: PaymentAddressDetails
  ) {
    requestRFID(
      userId: $userId
      cardPreference: $cardPreference
      addressDetails: $addressDetails
    ) {
      status
      data {
        event_details
        salesforce_ID
      }
    }
  }
`;

export const REPLACE_RFID = gql`
  mutation replaceRFID($userId: String!) {
    replaceRFID(userId: $userId) {
      status
      data {
        eventDetails
        eventTime
        salesforceID
      }
    }
  }
`;

export const GET_SUBSCRIPTION_PREFERENCE = gql`
  query getSubscriptionPreference($userId: String!) {
    getSubscriptionPreference(userId: $userId) {
      status
      payload {
        data {
          cardPreference
          addressLine
          addressCity
          addressCountry
          addressPostcode
          nextBillingDate
          toBeCancelled
        }
        error
        code
        eventDetails
        eventTime
        salesforceId
      }
    }
  }
`;

export const UPDATE_SUBSCRIPTION_PREFERENCE = gql`
  mutation updateSubscriptionPreference(
    $userId: String!
    $cardPreference: String!
    $addressDetails: AddressDetails
  ) {
    updateSubscriptionPreference(
      userId: $userId
      cardPreference: $cardPreference
      addressDetails: $addressDetails
    ) {
      status
      payload {
        code
        error
        eventDetails
        eventTime
        message
        salesforceId
      }
    }
  }
`;

export const CANCEL_SUBSCRIPTION = gql`
  mutation cancelSubscription($userId: String!) {
    cancelSubscription(userId: $userId) {
      status
      payload {
        code
        error
        eventDetails
        eventTime
        message
        salesforceId
      }
    }
  }
`;

export const CANCEL_WALLET_SUBSCRIPTION = gql`
  mutation cancelWalletSubscription($userId: String!) {
    cancelWalletSubscription(userId: $userId) {
      status
      membershipStatus
      statusReason
      cancelledOn
      endOfCurrentBillingCycle
      message
      error
    }
  }
`;

export const GET_USER_INFO = gql`
  query userInfo($userId: String!) {
    userInfo(userId: $userId) {
      balance
      status
      type
      partnerType
      country
      gocardless {
        mandateStatus
        mandateId
      }
      tagIds {
        tagId
        tagStatus
        tagNotes
      }
      membership {
        userType
        membershipStatus
        membershipRequestCancelDate
        membershipStartDate
        membershipEndDate
        membershipBillingCycleDate
      }
    }
  }
`;

export const VALIDATE_OFFER_CODE = gql`
  query validateOffer($offerCode: String!) {
    validateOffer(offerCode: $offerCode) {
      offer {
        offerCode
        offerType
        offerName
        offerPublicDescription
        subsPlanId
        status
        creditAmount
        currency
        creditBalance
        offerCountry
        subsDiscount
        subsDuration
      }
      isValid
      reason
      error
    }
  }
`;

export const CREATE_WALLET_SUBSCRIPTION = gql`
  mutation createWalletSubscription(
    $userId: String
    $paymentMethodId: String!
    $offerCode: String
    $country: String
    $threeDS: ThreeDS
  ) {
    createWalletSubscription(
      userId: $userId
      paymentMethodId: $paymentMethodId
      offerCode: $offerCode
      country: $country
      threeDS: $threeDS
    ) {
      status
      error
    }
  }
`;

export const RETRY_WALLET_SUBS_PAYMENT = gql`
  mutation retryWalletSubsPayment(
    $userId: String
    $membershipId: String!
    $country: String
  ) {
    retryWalletSubsPayment(
      userId: $userId
      membershipId: $membershipId
      country: $country
    ) {
      nextBillingDate
      cancelledOn
      accountId
      useCase
      status
      statusReason
      error
    }
  }
`;

export const GET_SUBS_PLAN = gql`
  query getSubsPlan($userId: String!, $externalPlanIds: [String]) {
    getSubsPlan(userId: $userId, externalPlanIds: $externalPlanIds) {
      plans {
        planid
        planName
        planDuration
        externalPlanId
        billingAmount {
          amount
          currency
        }
      }
    }
  }
`;

export const GET_WALLET_SUBSCRIPTION = gql`
  query getWalletSubscription($userId: String) {
    getWalletSubscription(userId: $userId) {
      id
      customerId
      paymentMethodId
      merchantId
      tenantId
      useCase
      billingItems {
        billingItemId
        planId
        name
        amount
        currency
      }
      createdAt
      status
      statusReason
      nextBillingDate
      cancelledOn
      accountId
      agreementId
      deactivationDate
      discountExpiresAt
    }
  }
`;

export const APPLY_OFFER = gql`
  mutation applyOffer($offerCode: String!, $userId: String) {
    applyOffer(offerCode: $offerCode, userId: $userId) {
      error
    }
  }
`;
