import React from 'react';

import { SubscriptionProvider as ISubscriptionProvider } from './common/interfaces';
import { AddressContextProvider } from './providers/AddressProvider';
import GraphQLProvider from './providers/GraphQLProvider';
import { HostNavigationProvider } from './providers/HostNavigationProvider';
import { SettingsContextProvider } from './providers/Settings';
import { SubscriptionContextProvider } from './providers/SubscriptionProvider';
import SubsPreferencesProvider from './providers/SubsPreferencesProvider';
import UserInfoProvider from './providers/UserInfoProvider';
import WalletSubscriptionDataProvider from './providers/WalletSubscriptionDataProvider';
import Theme from './themes/Theme';

const SubscriptionProvider = (props: ISubscriptionProvider) => {
  const { children } = props;
  return (
    <SettingsContextProvider {...props}>
      <GraphQLProvider>
        <HostNavigationProvider navigation={props.navigation}>
          <UserInfoProvider>
            <SubscriptionContextProvider>
              <SubsPreferencesProvider>
                <WalletSubscriptionDataProvider>
                  <AddressContextProvider>
                    <Theme>{children}</Theme>
                  </AddressContextProvider>
                </WalletSubscriptionDataProvider>
              </SubsPreferencesProvider>
            </SubscriptionContextProvider>
          </UserInfoProvider>
        </HostNavigationProvider>
      </GraphQLProvider>
    </SettingsContextProvider>
  );
};

export default SubscriptionProvider;
