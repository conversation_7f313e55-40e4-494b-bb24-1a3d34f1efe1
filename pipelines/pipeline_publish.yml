pool:
  vmImage: 'ubuntu-22.04'

resources:
  repositories:
    - repository: self
      trigger:
        batch: true
        branches:
          include:
            - main

stages:
  - stage: Lint
    displayName: Lint
    dependsOn: []
    jobs:
      - template: jobs/node_lint.yml

  - stage: Test
    displayName: Test
    dependsOn: []
    jobs:
      - template: jobs/node_test.yml

  - stage: Publish
    displayName: Publish
    condition: always()
    jobs:
      - template: jobs/node_publish.yml
