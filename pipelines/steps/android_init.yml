steps:
  - script: |
      $ANDROID_HOME/platform-tools/adb start-server
      $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --install emulator
      $ANDROID_HOME/cmdline-tools/latest/bin/sdkmanager --install "${{ variables.systemImage }}"
      echo no | $ANDROID_HOME/cmdline-tools/latest/bin/avdmanager create avd --force --name "${{ variables.avdName }}" --package "${{ variables.systemImage }}"
      $ANDROID_HOME/emulator/emulator -verbose -no-audio -no-boot-anim -no-window @${{ variables.avdName }} &
      booted=0; while [ "$booted" != "1" ]; do echo "Waiting for emulator..."; booted=`$ANDROID_HOME/platform-tools/adb shell getprop dev.bootcomplete`; sleep 3; done;
      $ANDROID_HOME/platform-tools/adb shell settings put system show_touches 1
      $ANDROID_HOME/platform-tools/adb shell settings put system pointer_location 1
      killall qemu-system-x86_64-headless
    displayName: 'android: init'
