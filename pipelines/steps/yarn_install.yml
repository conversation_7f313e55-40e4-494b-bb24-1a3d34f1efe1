parameters:
  - name: path
    type: string
    default: '.'

steps:
  - task: <PERSON><PERSON>@2
    displayName: 'yarn cache: ${{ parameters.path }}'
    inputs:
      key: 'yarn | "$(Agent.OS)" | ${{parameters.path}}/yarn.lock'
      path: ${{parameters.path}}/node_modules

  - script: |
      yarn --frozen-lockfile --ignore-engines
    displayName: 'yarn install: ${{ parameters.path }}'
    workingDirectory: ${{ parameters.path }}
