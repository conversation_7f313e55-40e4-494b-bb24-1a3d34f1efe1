pool:
  vmImage: 'ubuntu-22.04'

variables:
  packageName: 'subscription-mfe'
  projectKey: 'com.bp.bp-digital:bp-mfe-feature-subscription'

resources:
  repositories:
    - repository: self
      trigger:
        batch: true
        branches:
          include:
            - main

    - repository: bp-mfe-common-pipelines
      type: git
      name: bp-mfe-common-pipelines

    - repository: AdsTemplatesSecurity
      type: git
      name: DevOps-SRE/ads-ado-templates-security
      ref: refs/tags/2.x

    - repository: bp-via-checkmarx
      type: git
      name: Vulnerability_Identification_and_Awareness/bp-via-checkmarx

schedules:
  - cron: '0 8 * * <PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>hu,Fri'
    displayName: Daily 08:00 (UTC) build
    branches:
      include:
        - main

stages:
  - stage: Lint
    displayName: Lint
    dependsOn: []
    jobs:
      - template: jobs/node_lint.yml

  - stage: Test
    displayName: Test
    dependsOn: []
    jobs:
      - template: jobs/node_test.yml

  - stage: Build
    displayName: Build
    dependsOn: [Lint, Test]
    jobs:
      - template: jobs/node_build.yml

  - stage: SecurityScan
    displayName: DSES-Security-Scan
    jobs:
      - job: SecurityScanJob
        displayName: SAST and SCA Scan

        pool:
          vmImage: 'ubuntu-latest'

        continueOnError: true

        steps:
          - template: /templates-common/cx_ado_template/cx-ado-template.yml@bp-via-checkmarx
            parameters:
              releaseBranches:
                - main
              repoNames:
                - $(Build.Repository.Name)
              repoIDS:
                - $(Build.Repository.ID)
              repoBranchNames:
                - feature-subscription
              snowApplicationPackageNumber: SPKG0022546
              snowApplicationPackageName: EV-Platform
              projectInformalName: OTG-EMSP
              itServiceName: bp pulse digital channel and services
              itServiceNumber: ITSVC0010677
              scaServiceConnection: checkmarx-sca-SPKG0022546
              sastServiceConnection: checkmarx-sast-SPKG0022546
              isMicroService: no
              microserviceOrMonolithicAppName: feature-subscription
              vulnerabilityThreshold: true
              thresholdHigh: 0
              scaThreshold: true
              scaHigh: 0

  - stage: SonarQube
    displayName: SonarQube ${{ variables.packageName }}
    dependsOn: [Lint, Test, Build]
    jobs:
      - template: jobs/sonarqube_run.yml
        parameters:
          projectKey: ${{ variables.projectKey }}
