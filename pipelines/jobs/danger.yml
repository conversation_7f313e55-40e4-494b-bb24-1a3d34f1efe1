jobs:
  - job: Build
    steps:
      - checkout: self
        clean: 'true'
        fetchDepth: '0'
      - task: NodeTool@0
        inputs:
          versionSpec: '18.18'
        displayName: 'Install Node.js'
      - bash: |
          pwd
          ls -la
        displayName: 'Check directory'
      - task: npmAuthenticate@0
        inputs:
          workingFile: .npmrc
      - script: |
          yarn install
        displayName: 'yarn install'
      - bash: |
          yarn ci:danger
        condition: and(succeeded(), eq(variables.isMaster, 'false'))
        displayName: 'dangerjs checks'
        env:
          AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
          AZURE_REPO_ID: $(Build.Repository.ID)
          AZURE_PROJ: $(System.TeamProject)
          AZURE_API_URL: $(System.CollectionUri)
          AZURE_PR_ID: $(System.PullRequest.PullRequestId)
          DANGER_POLICY_NAME: 'Danger-Checks'
          AZURE_DANGER_MAIN_BRANCH: 'remotes/origin/main'
