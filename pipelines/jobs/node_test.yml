jobs:
  - job: Test
    displayName: Test
    steps:
      - template: ../steps/node_install.yml
      - template: ../steps/yarn_authenticate.yml
      - template: ../steps/yarn_install.yml

      - script: |
          yarn test:ci -u
        displayName: 'test: unit'

      - task: PublishTestResults@2
        displayName: 'results: publish'
        condition: succeededOrFailed()
        inputs:
          testRunner: JUnit
          testResultsFiles: 'test-results/jest/junit.xml'
          failTaskOnFailedTests: true

      - task: PublishCodeCoverageResults@1
        displayName: 'coverage: publish'
        inputs:
          codeCoverageTool: Cobertura
          summaryFileLocation: $(System.DefaultWorkingDirectory)/coverage/cobertura-coverage.xml

      - publish: $(System.DefaultWorkingDirectory)/coverage
        artifact: TestCoverage

      - publish: $(System.DefaultWorkingDirectory)/test-results
        artifact: TestResults
