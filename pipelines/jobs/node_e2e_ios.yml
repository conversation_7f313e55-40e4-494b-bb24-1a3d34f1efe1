parameters:
  - name: applicationName
    type: string
    default: VirtualRoutex

jobs:
  - job: E2E_iOS
    displayName: 'E2E: iOS'
    timeoutInMinutes: 90
    pool:
      vmImage: macos-latest
    variables:
      - group: Detox-Envs

    steps:
      # iOS
      - template: ../steps/allure_install.yml
      - template: ../steps/applesimutils_install.yml

      # @bp/map-mfe
      - template: ../steps/yarn_install.yml
      - script: |
          yarn build
        displayName: 'build'

      # exampleapp
      - template: ../steps/yarn_install.yml
        parameters:
          path: ./exampleapp

      - template: ../steps/pod_install.yml
        parameters:
          path: ./exampleapp/ios

      - template: ../steps/react-native_env.yml
        parameters:
          path: ./exampleapp

      # run
      - script: |
          yarn detox:build:ios:ci
        displayName: Build detox
        workingDirectory: ./exampleapp

      - script: |
          yarn test:ios:ci
        displayName: Run detox (iOS)
        workingDirectory: ./exampleapp

      # reports
      - template: ../steps/allure_generate.yml
        parameters:
          path: ./exampleapp

      - task: PublishBuildArtifacts@1
        displayName: Get Test Artifacts
        condition: always()
        inputs:
          pathToPublish: ./exampleapp/artifacts
          artifactName: iOS Artifacts
