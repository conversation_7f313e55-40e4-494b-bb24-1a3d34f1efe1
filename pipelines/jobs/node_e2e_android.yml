parameters:
  - name: applicationName
    type: string
    default: VirtualRoutex

jobs:
  - job: E2E_Android
    displayName: 'E2E: Android'
    timeoutInMinutes: 90
    pool:
      vmImage: macos-latest
    variables:
      - group: Detox-Envs
      - name: systemImage
        value: system-images;android-28;google_apis;x86_64
      - name: avdName
        value: E2E_TEST_EMULATOR

    steps:
      # @bp/map-mfe
      - template: ../steps/yarn_install.yml
      - script: |
          yarn build
        displayName: 'build'

      # exampleapp
      - template: ../steps/yarn_install.yml
        parameters:
          path: ./exampleapp

      # Android
      - template: ../steps/android_init.yml

      - template: ../steps/react-native_env.yml
        parameters:
          path: ./exampleapp

      # run
      - template: ../steps/metro_start.yml
        parameters:
          path: ./exampleapp

      - script: |
          yarn detox:build:android:ci
        displayName: Build detox
        workingDirectory: ./exampleapp

      - script: |
          yarn test:android:ci
        displayName: Run detox (Android)
        workingDirectory: ./exampleapp

      - task: PublishBuildArtifacts@1
        displayName: 'logs: publish'
        condition: always()
        inputs:
          pathToPublish: 'exampleapp/metro-android.log'

      - task: PublishBuildArtifacts@1
        displayName: 'artifacts: publish'
        condition: always()
        inputs:
          pathToPublish: 'exampleapp/artifacts'
