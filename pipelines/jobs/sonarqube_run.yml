# @see https://dev.azure.com/bp-digital/DevOps-SRE/_git/ads-ado-templates-security?path=/templates-common/code_quality/sonar/README.md&version=GBmaster&anchor=scan-sonar.yml-(step)&_a=preview

parameters:
  - name: projectKey

jobs:
  - job: SonarQube
    displayName: SonarQube
    pool: 'GenericPoolLinux-SS'
    steps:
      - task: DownloadPipelineArtifact@2
        displayName: 'get artifact: TestCoverage'
        inputs:
          source: current
          artifact: TestCoverage
          path: coverage/

      - task: DownloadPipelineArtifact@2
        displayName: 'get artifact: TestResults'
        inputs:
          source: current
          artifact: TestResults
          path: test-results/

      - template: templates-common/code_quality/sonar/steps/scan-sonar.yml@AdsTemplatesSecurity
        parameters:
          sonarService: 'sonarqube_dcm_frameworks'
          projectKey: ${{ parameters.projectKey  }}
          sonarBuildBreaker: true
