jobs:
  - job: Lint
    displayName: Lint
    steps:
      - template: ../steps/node_install.yml
      - template: ../steps/yarn_authenticate.yml
      - template: ../steps/yarn_install.yml

      - script: |
          yarn lint:prettier
        displayName: 'lint: prettier'
        condition: always()

      - script: |
          yarn lint:scripts
        displayName: 'lint: scripts'
        condition: always()

      - script: |
          yarn lint:styles
        displayName: 'lint: styles'
        condition: always()

      - script: |
          yarn lint:vectors
          git diff --exit-code "*.svg"
        displayName: 'lint: vectors'
        condition: always()
