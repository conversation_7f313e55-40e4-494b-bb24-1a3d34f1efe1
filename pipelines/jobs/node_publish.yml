jobs:
  - job: Publish
    displayName: Publish
    steps:
      - template: ../steps/node_install.yml
      - template: ../steps/yarn_authenticate.yml
      - template: ../steps/yarn_install.yml

      - script: |
          yarn build
        displayName: 'build'

      - task: npmAuthenticate@0
        inputs:
          workingFile: .npmrc

      - script: >
          npx -p semantic-release
          -p @semantic-release/changelog
          -p @semantic-release/git
          -p semantic-release-ado
          semantic-release
        env:
          GH_TOKEN: $(System.AccessToken)
        displayName: 'Semantic release'
