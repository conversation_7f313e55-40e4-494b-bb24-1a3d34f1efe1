const transformIgnorePackages = [
  '@bp/*',
  '@react-native/*',
  '@react-navigation/*',
  'react-native-*',
  'react-native',
  'react-navigation',
];

const transformIgnorePatterns = `node_modules/(?!${transformIgnorePackages.join(
  '|',
)})`;

// all tests which are date dependent will use the GMT time zone
process.env.TZ = 'GMT';

module.exports = {
  preset: 'react-native',
  testEnvironment: 'node',
  coveragePathIgnorePatterns: ['src/providers/GraphQLProvider.tsx'],
  transformIgnorePatterns: [transformIgnorePatterns],
  reporters: [
    'default',
    ['jest-junit', { outputDirectory: 'test-results/jest' }],
  ],
  moduleNameMapper: {
    '\\.svg': '<rootDir>/.jest/modules/svg.js',
  },
  coverageReporters: [
    'json',
    'lcov',
    'text',
    'clover',
    'text-summary',
    'cobertura',
  ],
  collectCoverageFrom: ['<rootDir>/src/**/*.{ts,tsx}'],
  roots: ['./src'],
  testTimeout: 20000,
  setupFiles: ['./.jest/setup.js'],
};
