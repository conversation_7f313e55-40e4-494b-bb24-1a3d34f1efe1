version: 2
updates:
  - package-ecosystem: 'npm'
    directory: '/'
    target-branch: 'main'
    schedule:
      interval: 'weekly'
    commit-message:
      prefix: 'chore'
    allow:
      - dependency-name: '@bp/pulse-mobile-sdk*'
        dependency-type: 'direct'
registries:
  DST-Digital_Cross-Business_Platforms:
    type: npm-registry
    url: 'pkgs.dev.azure.com/bp-digital/DST-Digital_Cross-Business_Platforms/_packaging/apolloxi-component-library/npm/registry/'
    token: 'bp-digital:${{SYSTEM_ACCESSTOKEN}}'
  DST-Digital_AMU:
    type: npm-registry
    url: 'pkgs.dev.azure.com/bp-digital/DST-Digital_AMU/_packaging/pulse-packages/npm/registry/'
    token: 'bp-digital:${{SYSTEM_ACCESSTOKEN}}'
  DCM_Frameworks:
    type: npm-registry
    url: 'pkgs.dev.azure.com/bp-digital/DCM_Frameworks/_packaging/bp-mfe-frameworks/npm/registry/'
    token: 'bp-digital:${{SYSTEM_ACCESSTOKEN}}'
