import 'styled-components/native';

declare module 'styled-components/native' {
  export interface DefaultTheme extends BPCoreTheme {
    // Add your own theme types here
    subscriptionMfe: {
      color: {
        primary: string;
        black: string;
        black2: string;
        white: string;
        grey: IThemeProps;
        state: IThemeProps;
      };
      text: {
        color: IThemeProps;
      };
      icons: {
        default: IThemeProps;
        enabled: IThemeProps;
        disabled: IThemeProps;
      };
      bottomNavigation: {
        background: string;
        enabled: IThemeProps;
        disabled: IThemeProps;
      };
      buttons: {
        primary: IThemeProps;
        secondary: IThemeProps;
        ghost: IThemeProps;
        text: IThemeProps;
        small: IThemeProps;
        cancel: IThemeProps;
        secondaryGhost: IThemeProps;
        fullWidth: IThemeProps;
      };
      card: {
        background: string;
        border: string;
        title: IThemeProps;
        'text-large': IThemeProps;
        'text-xl': IThemeProps;
        'text-xxl': IThemeProps;
        text: IThemeProps;
        'text-small': IThemeProps;
        'text-tc': IThemeProps;
        'text-medium': IThemeProps;
        textPressable: IThemeProps;
      };
      checkBox: {
        primary: {
          backgroundColor: string;
          tick: string;
          borderColor: string;
          checked: IThemeProps;
        };
        secondary: {
          tick: string;
          backgroundColor: string;
          borderColor: string;
          checked: IThemeProps;
        };
      };
      shadow: {
        default: IShadowProps;
      };
      table: {
        border: string;
      };
      header: {
        background: string;
        border: string;
        color: string;
      };
      gocardless: {
        text: IThemeProps;
        page: IThemeProps;
      };
      textInput: {
        color: string;
        backgroundColor: string;
        borderColor: string;
        clearButtonColor: string;
        labelTextColor: string;
        assistiveTextColor: string;
        placeholderTextColor: string;
        disabled: IThemeProps;
        error: IThemeProps;
        success: IThemeProps;
      };
      voucher: {
        color: IThemeProps;
      };
      cancelSubscription: {
        color: IThemeProps;
      };
    };
  }
}
