module.exports = {
  extends: ['plugin:@bp/react-native'],
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
      },
    },
    'import/ignore': ['react-native'],
  },
  overrides: [
    {
      files: ['*.ts', '*.tsx'],
      parser: '@typescript-eslint/parser',
      parserOptions: {
        project: `${__dirname}/tsconfig.json`,
      },
    },
  ],
  rules: {
    'sonarjs/cognitive-complexity': ['error', 17],
  },
};
