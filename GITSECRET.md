# ENV variables using GIT SECRETS

## Prerequisites

Create a GPG key using your BP email address. Find details on how to do this in the links below

Install GIT-SECRET on a Mac

`brew install gnupg`
`brew install git-secret`

## Create GPG key

`gpg --gen-key`

Export public key

`gpg --armor --export <EMAIL> > public-key.gpg`

## Using GIT SECRET

COMMANDS

`git secret add [filename]`

`git secret hide`

`git secret reveal`

## Adding a user to GIT SECRET

You need the users public gpg key. Then run.

`gpg --import KEY_NAME.txt`

`git <NAME_EMAIL>`

`git secret reveal; git secret hide -d`

Now the new user can use `git secret reveal`

## Removing a user

`git <NAME_EMAIL>`

`git secret reveal; git secret hide -d`

## References

[Install GPG](https://www.devdungeon.com/content/gpg-tutorial#install_gpg)

[GIT SECRETS](https://git-secret.io/)
