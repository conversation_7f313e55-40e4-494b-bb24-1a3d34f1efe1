<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="248" height="266" viewBox="0 0 248 266">
  <defs>
    <linearGradient id="c" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-opacity="0"/>
      <stop offset="100%" stop-color="#FFF"/>
    </linearGradient>
    <linearGradient id="e" x1=".258%" x2="100%" y1="50%" y2="50%">
      <stop offset="0%" stop-color="#3023AE"/>
      <stop offset="47.525%" stop-color="#53A0FD"/>
      <stop offset="100%" stop-color="#B4EC51"/>
    </linearGradient>
    <linearGradient id="f" x1="-.111%" x2="100%" y1="50.055%" y2="50.055%">
      <stop offset="0%" stop-color="#8585CE"/>
      <stop offset="100%" stop-color="#3333AD"/>
    </linearGradient>
    <linearGradient id="g" x1="37.711%" x2="62.253%" y1="-1.99%" y2="101.995%">
      <stop offset="0%" stop-color="#8585CE"/>
      <stop offset="100%" stop-color="#3333AD"/>
    </linearGradient>
    <filter id="b" width="130%" height="130%" x="-15%" y="-15%" filterUnits="objectBoundingBox">
      <feMorphology in="SourceAlpha" operator="dilate" radius="12" result="shadowSpreadOuter1"/>
      <feOffset in="shadowSpreadOuter1" result="shadowOffsetOuter1"/>
      <feGaussianBlur in="shadowOffsetOuter1" result="shadowBlurOuter1" stdDeviation="6"/>
      <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"/>
      <feColorMatrix in="shadowBlurOuter1" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
    </filter>
    <circle id="a" cx="100" cy="100" r="100"/>
  </defs>
  <g fill="none" fill-rule="evenodd" transform="translate(24)">
    <g transform="translate(0 42)">
      <mask id="d" fill="#fff">
        <use xlink:href="#a"/>
      </mask>
      <g opacity=".5">
        <use xlink:href="#a" fill="#000" filter="url(#b)"/>
        <circle cx="100" cy="100" r="106" fill="#D8D8D8" stroke="#FFF" stroke-width="12"/>
      </g>
      <path fill="url(#c)" d="M200 0v200H0z" mask="url(#d)" opacity=".4" style="mix-blend-mode:color-burn"/>
    </g>
    <rect width="200" height="5" y="186" fill="#000096" rx="2.5"/>
    <rect width="200" height="5" y="186" fill="url(#e)" rx="2.5"/>
    <circle cx="41.447" cy="41.447" r="39.947" fill="#FFF" stroke="#000096" stroke-width="3" transform="translate(58.553 100.526)"/>
    <rect width="16" height="16" x="55.928" y="2.928" fill="url(#f)" fill-rule="nonzero" rx="2" transform="rotate(30 63.928 10.928)"/>
    <path fill="#9C0" fill-rule="nonzero" d="m161.599 10.829.621 4.117c.043.325.26.62.55.767l3.67 1.8c.737.37.751 1.447.014 1.83l-3.641 1.875a1.02 1.02 0 0 0-.535.767l-.55 4.132c-.115.841-1.112 1.18-1.69.576l-2.876-2.952a.99.99 0 0 0-.882-.28l-4.017.753c-.81.147-1.445-.709-1.07-1.461l1.865-3.705a1.09 1.09 0 0 0 0-.944l-1.937-3.66c-.39-.738.217-1.623 1.04-1.49l4.033.664a1 1 0 0 0 .881-.295l2.818-3.011c.564-.605 1.576-.28 1.706.546z"/>
    <rect width="20" height="20" x="13" y="42" fill="#FBDB65" fill-rule="nonzero" rx="10"/>
    <path stroke="url(#g)" stroke-dasharray="0" stroke-width="4" d="M179.03 89.03c5.523 0 10-4.477 10-10s-4.477-10-10-10-10 4.477-10 10 4.477 10 10 10z" transform="rotate(-76.72 179.03 79.03)"/>
    <path fill="none" d="M63.986 105.986h69.029v69.029h-69.03z"/>
    <path stroke="#000096" stroke-width="4.314" d="M81.674 134.029h36.24m-28.475-12.943v5.177m20.709-5.177v5.177m-10.354-5.177v5.177m15.531 0H84.262a2.59 2.59 0 0 0-2.588 2.588v28.475a2.59 2.59 0 0 0 2.588 2.588h31.063a2.59 2.59 0 0 0 2.589-2.588V128.85a2.59 2.59 0 0 0-2.589-2.588z"/>
    <g fill="#000096">
      <path d="m96.05 140.222 10.607 10.607-2.829 2.829-10.606-10.607z"/>
      <path d="M106.656 143.05 96.05 153.657l-2.829-2.829 10.607-10.606z"/>
    </g>
  </g>
</svg>
