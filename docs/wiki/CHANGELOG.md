##[2.195.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.195.3&targetVersion=GTv2.195.4&_a=commits)  (2025-08-21)


### Bug Fixes

*  **membership:** fix extra tariff layout issue in max font size eaa ([2a22f53](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/2a22f53aaf67f1fe03525048a495196a97079e48))    #8794313

##[2.195.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.195.2&targetVersion=GTv2.195.3&_a=commits)  (2025-08-20)


### Bug Fixes

*  **myconfortmembership:** handled no signal screen on back navigation ([7ab6f6d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7ab6f6dafd2eb55637dc7b0e06ee466dc64958d0))    #8462406

##[2.195.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.195.1&targetVersion=GTv2.195.2&_a=commits)  (2025-08-15)


### Bug Fixes

*  **components:** refactor defaultProps usage to fix crash ([463e813](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/463e8134c057cc7de030554161f81035eeb73e0d))    #8835358

##[2.195.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.195.0&targetVersion=GTv2.195.1&_a=commits)  (2025-08-14)


### Bug Fixes

*  **subscription:** my subscription page payment card overlaping issue ([013af9c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/013af9cc822b4ebe5d4724db2e0fa0c7a323c3fd))    #8794313

#[2.195.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.194.1&targetVersion=GTv2.195.0&_a=commits)  (2025-08-11)


### Features

*  subscription Summary screen updates to match offers discount and new "first month free" policy ([7e7fd15](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7e7fd15be22643110714c0196e95bc0bdfa102bd))    #8694894

##[2.194.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.194.0&targetVersion=GTv2.194.1&_a=commits)  (2025-08-08)


### Bug Fixes

*  **3ds in create subs:** pass 3ds reference through create subscription ([fe3425f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/fe3425f326b07fdd6e68a1bee61f61d36d4cc5e4))    #8730605

#[2.194.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.193.0&targetVersion=GTv2.194.0&_a=commits)  (2025-08-05)


### Features

*  8587960-subs-header-issues ([c77c792](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c77c792bab40f62383d9a5f0c028013356ffd9d3))    #8587960

#[2.193.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.192.0&targetVersion=GTv2.193.0&_a=commits)  (2025-08-05)


### Features

*  8587960-subs-header-issue ([e512e40](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e512e40d268dd7569ca4b727368ce611d19934c0))    #8587960

#[2.192.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.191.0&targetVersion=GTv2.192.0&_a=commits)  (2025-08-01)


### Features

*  8587960-subscription-header ([c6be3e2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c6be3e2f80bcccd506916674a65bdcde946b5776))    #8565660

#[2.191.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.190.0&targetVersion=GTv2.191.0&_a=commits)  (2025-07-31)


### Features

*  8565660-header-issue-fix ([05643c7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/05643c7ac050c7fa39fd3435dcb122b08638a935))    #8565660
    #8587960

#[2.190.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.189.0&targetVersion=GTv2.190.0&_a=commits)  (2025-07-28)


### Features

*  8587991-translation-version-update ([ff43e0a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ff43e0ae06522a42f23fe71dc70e8b33d98a91ba))    #8587991

#[2.189.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.188.0&targetVersion=GTv2.189.0&_a=commits)  (2025-07-23)


### Features

*  8587991-version-update ([f03a6cb](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f03a6cb1fd7bd69fe981902b72b4c728f78f7a94))    #8587991

#[2.188.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.187.0&targetVersion=GTv2.188.0&_a=commits)  (2025-07-23)


### Features

*  8587991-payment-card-issue-fix ([7734ec1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7734ec1e7366bde65c4f7c956d9d5f328fd922a0))    #8587991

#[2.187.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.186.0&targetVersion=GTv2.187.0&_a=commits)  (2025-07-21)


### Features

*  CrowdIn translation updates ([0651d70](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0651d70adaec6e51e4c93f17c8f121ba5a7e1290))    #8521568

#[2.186.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.185.0&targetVersion=GTv2.186.0&_a=commits)  (2025-07-14)


### Features

*  CrowdIn translation updates ([7620220](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7620220b0efe297591eeac6a434c5751da6e8bf5))    #8625169

#[2.185.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.184.0&targetVersion=GTv2.185.0&_a=commits)  (2025-07-11)


### Features

*  8625169-all-interactive-elements-accessible-screen-reader ([0d38bbd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0d38bbd12030a75c4b4ae17a1c18cc5552746555))    #8625169

#[2.184.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.183.1&targetVersion=GTv2.184.0&_a=commits)  (2025-07-11)


### Features

*  CrowdIn translation updates ([7c1cbe4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7c1cbe49c3a9c218990ec5accffe785199da3a4c))    #8670150

##[2.183.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.183.0&targetVersion=GTv2.183.1&_a=commits)  (2025-07-11)


### Bug Fixes

*  copy fix ([5696c9f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5696c9f9e913beea7b461168a4d0b014ffbc18cc))    #8670150

#[2.183.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.182.0&targetVersion=GTv2.183.0&_a=commits)  (2025-07-10)


### Features

*  crowdIn translation updates ([d8ed78e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d8ed78e5fca2c9fca3b7c23a87df950ec800f338))    #8670306

#[2.182.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.181.2&targetVersion=GTv2.182.0&_a=commits)  (2025-07-10)


### Features

*  copy changes for Subscribe and save screen ([0719f6d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0719f6dfed994b07449ec833e06dfed29606d41e))    #8670150
    #8670306

##[2.181.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.181.1&targetVersion=GTv2.181.2&_a=commits)  (2025-07-07)


### Bug Fixes

*  **confirncancelcomfortmembership:** disable CTA button when no internet conn... ([8ffc50e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8ffc50eb3506b9d1d6e6ff7ba77e6c027c7925d5))    #8462406

##[2.181.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.181.0&targetVersion=GTv2.181.1&_a=commits)  (2025-07-03)


### Bug Fixes

*  **mycomfortmembershipactivation:** disabled button when no internet connection ([b982730](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b982730c54703484ffa983ccf016761a5906b378))    #8462406

#[2.181.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.180.0&targetVersion=GTv2.181.0&_a=commits)  (2025-07-01)


### Features

*  Added scroll on subscription success screen ([8ebf848](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8ebf848b9d71a11aab85749218d1b0b18a16b4df))    #8587954

#[2.180.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.179.0&targetVersion=GTv2.180.0&_a=commits)  (2025-07-01)


### Features

*  success screen scroll issue resolved when large font scale ([1f1b3d6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1f1b3d6b3301a01c38a7b82b13b2d1c1394373f1))    #8587954

#[2.179.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.178.2&targetVersion=GTv2.179.0&_a=commits)  (2025-06-30)


### Features

*  added a check for disabling a button if the card is expired ([204dcfa](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/204dcfa7bed1c89b92fad8839a677585ab7542d0))    #8460905

##[2.178.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.178.1&targetVersion=GTv2.178.2&_a=commits)  (2025-06-27)


### Bug Fixes

*  **helper:** resolved issue with missing Get Free Aral Charge Card Button on... ([1a48f34](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1a48f34859e1be17d7e6b65b2567692c98f14afa))    #8253336

##[2.178.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.178.0&targetVersion=GTv2.178.1&_a=commits)  (2025-06-27)


### Bug Fixes

*  **no signal subs wording:** no signal subs wording ([de5c4de](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/de5c4decbe04ed391cd58085cd7135b044011d58))    #8401911

#[2.178.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.177.1&targetVersion=GTv2.178.0&_a=commits)  (2025-06-25)


### Features

*  CrowdIn translation updates ([fd255e2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/fd255e21749b40cff30f900e8479bb3f399ca6bb))

##[2.177.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.177.0&targetVersion=GTv2.177.1&_a=commits)  (2025-06-25)


### Bug Fixes

*  screen not scrollable when font size increased ([7e71cce](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7e71cce6327b48f36a9e0cbd6edad3abbd44290b))    #8587996

#[2.177.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.176.2&targetVersion=GTv2.177.0&_a=commits)  (2025-06-20)


### Features

*  CrowdIn translation updates ([17d8eeb](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/17d8eeb966f0038f3bbbe08a66beb8a11edb12dc))    #8335139

##[2.176.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.176.1&targetVersion=GTv2.176.2&_a=commits)  (2025-06-19)


### Bug Fixes

*  **header style:** change header background color to gradient image ([5bff0f5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5bff0f56cfa862572a6828c1d67eefad58d8a302))    #8270421

##[2.176.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.176.0&targetVersion=GTv2.176.1&_a=commits)  (2025-06-17)


### Bug Fixes

*  fix translations and undo tariff urls ([c75e941](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c75e941156f6515676c5788ef3b84a89851ff479))    #8335139

#[2.176.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.175.0&targetVersion=GTv2.176.0&_a=commits)  (2025-06-11)


### Features

*  **8514760:** fix biling payment due date ([f0157c2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f0157c2a9fa5a4b8adfef44c7f6760cfd5148af0))    #8514760

#[2.175.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.174.2&targetVersion=GTv2.175.0&_a=commits)  (2025-06-05)


### Features

*  CrowdIn translation updates ([8c702a3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8c702a333b3a188f8ed9ec3a922674fe85bc6ac9))    #8513693

##[2.174.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.174.1&targetVersion=GTv2.174.2&_a=commits)  (2025-06-04)


### Bug Fixes

*  add missing analytics events ([5a1672b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5a1672bc8672518893e294b80ade0d65497b7ee2))    #8236373

##[2.174.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.174.0&targetVersion=GTv2.174.1&_a=commits)  (2025-06-03)


### Bug Fixes

*  fix resubscriber status ([6836957](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6836957bb344f373f2564c45cf25d078816cd878))    #8468480

#[2.174.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.173.0&targetVersion=GTv2.174.0&_a=commits)  (2025-05-30)


### Features

*  CrowdIn translation updates ([6f07d8e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6f07d8e9df97a6dcfd78c1e2f38e7d56e613de65))    #8462429

#[2.173.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.172.1&targetVersion=GTv2.173.0&_a=commits)  (2025-05-30)


### Features

*  CrowdIn translation updates ([38ef836](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/38ef836ae8054024d9c44dbfe0ff9dd05d040921))    #8462429

##[2.172.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.172.0&targetVersion=GTv2.172.1&_a=commits)  (2025-05-30)


### Bug Fixes

*  fix something went wrong ([5f8adb6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5f8adb6d8143eec27c681a5eb77d88551580ee79))    #8184644

#[2.172.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.171.3&targetVersion=GTv2.172.0&_a=commits)  (2025-05-30)


### Features

*  fixed subscription screens accessibility issues ([a5d86d9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a5d86d98449619732e5b28e291bcff7fba205b25))    #8462429

##[2.171.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.171.2&targetVersion=GTv2.171.3&_a=commits)  (2025-05-29)


### Bug Fixes

*  **infinite loading on regaining connection:** infinite loading on regaining... ([13013d2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/13013d2ddd47527fc36118acada2acfc5fbf1e7d))    #8491597

##[2.171.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.171.1&targetVersion=GTv2.171.2&_a=commits)  (2025-05-28)


### Bug Fixes

*  updating design of new screens as per comments ([6081b33](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6081b334611da0dde60f815afb105dc1752c8f48))    #8451230
    #8451232

##[2.171.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.171.0&targetVersion=GTv2.171.1&_a=commits)  (2025-05-28)


### Bug Fixes

*  fixed hit area issue ([3dcd687](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3dcd687427ec258f2cd291e3e00e995078b3f50f))    #8462344

#[2.171.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.170.0&targetVersion=GTv2.171.0&_a=commits)  (2025-05-28)


### Features

*  resolved accessibility of back button ([9dcc71e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9dcc71ede9c8285f2cce5e0e6366ab48b51b60d1))    #8462433

#[2.170.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.169.0&targetVersion=GTv2.170.0&_a=commits)  (2025-05-28)


### Features

*  resolved back button accessibility ([b628574](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b628574d40d81e49d7d1379fe3c5375454904c20))    #8462433

#[2.169.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.168.0&targetVersion=GTv2.169.0&_a=commits)  (2025-05-28)


### Features

*  fixed hit area issue for subscription screen ([7ec311d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7ec311d05f4c941df3ccda48ccf33c7552818b77))    #8462344

#[2.168.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.167.0&targetVersion=GTv2.168.0&_a=commits)  (2025-05-27)


### Features

*  CrowdIn translation updates ([e8723a9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e8723a91a5b428aa470250476a20a2adf9dca3ac))    #8451232

#[2.167.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.166.1&targetVersion=GTv2.167.0&_a=commits)  (2025-05-27)


### Features

*  add missing accessibility on back button ([c7b42ad](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c7b42ad9dd9903e822ea02ef6b8dbf189407d6a7))    #8462350

##[2.166.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.166.0&targetVersion=GTv2.166.1&_a=commits)  (2025-05-27)


### Bug Fixes

*  t and cs onPress ([086b30a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/086b30a9263d655131b438d006725655bb72e19d))    #8451323

#[2.166.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.165.0&targetVersion=GTv2.166.0&_a=commits)  (2025-05-23)


### Features

*  CrowdIn translation updates ([f6da22d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f6da22dbd1ad14c6a37f5a182fada2599f1b22c4))    #8335139

#[2.165.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.164.0&targetVersion=GTv2.165.0&_a=commits)  (2025-05-20)


### Features

*  offer changes UI and copy ([e709bbd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e709bbd132cb0dfbc85777b83d507848a1fa5b5c))    #8335139

#[2.164.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.163.0&targetVersion=GTv2.164.0&_a=commits)  (2025-05-15)


### Features

*  new copy and design on confirm cancellation page ([d77add1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d77add1f07ec7407c203602b181124b27e3dcc42))    #8451232

#[2.163.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.162.0&targetVersion=GTv2.163.0&_a=commits)  (2025-05-14)


### Features

*  new analytics for DE SUBS ([37d0a80](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/37d0a80591a9b32a670650d3734043d14b4db574))    #8018811

#[2.162.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.161.0&targetVersion=GTv2.162.0&_a=commits)  (2025-05-13)


### Features

*  new feature flag support ([e539b18](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e539b187bd6e673695555df1b4f8b744c94030d8))    #8434067

#[2.161.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.160.3&targetVersion=GTv2.161.0&_a=commits)  (2025-05-08)


### Features

*  CrowdIn translation updates ([bbd27cd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/bbd27cd395548950a16ed9bb6a6fbfa8002f937d))    #8345936

##[2.160.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.160.2&targetVersion=GTv2.160.3&_a=commits)  (2025-05-07)


### Bug Fixes

*  **cancelsubscriptionfailed:** add aral translations to cancel failed screen ([0052738](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0052738f9561273e0873ad373f92df0237074699))    #8345936

##[2.160.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.160.1&targetVersion=GTv2.160.2&_a=commits)  (2025-05-07)


### Bug Fixes

*  **subscriptionprovider:** set subscription cost to be 0 if the subsDiscount... ([805a632](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/805a632e3df39398ead1cfc505935e61f9b935dd))    #8246789

##[2.160.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.160.0&targetVersion=GTv2.160.1&_a=commits)  (2025-05-07)


### Bug Fixes

*  **8368518:** fix navigation after payment was succesfull ([f01c838](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f01c838b099a6829f3a49d002b335b8bdf26086a))    #8368518

#[2.160.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.159.2&targetVersion=GTv2.160.0&_a=commits)  (2025-05-02)


### Features

*  CrowdIn translation updates ([d75fbf3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d75fbf33243d7f288df086b17eca26244d8c1cb5))    #8395480

##[2.159.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.159.1&targetVersion=GTv2.159.2&_a=commits)  (2025-05-01)


### Bug Fixes

*  fixing billing cycle date on your subs screen ([853eda2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/853eda28ea42f99b90cd537d18209b577bae79d6))    #8314027


*  **mycomfortmembership:** add T&Cs link for resubscribe ([8aa3506](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8aa3506578bc60aebce575acd3a161cd94b1153f))    #8388058

##[2.159.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.159.0&targetVersion=GTv2.159.1&_a=commits)  (2025-04-23)


### Bug Fixes

*  **localise all currencies:** localise all currency values ([3305be0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3305be071ac4de81e7d2ac68227d30d802ebdbf7))    #8240302

#[2.159.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.158.1&targetVersion=GTv2.159.0&_a=commits)  (2025-04-16)


### Features

*  CrowdIn translation updates ([f3ce558](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f3ce5581c159425f23da0bd6faf9dd517961ad2f))    #8075065

##[2.158.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.158.0&targetVersion=GTv2.158.1&_a=commits)  (2025-04-15)


### Bug Fixes

*  fixing remaining bugs on onboarding screen ([155d227](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/155d2271bb2e1c4b3bbaea8e7e2dc018aa82fea6))    #8267767

#[2.158.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.157.0&targetVersion=GTv2.158.0&_a=commits)  (2025-04-15)


### Features

*  CrowdIn translation updates ([e19e20b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e19e20b5a368e86d5ca64d84c6f98d9e08139a9f))    #8267767

#[2.157.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.156.2&targetVersion=GTv2.157.0&_a=commits)  (2025-04-15)


### Features

*  **8267767:** implement new link component and fix UI ([c49e08c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c49e08caa8989db4fb1a541430e6bdbc86da7047))    #8267767

##[2.156.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.156.1&targetVersion=GTv2.156.2&_a=commits)  (2025-04-15)


### Bug Fixes

*  **onboarding rate value:** fix onboarding rate value ([2c41a2f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/2c41a2f42509692602f421540a3bb84fa8590fde))    #8240302

##[2.156.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.156.0&targetVersion=GTv2.156.1&_a=commits)  (2025-04-15)


### Bug Fixes

*  **8267652:** fix UI discrepency ([dd7aa52](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/dd7aa52016be821cb03ca895d092bba702722752))    #8267652

#[2.156.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.155.2&targetVersion=GTv2.156.0&_a=commits)  (2025-04-14)


### Features

*  CrowdIn translation updates ([0caf831](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0caf83181ac8316f3d292a095c3c4e8b6b137a6f))    #8269277

##[2.155.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.155.1&targetVersion=GTv2.155.2&_a=commits)  (2025-04-14)


### Bug Fixes

*  change the membership end date to be - 1 day ([1efe038](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1efe038d6cf8768161eb126fae45807ee541cfb2))    #8269277

##[2.155.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.155.0&targetVersion=GTv2.155.1&_a=commits)  (2025-04-14)


### Bug Fixes

*  correcting navigation from subs paused cancellation flow ([068f042](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/068f042bde92762d99b62a932b8202141a7a1306))    #8322968
    #8323039

#[2.155.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.154.2&targetVersion=GTv2.155.0&_a=commits)  (2025-04-10)


### Features

*  CrowdIn translation updates ([7ce7aae](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7ce7aaef0b4085b4b55ad69839ede2119eabc2c6))    #8075065

##[2.154.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.154.1&targetVersion=GTv2.154.2&_a=commits)  (2025-04-09)


### Bug Fixes

*  German date format ([8ab7d4a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8ab7d4af8de5793224c921c2b3c76ebc9e4f5c9d))    #8288272

##[2.154.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.154.0&targetVersion=GTv2.154.1&_a=commits)  (2025-04-09)


### Bug Fixes

*  firebase config values & screens fixes ([94a87c6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/94a87c697104fdca4c5f8c4f4e27cbf05805479d))    #8240302
    #8245705
    #8245783

#[2.154.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.153.3&targetVersion=GTv2.154.0&_a=commits)  (2025-04-08)


### Features

*  tenant country config for all subs requests ([050ba1a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/050ba1a3ce71cb555df0ced54fe9f4b178be9c21))    #8274066

##[2.153.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.153.2&targetVersion=GTv2.153.3&_a=commits)  (2025-04-08)


### Bug Fixes

*  wrong headings ([2341629](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/23416295dbc1ae746570d98742a7d0170b087ff8))    #8268877

##[2.153.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.153.1&targetVersion=GTv2.153.2&_a=commits)  (2025-04-08)


### Bug Fixes

*  incorrect cancelation date on confirm screen ([edeed2a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/edeed2a8bb06a7a52f3f5b63bafef5506754fa97))    #8269277

##[2.153.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.153.0&targetVersion=GTv2.153.1&_a=commits)  (2025-04-07)


### Bug Fixes

*  correcting navigation back to paused screen ([84341ad](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/84341adaf2cc688d2d2e2938244124e2661198a9))    #8273763

#[2.153.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.152.1&targetVersion=GTv2.153.0&_a=commits)  (2025-04-03)


### Features

*  CrowdIn translation updates ([d08448a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d08448a134cf21caebef32e0a362aca288f474bb))    #8268877

##[2.152.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.152.0&targetVersion=GTv2.152.1&_a=commits)  (2025-04-03)


### Bug Fixes

*  wrong subs screen after cancellation ([bdd700d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/bdd700d568c746200f9866ae18c41c060ae83709))    #8268877

#[2.152.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.151.1&targetVersion=GTv2.152.0&_a=commits)  (2025-04-02)


### Features

*  CrowdIn translation updates ([65ce143](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/65ce143dd63c22ea9270c51b819068acf26d7f7a))    #8075065

##[2.151.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.151.0&targetVersion=GTv2.151.1&_a=commits)  (2025-04-01)


### Bug Fixes

*  fixes on extra tariff screen ([8e83e13](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8e83e13129a2ada9977688cf19e561f4f778fa91))    #8246078

#[2.151.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.150.2&targetVersion=GTv2.151.0&_a=commits)  (2025-03-28)


### Features

*  CrowdIn translation updates ([0b7e4b7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0b7e4b723eb9951dafcc15a9d78e4a0d676e577e))    #8075065

##[2.150.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.150.1&targetVersion=GTv2.150.2&_a=commits)  (2025-03-27)


### Bug Fixes

*  ensuring inactive subs wallet users see the right screen ([1480674](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/148067410b680bd0f31de8eda10a84280937b8f9))    #8009715

##[2.150.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.150.0&targetVersion=GTv2.150.1&_a=commits)  (2025-03-26)


### Bug Fixes

*  update wallet to 6.21.1 ([bb97ce9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/bb97ce9dca4e0dd9c48aa3a1443319abfe12c34f))    #8234250

#[2.150.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.149.2&targetVersion=GTv2.150.0&_a=commits)  (2025-03-26)


### Features

*  CrowdIn translation updates ([5b7bea4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5b7bea407dbed591106d9d111e1aaa789cb7498f))    #7976748

##[2.149.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.149.1&targetVersion=GTv2.149.2&_a=commits)  (2025-03-26)


### Bug Fixes

*  **7976748:** Added Extra Tariff and Classic Tariff for every user ([e409cf6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e409cf6a64de5c3bd7aa4a2b912664a99ba51790))    #7976748

##[2.149.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.149.0&targetVersion=GTv2.149.1&_a=commits)  (2025-03-25)


### Bug Fixes

*  correcting import path ([ea3c474](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ea3c47411adb01958e705971f44e738fb37574f6))    #7976748

#[2.149.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.148.1&targetVersion=GTv2.149.0&_a=commits)  (2025-03-25)


### Features

*  **8045482:** Redirect Profile Screen when can't goBack ([5e252a4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5e252a46345fa5bb34c66c31f112c839cede2c1e))    #8045482

##[2.148.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.148.0&targetVersion=GTv2.148.1&_a=commits)  (2025-03-25)


### Bug Fixes

*  issue with superscript ([6374393](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6374393020497f6b9ff71e93b5ef69a3720399de))    #8223984

#[2.148.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.147.7&targetVersion=GTv2.148.0&_a=commits)  (2025-03-24)


### Features

*  CrowdIn translation updates ([13f8322](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/13f8322b20fe6a3b4b1ef4346a3c40a70de33506))    #7976748

##[2.147.7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.147.6&targetVersion=GTv2.147.7&_a=commits)  (2025-03-24)


### Bug Fixes

*  7976748 DE subscriptions UI naming updates ([cc48202](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/cc4820224d4bae8a03c951785d59e71ae2a22aeb))    #7976748

##[2.147.6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.147.5&targetVersion=GTv2.147.6&_a=commits)  (2025-03-19)


### Bug Fixes

*  fix remaining texts ([f4d7995](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f4d7995a0013d389186c61e8214ee71f70ca6eae))    #7980135

##[2.147.5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.147.4&targetVersion=GTv2.147.5&_a=commits)  (2025-03-19)


### Bug Fixes

*  payment method not updating ([c19bf26](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c19bf2674aa15565d2f059e0a5a50eea24dbb8fd))    #8114881

##[2.147.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.147.3&targetVersion=GTv2.147.4&_a=commits)  (2025-03-17)


### Bug Fixes

*  standardise prices format ([161d31c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/161d31ce1c31e23a2c05de3862174e7f60a996f3))    #8184347

##[2.147.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.147.2&targetVersion=GTv2.147.3&_a=commits)  (2025-03-13)


### Bug Fixes

*  updating snapshot ([e557fb1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e557fb1fe094e245e68dec638e7b2de832e4cb08))    #8172711

##[2.147.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.147.1&targetVersion=GTv2.147.2&_a=commits)  (2025-03-13)


### Bug Fixes

*  fixing redirection to correct screen for DE ([325df74](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/325df743e008bd8f432f123d3f016b0c72b00c79))    #8182197

##[2.147.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.147.0&targetVersion=GTv2.147.1&_a=commits)  (2025-03-13)


### Bug Fixes

*  fixing date on cancellation page ([3d89cf3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3d89cf395a1562e86d419d926f9face83c7a2427))    #8172711

#[2.147.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.146.0&targetVersion=GTv2.147.0&_a=commits)  (2025-03-11)


### Features

*  CrowdIn translation updates ([8506e55](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8506e55317322815cb32ef08d5d99fee3132d471))    #8075065

#[2.146.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.145.8&targetVersion=GTv2.146.0&_a=commits)  (2025-03-11)


### Features

*  updating screen to disable Amex and add new copy ([114e96c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/114e96c9b18f158168e005bbc570eb91f80388e6))    #8092865

##[2.145.8](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.145.7&targetVersion=GTv2.145.8&_a=commits)  (2025-03-10)


### Bug Fixes

*  wrong imports ([a149edc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a149edcc94060815fd6ca6e276add79946c2869b))    #8139186

##[2.145.7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.145.6&targetVersion=GTv2.145.7&_a=commits)  (2025-03-07)


### Bug Fixes

*  subscribe button status when consents are already satisfied ([a92ffbd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a92ffbd82a8357c964f407ea37882f1dbeb32dff))    #8139186

##[2.145.6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.145.5&targetVersion=GTv2.145.6&_a=commits)  (2025-03-07)


### Bug Fixes

*  7976748 DE SUBS Name changes for Invoices ([503dc3b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/503dc3bfacf20394e314ebdf0a9cb09820edd7e9))    #7976748

##[2.145.5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.145.4&targetVersion=GTv2.145.5&_a=commits)  (2025-03-07)


### Bug Fixes

*  t&c updates and links ([10aa727](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/10aa7275f45485940419d2ea7e6f8d369c0f22ea))    #8139186

##[2.145.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.145.3&targetVersion=GTv2.145.4&_a=commits)  (2025-03-07)


### Bug Fixes

*  8139181 DE SUBS Terms and Conditions checkbox text fix ([48ce7d4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/48ce7d4f6741582a87be093f51dae86ae5f73e56))    #8139181

##[2.145.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.145.2&targetVersion=GTv2.145.3&_a=commits)  (2025-03-05)


### Bug Fixes

*  show subs ending screen when cancel date ([efe4d94](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/efe4d9400cfadcff4bfff58a6a5915d37f396ea1))    #8145029

##[2.145.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.145.1&targetVersion=GTv2.145.2&_a=commits)  (2025-03-05)


### Bug Fixes

*  removed statusReason from legacy cancel subs flow ([ed7b8af](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ed7b8af3d848e292c5194ca0f211e7d17616d64a))    #8134102

##[2.145.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.145.0&targetVersion=GTv2.145.1&_a=commits)  (2025-03-03)


### Bug Fixes

*  handle api error scenario to wallet failed card ([ef2a7ed](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ef2a7ed6ab36c1925e09d7febb9f879e223344ba))    #8114881

#[2.145.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.11&targetVersion=GTv2.145.0&_a=commits)  (2025-02-27)


### Features

*  **7980135:** update ui text based on DE ([4f9888c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/4f9888c88a404034faf3dbac93cff777799f7a41))    #7980135

##[2.144.11](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.10&targetVersion=GTv2.144.11&_a=commits)  (2025-02-24)


### Bug Fixes

*  8017941: add logs ([2ed9a03](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/2ed9a03ac21ada6df9723c08cee03c19b2998538))    #8017941

##[2.144.10](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.9&targetVersion=GTv2.144.10&_a=commits)  (2025-02-20)


### Bug Fixes

*  billing date logic for cancel subscription ([ed43926](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ed43926e81732ad3922d6a3da2daf8b79ea5083f))    #8075246

##[2.144.9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.8&targetVersion=GTv2.144.9&_a=commits)  (2025-02-18)


### Bug Fixes

*  fixed the styles ([abcbe75](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/abcbe75467925084ad204d4fea260d531474cc6f))    #8073454

##[2.144.8](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.7&targetVersion=GTv2.144.8&_a=commits)  (2025-02-17)


### Bug Fixes

*  error handling for retry payment ([7c09449](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7c094497089ea4c67b78543326efee0e270cfb46))    #8067566

##[2.144.7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.6&targetVersion=GTv2.144.7&_a=commits)  (2025-02-13)


### Bug Fixes

*  8023355: fix comfort activation failed backpress ([ce66c1b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ce66c1b787b2f1d3eb915925ff95d576e11190e3))    #8023355

##[2.144.6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.5&targetVersion=GTv2.144.6&_a=commits)  (2025-02-12)


### Bug Fixes

*  8017941: update mutation ([bcb6d70](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/bcb6d7051df19ee411f5fb905a1f0785916f5e40))    #8017941

##[2.144.5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.4&targetVersion=GTv2.144.5&_a=commits)  (2025-02-11)


### Bug Fixes

*  bump ui-components version and wallet mfe version ([2b8c100](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/2b8c100274122d43d16ea1eaadb36b3194f9f986))    #7649499

##[2.144.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.3&targetVersion=GTv2.144.4&_a=commits)  (2025-02-11)


### Bug Fixes

*  8017941 : add missing arguments ([9a639e5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9a639e5368e354359191254a59ac7734089a4202))    #8017941

##[2.144.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.2&targetVersion=GTv2.144.3&_a=commits)  (2025-02-10)


### Bug Fixes

*  8017941 : fix confirm mutation ([b18e4d3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b18e4d37b379efe43586dd5ede174153996c4a3c))    #8017941

##[2.144.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.1&targetVersion=GTv2.144.2&_a=commits)  (2025-02-10)


### Bug Fixes

*  8017941: update graphql mutation ([34c5d3f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/34c5d3f701ab52394ea1ec73004e2a46ce21a0ef))    #8017941

##[2.144.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.144.0&targetVersion=GTv2.144.1&_a=commits)  (2025-02-07)


### Bug Fixes

*  8023355:  add temp logs ([717613f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/717613ffa2c7e5fa152b0c65d45613a76c223c56))    #8023355

#[2.144.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.143.1&targetVersion=GTv2.144.0&_a=commits)  (2025-02-07)


### Features

*  revert-merged-pr-682081-7986478-subscription-benefits-component-added ([3e3af32](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3e3af32070fc5f3209fa2ac36a0466e653af2c37))    #7986478
    #7986478

##[2.143.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.143.0&targetVersion=GTv2.143.1&_a=commits)  (2025-02-06)


### Bug Fixes

*  7996289: back arrow + cta ([86d6408](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/86d6408009fb227f00cd4227ab4a77cb66e3f8b1))    #7996289
    #8019805

#[2.143.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.142.1&targetVersion=GTv2.143.0&_a=commits)  (2025-02-06)


### Features

*  revert-merged-pr-682774-7986478-migration-success-screen-added ([55bfd73](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/55bfd73633b7e3bd0f62fae954ef4f61c7cb8caa))    #7986478
    #7986478

##[2.142.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.142.0&targetVersion=GTv2.142.1&_a=commits)  (2025-02-05)


### Bug Fixes

*  7996289: multiheader fix ([2207202](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/22072029005053929636b3b29c1879fa390332f8))    #7996289

#[2.142.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.141.0&targetVersion=GTv2.142.0&_a=commits)  (2025-02-05)


### Features

*  7709778: comfort activation screen minor corrections ([0e7741a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0e7741adf13ae472e19d95c4de5a7b12c35003ef))    #7709778

#[2.141.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.140.0&targetVersion=GTv2.141.0&_a=commits)  (2025-02-05)


### Features

*  7986478-migration-success-screen-added ([ff229a1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ff229a1b06ccb2e7e0a09a7ef3831d31ea12678d))    #7986478

#[2.140.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.139.1&targetVersion=GTv2.140.0&_a=commits)  (2025-02-04)


### Features

*  7709778: corrections for comfort activation screen ([dd686cc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/dd686cc84340e51e0ed508fb540cb0c450f5dc00))    #7709778

##[2.139.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.139.0&targetVersion=GTv2.139.1&_a=commits)  (2025-02-04)


### Bug Fixes

*  increase polling timeout for create subscription ([6de604e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6de604e88e1fd292c2c21d9154004e2746f130a7))    #7981420

#[2.139.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.138.4&targetVersion=GTv2.139.0&_a=commits)  (2025-02-04)


### Features

*  7986478-subscription-benefits-component-added ([84f2d1f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/84f2d1f59dad6782bd3e5f380c6d4ee0518f85eb))    #7986478

##[2.138.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.138.3&targetVersion=GTv2.138.4&_a=commits)  (2025-02-04)


### Bug Fixes

*  7996289: add margin top to offset view ([3370bfc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3370bfcb1b0583608207deea0db72b2392301c3a))    #7996289

##[2.138.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.138.2&targetVersion=GTv2.138.3&_a=commits)  (2025-02-03)


### Bug Fixes

*  cancel comfort membership call ([8cdfd3a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8cdfd3aa2afcc5bccbf9544a3023cc95fb5c888d))    #7505350

##[2.138.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.138.1&targetVersion=GTv2.138.2&_a=commits)  (2025-01-30)


### Bug Fixes

*  add comments and bump version ([f7b3ae5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f7b3ae5d02d40e0403e44dca8542919126cf0c0b))    #7709778

##[2.138.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.138.0&targetVersion=GTv2.138.1&_a=commits)  (2025-01-29)


### Bug Fixes

*  missing dates ([ee36dcf](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ee36dcf45db120dc6a2a6b6ea8417db080a29ec5))    #7866043

#[2.138.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.137.0&targetVersion=GTv2.138.0&_a=commits)  (2025-01-29)


### Features

*  update error message on subs ([4ac183c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/4ac183cf85005f1ae7412d4375caa398f7f63560))    #7697731

#[2.137.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.136.0&targetVersion=GTv2.137.0&_a=commits)  (2025-01-29)


### Features

*  7984077-activation-membership-page-invalid-import-issue ([087aff6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/087aff6ec94f87d5ba2c6039657704a4a6830ddf))    #7984077

#[2.136.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.135.0&targetVersion=GTv2.136.0&_a=commits)  (2025-01-29)


### Features

*  7971210-subscription-amount-updated ([7eaab09](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7eaab09bc94bc5a58e2fcf6f86e1d5cd7dd629fe))    #7971210

#[2.135.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.134.0&targetVersion=GTv2.135.0&_a=commits)  (2025-01-28)


### Features

*  correct cancel popups for uk and de ([3be1687](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3be1687b9a4eb326f37ed92a5bdc5abbc1b44abe))    #7709453

#[2.134.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.133.0&targetVersion=GTv2.134.0&_a=commits)  (2025-01-27)


### Features

*  add missing myComfortMembership screen to subLanding ([f968b87](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f968b87cad12de4df6b87fb4d5372fbfcd2b0cde))    #7967556

#[2.133.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.132.0&targetVersion=GTv2.133.0&_a=commits)  (2025-01-27)


### Features

*  update my comfort membership screen layout ([b627bad](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b627badf86b3ee7a1452a89dddaf8658b24e729e))    #7709325

#[2.132.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.131.0&targetVersion=GTv2.132.0&_a=commits)  (2025-01-24)


### Features

*  7504722-message-updated ([40e11e6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/40e11e6288bd4c94878a531bff9207db864bfe98))    #7504722

#[2.131.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.130.0&targetVersion=GTv2.131.0&_a=commits)  (2025-01-22)


### Features

*  cancel subscription popup ([0f1ed4d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0f1ed4dfa6f1295aee398ccbabc6d42bebe2a81c))    #7709453
    #7709453

#[2.130.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.129.0&targetVersion=GTv2.130.0&_a=commits)  (2025-01-22)


### Features

*  7504722-memmbership-onboard-dynamic-screen ([a1f1050](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a1f1050f647a7db83f9c7656907d038ed9ad48fa))    #7504722

#[2.129.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.128.0&targetVersion=GTv2.129.0&_a=commits)  (2025-01-20)


### Features

*  version bump ([4902254](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/4902254793c7a79f124307e7d7e1f2086834eea2))    #7709325

#[2.128.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.127.1&targetVersion=GTv2.128.0&_a=commits)  (2025-01-20)


### Bug Fixes

*  **ui fix for small screen devices:** ui fix for small screen devices ([c0dfbc8](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c0dfbc85b4efea460915c2847d9df34f2eaab55f))    #7862023




### Features

*  create my comfort membership screen ([45f74fe](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/45f74fe93b5a6466a99277a237942b750fc14010))    #7709325

##[2.127.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.127.0&targetVersion=GTv2.127.1&_a=commits)  (2025-01-17)


### Bug Fixes

*  change pt to px ([0f2bb60](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0f2bb60e567603db84673b8c9363a6e5d7138544))    #7778377

#[2.127.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.126.2&targetVersion=GTv2.127.0&_a=commits)  (2025-01-17)


### Features

*  **7505350:** cancel comfort membership screen ([8d11e6b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8d11e6bfe78f1e689f044a6ea231874103dc212f))    #7505350

##[2.126.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.126.1&targetVersion=GTv2.126.2&_a=commits)  (2025-01-17)


### Bug Fixes

*  **7751982:** move toUpperCase out of query into a function that calls it ([a36ca6a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a36ca6a6e486c8fe5c44939cf463afffa1d58c01))    #7751982

##[2.126.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.126.0&targetVersion=GTv2.126.1&_a=commits)  (2025-01-17)


### Bug Fixes

*  fix for the click of the here text ([487b0cc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/487b0cc7b020ec94a79656fe75f2531a4557901d))    #7849201

#[2.126.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.125.4&targetVersion=GTv2.126.0&_a=commits)  (2025-01-16)


### Features

*  **7709790:** Update UI - Cancel confirmation screens ([94dc6b2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/94dc6b28c4fb2a5233e13997032cb7e5712e9f64))    #7709790

##[2.125.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.125.3&targetVersion=GTv2.125.4&_a=commits)  (2025-01-16)


### Bug Fixes

*  modified the implementation and styling changes ([c58f104](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c58f10439b1ba4ca20a2f96e46089150acb57e86))    #7778377

##[2.125.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.125.2&targetVersion=GTv2.125.3&_a=commits)  (2025-01-15)


### Bug Fixes

*  populate the right data ([e2f4987](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e2f498794ffa49e13c15a8d86bfca570a156229c))    #7833271

##[2.125.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.125.1&targetVersion=GTv2.125.2&_a=commits)  (2025-01-15)


### Bug Fixes

*  update version of wallet mfe ([5309b36](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5309b3600abff8538ca83c44054f549b413619c0))    #7769517

##[2.125.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.125.0&targetVersion=GTv2.125.1&_a=commits)  (2025-01-14)


### Bug Fixes

*  fix padding on cta button ([9d1a536](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9d1a53646d62cbd9e4a88f74abe1ef3ff4b16bee))    #7748614

#[2.125.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.124.1&targetVersion=GTv2.125.0&_a=commits)  (2025-01-14)


### Features

*  CrowdIn translation updates ([93671a2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/93671a2bee2ebf665b75c4019dad5abcf909e965))    #5604944

##[2.124.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.124.0&targetVersion=GTv2.124.1&_a=commits)  (2025-01-14)


### Bug Fixes

*  **7750149:** fix padding issue for subscription-success ([5cb4263](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5cb42631474a03b5ef7af10be61f8631e05fa77b))    #7750149

#[2.124.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.123.0&targetVersion=GTv2.124.0&_a=commits)  (2025-01-10)


### Features

*  updated the pricing url ([f109c9f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f109c9f006d3355c8c97562711b04685f103d2d2))    #7750172

#[2.123.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.122.0&targetVersion=GTv2.123.0&_a=commits)  (2025-01-10)


### Features

*  CrowdIn translation updates ([0664dfe](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0664dfe178b59259a821102acebcf63dc8a23135))    #7750172

#[2.122.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.121.1&targetVersion=GTv2.122.0&_a=commits)  (2025-01-10)


### Features

*  **7751982:** auto-capitalise offer code ([50fe33e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/50fe33e8c620fc48714e3d69338ab958d1cff241))    #7751982

##[2.121.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.121.0&targetVersion=GTv2.121.1&_a=commits)  (2025-01-10)


### Bug Fixes

*  add extra check for date ([e368aa4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e368aa4a816e858abe5f1611dd5db4971ee38a60))    #7769529

#[2.121.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.120.0&targetVersion=GTv2.121.0&_a=commits)  (2025-01-08)


### Features

*  fine print for asterisk on the subscription screen ([11802f7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/11802f71fe2ba1ba58cc9777b264d4be5521465f))    #7750172
    #7778377

#[2.120.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.119.2&targetVersion=GTv2.120.0&_a=commits)  (2025-01-08)


### Features

*  enforce security scans on pipelines ([1553e05](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1553e053e4f4cf00e44671d95b7ee8f7bdceaad1))    #7732649

##[2.119.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.119.1&targetVersion=GTv2.119.2&_a=commits)  (2025-01-08)


### Bug Fixes

*  check if user is subs wallet check when calling getWalletSubscription ([e6bc4aa](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e6bc4aa360835431c2d47df53d5a147a14688a17))    #7797168

##[2.119.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.119.0&targetVersion=GTv2.119.1&_a=commits)  (2025-01-01)


### Bug Fixes

*  profile section (post subs cancellation) - billing date shows as nanth ([c7e69a3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c7e69a3110d6c535dccbb953c2bb7e399105e8bf))    #7752170

#[2.119.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.118.1&targetVersion=GTv2.119.0&_a=commits)  (2024-12-13)


### Features

*  **7495654:** Updated and mapped translation ([1a2a744](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1a2a7445698146a691c81771a36a0323de0f7d63))    #7495654

##[2.118.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.118.0&targetVersion=GTv2.118.1&_a=commits)  (2024-12-12)


### Bug Fixes

*  **7705653:** update vulnerable axios package ([d6bdca6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d6bdca6af7ac8543c73195b90150b0bbb51f976a))    #7705653

#[2.118.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.117.0&targetVersion=GTv2.118.0&_a=commits)  (2024-12-12)


### Features

*  **7709453:** Create 'Cancel Subscription' pop-up modal ([c82db24](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c82db24ca59d9f1b824ef5ce3a74ee2b2490dc3d))    #7709453

#[2.117.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.116.7&targetVersion=GTv2.117.0&_a=commits)  (2024-12-09)


### Features

*  CrowdIn translation updates ([9fa0ba5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9fa0ba500fbf9626cc44416a3cc387a54f02f395))

##[2.116.7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.116.6&targetVersion=GTv2.116.7&_a=commits)  (2024-12-04)


### Bug Fixes

*  ui components version bump 12.15 ([550dc42](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/550dc422694c540b0e29976ac10e768d7104cd8b))    #7661366

##[2.116.6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.116.5&targetVersion=GTv2.116.6&_a=commits)  (2024-12-04)


### Bug Fixes

*  bump version of wallet mfe with lock file ([daee597](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/daee597cab79136bc1e528ae99cd931a99405e0b))    #7661366

##[2.116.5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.116.4&targetVersion=GTv2.116.5&_a=commits)  (2024-11-28)


### Bug Fixes

*  bump version of subs mfe with lock file ([f57eedf](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f57eedf8e0e5a09eb036398a011336ccdeedbc24))    #7661366

##[2.116.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.116.3&targetVersion=GTv2.116.4&_a=commits)  (2024-11-27)


### Bug Fixes

*  bump version of wallet mfe with lock file ([38853ff](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/****************************************))    #7661366

##[2.116.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.116.2&targetVersion=GTv2.116.3&_a=commits)  (2024-11-27)


### Bug Fixes

*  format the date only one time ([67bdca8](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/67bdca8394f10cd73d33a530c2716bc3cae87306))    #7667256

##[2.116.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.116.1&targetVersion=GTv2.116.2&_a=commits)  (2024-11-27)


### Bug Fixes

*  cancel subscription and offer discount ([7f41b51](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7f41b51060fa269eaf4f01b03b43fd36c138b0b6))    #7667039
    #7667094
    #7667256

##[2.116.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.116.0&targetVersion=GTv2.116.1&_a=commits)  (2024-11-26)


### Bug Fixes

*  set the correct canceled date on FE ([6a5f132](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6a5f132aed3cc38ca649fccbdfc2735234493399))    #7603367
    #7606774

#[2.116.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.115.6&targetVersion=GTv2.116.0&_a=commits)  (2024-11-26)


### Features

*  update subscribe and save screen intro offer ([d35745c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d35745cc0ec9ad271d9fb034fe218858d532cb9d))    #7632346

##[2.115.6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.115.5&targetVersion=GTv2.115.6&_a=commits)  (2024-11-26)


### Bug Fixes

*  bump version of wallet mfe with lock file ([6b2900c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6b2900cec484f866bcc391877dce1b522ffa50d6))    #7622031

##[2.115.5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.115.4&targetVersion=GTv2.115.5&_a=commits)  (2024-11-22)


### Bug Fixes

*  subscription price flickers as nan ([c48d79c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c48d79c0154e706b5331a8a0a545407e32e25c03))    #7618952

##[2.115.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.115.3&targetVersion=GTv2.115.4&_a=commits)  (2024-11-21)


### Bug Fixes

*  add right redirect on natural back button ([474ba35](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/474ba3516606658a8de7b6cf0e54f451d9be3360))    #7618945

##[2.115.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.115.2&targetVersion=GTv2.115.3&_a=commits)  (2024-11-21)


### Bug Fixes

*  bump version of wallet mfe with lock file ([3c33812](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3c33812f419091b2c13268b367a2fdfad9251996))    #7598262

##[2.115.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.115.1&targetVersion=GTv2.115.2&_a=commits)  (2024-11-20)


### Bug Fixes

*  right way of calculating amount ([93739d2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/93739d27feff4812ec06aa94a7634a30c56f1803))    #7606774

##[2.115.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.115.0&targetVersion=GTv2.115.1&_a=commits)  (2024-11-20)


### Bug Fixes

*  memberships not being ordered and filtered correctly ([52dbdf2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/52dbdf21991be0027302322a5534e38053bcbd89))    #7618996


*  set total amount for combo offers ([72e7ebb](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/72e7ebb08d96a014e024228da0ef38e23e3fc5a0))    #7601854

#[2.115.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.114.0&targetVersion=GTv2.115.0&_a=commits)  (2024-11-19)


### Features

*  bump up wallet version ([ac696ab](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ac696abd311edd02ed90b3ec438112a4573058e6))    #7370384

#[2.114.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.113.0&targetVersion=GTv2.114.0&_a=commits)  (2024-11-18)


### Features

*  **7513785:** added a new ADAC subs confirmation screen ([51983f1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/51983f1509ab626914451dbd9c12cf1020d253f4))    #7513785

#[2.113.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.112.3&targetVersion=GTv2.113.0&_a=commits)  (2024-11-15)


### Features

*  7513810 user partner type check when to show SUBS nav screens ([1ebc979](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/****************************************))    #7513810

##[2.112.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.112.2&targetVersion=GTv2.112.3&_a=commits)  (2024-11-13)


### Bug Fixes

*  adds hascanceledmembership hasactivemembership to check the membership status ([a5677a2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a5677a24602378d18ebfcab1e47df22746adb657))    #7568797

##[2.112.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.112.1&targetVersion=GTv2.112.2&_a=commits)  (2024-11-08)


### Bug Fixes

*  autoCheck the offerCode when we have it in routeParams ([1737515](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1737515d541bb58c214eefbc11aa9f4091fa86e0))    #7569084

##[2.112.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.112.0&targetVersion=GTv2.112.1&_a=commits)  (2024-11-07)


### Bug Fixes

*  **7542693:** added loading screen to dynamic values screens ([fee827f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/fee827f5ba52a15003bb4c7573c2c084d3994ad0))    #7542693

#[2.112.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.111.1&targetVersion=GTv2.112.0&_a=commits)  (2024-11-06)


### Features

*  show notification when payment method is updated from the subscription paused screen ([8bcda07](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8bcda071d4eb2522d7d21e30b718ef60132652e8))    #7546588

##[2.111.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.111.0&targetVersion=GTv2.111.1&_a=commits)  (2024-11-01)


### Bug Fixes

*  only show the banner on changing the payment method ([f61c914](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f61c914871a8547e19eb00551fa5cc7309f920ba))    #7542821

#[2.111.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.110.2&targetVersion=GTv2.111.0&_a=commits)  (2024-11-01)


### Features

*  add loading indicator for subscription details component ([71ab5af](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/71ab5af6075f525962a5eb9ff23fbd1b1fd746de))    #7542598

##[2.110.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.110.1&targetVersion=GTv2.110.2&_a=commits)  (2024-10-31)


### Bug Fixes

*  quick setup missing from subcription and save screen ([7bdc31b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7bdc31b5f83d324f2fa77c735424386a983b268d))    #7537727

##[2.110.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.110.0&targetVersion=GTv2.110.1&_a=commits)  (2024-10-31)


### Bug Fixes

*  set subsDiscount for combo offer ([5e7bc18](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5e7bc1801593db83f425eb9e8c115e7b9e3dc7f8))    #7538655

#[2.110.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.109.0&targetVersion=GTv2.110.0&_a=commits)  (2024-10-28)


### Features

*  refetch subscription after cancellation ([b96a442](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b96a442dad1900d5641966f8c1197f33ebaf29ad))    #7414096

#[2.109.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.108.1&targetVersion=GTv2.109.0&_a=commits)  (2024-10-24)


### Features

*  **7431241:** added start date row inside of <MySubscription /> ([c32af5c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c32af5ce157741bf791320c3f01e4a2ce067d789))    #7431241

##[2.108.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.108.0&targetVersion=GTv2.108.1&_a=commits)  (2024-10-23)


### Bug Fixes

*  bump up the wallet mfe for fixing the bug for bad right padding ([a69bf41](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a69bf41eb4157f05371815e4c8426a5648450071))    #7468693

#[2.108.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.107.0&targetVersion=GTv2.108.0&_a=commits)  (2024-10-22)


### Features

*  change the logic to send offerCode only if different than empty string ([87ea07f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/87ea07fbdc7617b2ad039a084a18dcbe7626620d))    #7460765

#[2.107.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.106.2&targetVersion=GTv2.107.0&_a=commits)  (2024-10-21)


### Features

*  **translations:** crowdin translation updates ([63371ee](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/63371eeb9753e05b784570f075f8b7b61cdee5ed))    #7431241

##[2.106.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.106.1&targetVersion=GTv2.106.2&_a=commits)  (2024-10-21)


### Bug Fixes

*  bump up wallet version with fix for mastercards padding ([ab390c1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ab390c189b5739e82af979512b7ec3af11e8c30c))    #7468693

##[2.106.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.106.0&targetVersion=GTv2.106.1&_a=commits)  (2024-10-16)


### Bug Fixes

*  bump wallet mfe version for recache flow ([b216333](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b216333b6ff9c92d078021bb2f353905caa4aaf3))    #7321391

#[2.106.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.105.0&targetVersion=GTv2.106.0&_a=commits)  (2024-10-11)


### Features

*  add subsDefault to trigger add payment function ([efef3e5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/efef3e553466f788767c9ddac988cb22318336f0))    #7321394

#[2.105.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.104.0&targetVersion=GTv2.105.0&_a=commits)  (2024-10-11)


### Features

*  analytic events for cvv ([b272507](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b272507fb47c333c799f88a5693cfb4aa4f32695))    #7236524

#[2.104.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.103.0&targetVersion=GTv2.104.0&_a=commits)  (2024-10-11)


### Features

*  added new cancelledOn field ([f182c23](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f182c23fb84923a20bd479d3ea20fd3ad5d27784))    #7421105

#[2.103.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.102.0&targetVersion=GTv2.103.0&_a=commits)  (2024-10-04)


### Features

*  add the new microsite link to the env ([fa58ccc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/fa58ccc24bbdd29cfae8449b034e541e1593ebe1))    #7321394

#[2.102.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.101.0&targetVersion=GTv2.102.0&_a=commits)  (2024-09-30)


### Features

*  send rfid params from subs mfe ([8d6bddd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8d6bddd555806fda91cf74158a9b251e3f9396d6))    #7363260

#[2.101.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.100.0&targetVersion=GTv2.101.0&_a=commits)  (2024-09-24)


### Features

*  add subs analytics ([a5be790](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a5be790394b94418836db16a232c26949374fb31))    #6261383

#[2.100.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.99.1&targetVersion=GTv2.100.0&_a=commits)  (2024-09-19)


### Features

*  intergrate cvv flow into the subs mfe for changing  subscription payment method ([cc88485](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/cc884858db54ec5d574f8c5439f062b8ab404afc))    #7121444

##[2.99.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.99.0&targetVersion=GTv2.99.1&_a=commits)  (2024-09-17)


### Bug Fixes

*  filter the loading screen, so the user is not getting stuck ([1e4d32b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/****************************************))    #7320871

#[2.99.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.98.2&targetVersion=GTv2.99.0&_a=commits)  (2024-09-17)


### Features

*  set isSubsDefault for wallet component ([d919fa7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d919fa7b409b4caa08ec5df508bb88c356e812c2))    #7255175

##[2.98.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.98.1&targetVersion=GTv2.98.2&_a=commits)  (2024-09-13)


### Bug Fixes

*  resolved the loop after fail create subscription ([be5479b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/be5479b173a2c981e667659f368fb987df2db863))    #7281795
    #7297781

##[2.98.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.98.0&targetVersion=GTv2.98.1&_a=commits)  (2024-09-11)


### Bug Fixes

*  fixing issue for host app with subs navigation ([8e42e74](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8e42e7467ac35dc3e457108585cbd02dfcd3b28b))    #7116216

#[2.98.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.97.1&targetVersion=GTv2.98.0&_a=commits)  (2024-09-11)


### Features

*  integrate cvv capture flow ([024f7df](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/024f7df844d5a8715d9aa5bf2858567915458635))    #7116216
    #7124868

##[2.97.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.97.0&targetVersion=GTv2.97.1&_a=commits)  (2024-09-10)


### Bug Fixes

*  check the N/A value of the next billing date ([c2037f3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c2037f35b3cdd9f99405ad942b0d1d69a423d670))    #7279389

#[2.97.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.96.0&targetVersion=GTv2.97.0&_a=commits)  (2024-09-06)


### Features

*  update text depending on membership ([18dbcb1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/18dbcb1e7db591b32e7b14c6184e00b17f0eea1b))    #7179752

#[2.96.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.95.2&targetVersion=GTv2.96.0&_a=commits)  (2024-09-03)


### Features

*  CrowdIn translation updates ([ed8ffef](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ed8ffef3d30a4eaab560d1c24d8b86c91ef47753))    #7169759

##[2.95.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.95.1&targetVersion=GTv2.95.2&_a=commits)  (2024-08-28)


### Bug Fixes

*  remove X button ([d2f3777](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d2f3777c5187e6c921655aaf30b644051e3285f9))    #7203391

##[2.95.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.95.0&targetVersion=GTv2.95.1&_a=commits)  (2024-08-27)


### Bug Fixes

*  resolved the wrong navigation from the don't cancel flow ([1990cb3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1990cb38e5887cf9037d1ba2e8c32316f6248de7))    #7203674

#[2.95.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.94.3&targetVersion=GTv2.95.0&_a=commits)  (2024-08-27)


### Features

*  added notification component ([7a2b629](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7a2b629fff5959e4b038b52dce1e351f047bc8f3))    #7121423

##[2.94.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.94.2&targetVersion=GTv2.94.3&_a=commits)  (2024-08-23)


### Bug Fixes

*  bump wallet version, add subsFlag to wallet provider ([8ccb0ce](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8ccb0ce63b182776991cfc5316e747d369164dd0))    #7203939

##[2.94.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.94.1&targetVersion=GTv2.94.2&_a=commits)  (2024-08-19)


### Bug Fixes

*  **getSubsPlan:** fix getSubsPlan graphQL call ([ba46f0b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ba46f0be3952f5cca6ab3836d398caa544d20740))    #6881585

##[2.94.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.94.0&targetVersion=GTv2.94.1&_a=commits)  (2024-08-14)


### Bug Fixes

*  remove screen from dashboard ([d1c1fb7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d1c1fb702d0bb33dcfde4ee7e97a81dd8399808a))    #6512533

#[2.94.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.93.1&targetVersion=GTv2.94.0&_a=commits)  (2024-08-14)


### Features

*  apply offer on subscription upgrade flow ([44b8d68](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/44b8d68b17c36adfa1a3a5877fdfa59daee18a52))    #7107563

##[2.93.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.93.0&targetVersion=GTv2.93.1&_a=commits)  (2024-08-14)


### Bug Fixes

*  **7107396:** postal code error ([0778dc2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0778dc2d597d80e0c77ab6012e03303059e0f7a9))    #7107396

#[2.93.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.92.0&targetVersion=GTv2.93.0&_a=commits)  (2024-08-13)


### Features

*  **6437839:** cancel membership ([f045ef1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f045ef1e7c399b8e097af3ecf4c85f637ef92721))    #6437839

#[2.92.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.91.0&targetVersion=GTv2.92.0&_a=commits)  (2024-08-12)


### Features

*  pre select offer code from params & handle back navigation ([65c870e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/65c870e8bd65a86a8ce638b09935c2258cb9eeef))    #6881585

#[2.91.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.90.0&targetVersion=GTv2.91.0&_a=commits)  (2024-08-12)


### Features

*  create wallet subscription polling ([aeba243](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/aeba243854c8269bcbe7124f775ede523ded9d2c))    #7042399

#[2.90.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.89.1&targetVersion=GTv2.90.0&_a=commits)  (2024-08-07)


### Features

*  error handling for to many offer codes ([2e09da2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/2e09da283ecf5294112a0e72e14eda1c8cc75f49))    #6991447

##[2.89.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.89.0&targetVersion=GTv2.89.1&_a=commits)  (2024-08-06)


### Bug Fixes

*  android back navigation and subs ending header ([ead1d7f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ead1d7f56620a60b99c14a6ffb7f8b543092d454))    #6973461
    #7083242

#[2.89.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.88.3&targetVersion=GTv2.89.0&_a=commits)  (2024-08-06)


### Features

*  retrying subscription wallet payment on the subscription paused screen ([383a380](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/383a380a2de9252d8d9fc0cab2ee30ba8811463b))    #6512477

##[2.88.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.88.2&targetVersion=GTv2.88.3&_a=commits)  (2024-07-31)


### Bug Fixes

*  added logic to load the cancel subscription screen with check box selected ([832134f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/832134fb74fdc7aff011e4f6128c49356470c9ab))    #7028550

##[2.88.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.88.1&targetVersion=GTv2.88.2&_a=commits)  (2024-07-30)


### Bug Fixes

*  **7048091:** loading state for check offer button ([f74f59f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f74f59f062168dd2185b3143eba7ecc7bd2924d0))    #7048091

##[2.88.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.88.0&targetVersion=GTv2.88.1&_a=commits)  (2024-07-23)


### Bug Fixes

*  navigation fixes after cancel subscription ([65ce6e5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/65ce6e5e0e937f2a2d247ab75e48c84cd7920230))    #6990584
    #7027855

#[2.88.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.87.0&targetVersion=GTv2.88.0&_a=commits)  (2024-07-12)


### Features

*  CrowdIn translation updates ([4eeffff](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/4eeffff616ae2aecfa9c7c124ee85e6c4dd60f9c))    #6922184

#[2.87.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.86.2&targetVersion=GTv2.87.0&_a=commits)  (2024-07-11)


### Features

*  update the text for offer code check ([2776a41](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/2776a417c9582d0b4b53968a6bf0bab9dbeb631d))    #6922184

##[2.86.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.86.1&targetVersion=GTv2.86.2&_a=commits)  (2024-07-11)


### Bug Fixes

*  prevent the user to add to many offer codes ([407c301](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/407c30168deafe80a5752213bf32bf9b10a7ecf9))    #6689261

##[2.86.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.86.0&targetVersion=GTv2.86.1&_a=commits)  (2024-07-10)


### Bug Fixes

*  added rate dynamic value that I missed ([7617764](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/761776497b55835286ee474903e9aee7878f89a3))    #6944905

#[2.86.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.85.0&targetVersion=GTv2.86.0&_a=commits)  (2024-07-10)


### Features

*  CrowdIn translation updates ([9a34bb9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9a34bb9264d97e137de917f45c671e1924d74649))    #5604944

#[2.85.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.84.1&targetVersion=GTv2.85.0&_a=commits)  (2024-07-09)


### Features

*  swap % for {{}} with dynamic values ([210b48f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/210b48fac5ab9c5baee478f257017d31d1d31824))    #6944905

##[2.84.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.84.0&targetVersion=GTv2.84.1&_a=commits)  (2024-07-09)


### Bug Fixes

*  fixing tests that are preventing crowdIn pr ([7596bfa](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7596bfa7beb39a733e37e5a13dbf158a4fd2af6c))    #6587335

#[2.84.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.83.2&targetVersion=GTv2.84.0&_a=commits)  (2024-07-09)


### Features

*  make brand dynamic ([d82383c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d82383cf66004924be4d8df6616502d0f786e522))    #6587335

##[2.83.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.83.1&targetVersion=GTv2.83.2&_a=commits)  (2024-07-03)


### Bug Fixes

*  bump wallet to fix analytics ([a5a6672](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a5a6672132ce73ab58f5f4a6ca5ecc94eeb14f8e))    #6589521

##[2.83.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.83.0&targetVersion=GTv2.83.1&_a=commits)  (2024-07-03)


### Bug Fixes

*  fixed swipeable bug for subs ([498a3a0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/498a3a0464fa0569fdfdb1fa191e5651ac4ee43a))    #6914189

#[2.83.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.82.0&targetVersion=GTv2.83.0&_a=commits)  (2024-07-03)


### Features

*  analytics event fix ([b83a21e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b83a21eda3e8f6e6b734366c5753601ef1716cbb))    #6529977

#[2.82.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.81.0&targetVersion=GTv2.82.0&_a=commits)  (2024-07-01)


### Features

*  add navigation to the subscription ending subscribe and save screen ([7a9ba7c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7a9ba7c6f5f61df95baa61399e98e9fd13546634))    #6495955

#[2.81.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.80.0&targetVersion=GTv2.81.0&_a=commits)  (2024-06-28)


### Features

*  **confirmsubscription:** change confirm subscription cta when no card is added ([fde4e74](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/fde4e74a87a153cf96ad63f5cc0d237b4cb43f84))    #6893077

#[2.80.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.79.0&targetVersion=GTv2.80.0&_a=commits)  (2024-06-27)


### Bug Fixes

*  fixed back navigation on subs landing screen ([00d1bbc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/00d1bbcc4bab91f37ced561e6dd4d44a546633c6))    #6914189




### Features

*  update validation of offer code ([dde3228](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/dde322879d2766cff3039eb877d4ea27f1f52d5e))    #6689261

#[2.79.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.78.1&targetVersion=GTv2.79.0&_a=commits)  (2024-06-27)


### Features

*  **6476329:** small improvements to the paused screen ([1891c4d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1891c4d48aea3a01697dfca9ced8b53f2b8fea59))    #6476329

##[2.78.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.78.0&targetVersion=GTv2.78.1&_a=commits)  (2024-06-24)


### Bug Fixes

*  update bp/ui-components to 12.9.1 ([5a9cc3f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5a9cc3f1706954397947cbb5ab7d17b71c5bfb74))    #6530313

#[2.78.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.77.0&targetVersion=GTv2.78.0&_a=commits)  (2024-06-24)


### Features

*  navigation bug fix ([0aeebb1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0aeebb1bd5932cda0c89206adf760e5f6fb93198))    #6529977

#[2.77.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.76.0&targetVersion=GTv2.77.0&_a=commits)  (2024-06-20)


### Features

*  create UI and navigation to the subscription ending subscribe and save screen ([d932257](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d932257cdc1a67416fdf0c86a5f0509916926784))    #6495955

#[2.76.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.75.0&targetVersion=GTv2.76.0&_a=commits)  (2024-06-18)


### Features

*  CrowdIn translation updates ([a6b8a6b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a6b8a6b7e3c24db4843f6419fc14bba9d37899f6))    #6437835
    #6476329
    #6529977

#[2.75.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.74.0&targetVersion=GTv2.75.0&_a=commits)  (2024-06-13)


### Features

*  Functional logic and mapping from getSubscription ([1497a76](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1497a76922203554c86202993c70531cca9e1781))    #6437835

#[2.74.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.73.1&targetVersion=GTv2.74.0&_a=commits)  (2024-06-13)


### Features

*  **6476329:** subscription paused screen ([9019966](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/90199668d4c5ac10b2e2fd9b2dae46912130f267))    #6476329

##[2.73.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.73.0&targetVersion=GTv2.73.1&_a=commits)  (2024-06-12)


### Bug Fixes

*  move offer validation ([2342101](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/234210114a0f60d1f696ba33818294cbcfa39ff0))    #6749227

#[2.73.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.72.1&targetVersion=GTv2.73.0&_a=commits)  (2024-06-12)


### Features

*  added onMySubsPress callback ([dba59e5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/dba59e51bfd54d125e2d2a76c939b15d1c64d303))    #6530313

##[2.72.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.72.0&targetVersion=GTv2.72.1&_a=commits)  (2024-06-10)


### Bug Fixes

*  modified the getSubsPlan query ([3eabd84](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3eabd844903ace2d75999cd95f4aafecdb943148))    #6825485

#[2.72.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.71.0&targetVersion=GTv2.72.0&_a=commits)  (2024-06-10)


### Features

*  CrowdIn translation updates ([311632c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/311632ce19e9d5869ff0f5887b4becffa4095064))    #5604944

#[2.71.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.70.0&targetVersion=GTv2.71.0&_a=commits)  (2024-06-05)


### Bug Fixes

*  change something about the logic ([699d040](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/699d040e576bf04eb1e89a29cbd596ea2df3b11a))    #6587340




### Features

*  CrowdIn translation updates ([aaae628](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/aaae62811d10b647ac0c2a69b8d87f23437e726b))    #5604944

#[2.70.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.69.0&targetVersion=GTv2.70.0&_a=commits)  (2024-06-04)


### Features

*  remove unused feature flags ([d65d1ae](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d65d1aeee8ef1f9f3b19629a5fd644ed08f59722))    #6270836

#[2.69.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.68.0&targetVersion=GTv2.69.0&_a=commits)  (2024-05-31)


### Features

*  add new feature flags + refactoring ([f804d5b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f804d5b9baee5ab164e482c8b5d2d658c5cf2fd9))    #6587340

#[2.68.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.67.0&targetVersion=GTv2.68.0&_a=commits)  (2024-05-29)


### Features

*  add right validations for the RFID screens ([8a17756](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8a1775682e959e9f3a7fd41f7a3121886f1a404d))    #6493731

#[2.67.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.66.0&targetVersion=GTv2.67.0&_a=commits)  (2024-05-27)


### Features

*  implement the right way to navigate to the RFID mfe ([8d76441](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8d76441ccd04c3143d3de70468c29d185d940012))    #6225999

#[2.66.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.65.0&targetVersion=GTv2.66.0&_a=commits)  (2024-05-22)


### Features

*  populate the subscription details section ([a5cca17](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a5cca172d87f95e0d2239cc616c07aeec9ebcee9))    #6479842

#[2.65.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.64.0&targetVersion=GTv2.65.0&_a=commits)  (2024-05-14)


### Features

*  navigation of the RFID ([20312b6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/20312b6bd567bc9ff0d91ab2d89f378ef493c7f3))    #6225999

#[2.64.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.63.0&targetVersion=GTv2.64.0&_a=commits)  (2024-05-13)


### Features

*  **6275573:** functional logic and navigations on the "confirm" subscription screen ([bb5d9b7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/bb5d9b7adfeedc7c74aa01f3500402ad2b2c2324))    #6275573

#[2.63.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.62.0&targetVersion=GTv2.63.0&_a=commits)  (2024-05-09)


### Features

*  **6306609:** offer code validity check ([c58a7ae](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c58a7ae657284fccc2431693780f5e96ae319b0f))    #6306609
    #6560527
    #6593542

#[2.62.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.61.0&targetVersion=GTv2.62.0&_a=commits)  (2024-05-08)


### Features

*  check if PAYG-Wallet user is a first time subscriber or not ([328afaf](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/328afafb4456a2407794c0d287a3d61045471fb7))    #6238264

#[2.61.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.60.1&targetVersion=GTv2.61.0&_a=commits)  (2024-05-07)


### Features

*  CrowdIn translation updates ([c54efc2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c54efc256503951d425a0bc3909abce5b84e692a))    #5604944

##[2.60.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.60.0&targetVersion=GTv2.60.1&_a=commits)  (2024-05-07)


### Bug Fixes

*  update header on mysubscription screen ([8c9bfab](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8c9bfab65169eaf479d50e157f9fabd0797af8bf))    #6586905

#[2.60.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.59.0&targetVersion=GTv2.60.0&_a=commits)  (2024-05-07)


### Features

*  CrowdIn translation updates ([5753578](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5753578b0b0742b731d910cc8842fcaeba6913fa))    #5604944

#[2.59.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.58.0&targetVersion=GTv2.59.0&_a=commits)  (2024-05-02)


### Features

*  **mysubscription:** update content on my subscription screen ([73d2e9b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/73d2e9b95d311b694823f0f5db45d70543a10e89))    #6586905

#[2.58.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.57.0&targetVersion=GTv2.58.0&_a=commits)  (2024-04-19)


### Features

*  update the wallet version, to validate the right flow ([87214fd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/87214fde34f7a9ca2b0d5bc5e3e6e79bba636208))    #6355297

#[2.57.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.56.0&targetVersion=GTv2.57.0&_a=commits)  (2024-04-18)


### Features

*  added UI for cancel modal component ([1e3f214](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1e3f2146d2c27810c76adf5365ba2ba1e117abb6))    #6225762

#[2.56.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.55.0&targetVersion=GTv2.56.0&_a=commits)  (2024-04-18)


### Features

*  first implementation of my subscription screen ([7baeca6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7baeca6f8c6a53cd386be1fdfa8af4782e2721a7))    #6218081

#[2.55.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.54.1&targetVersion=GTv2.55.0&_a=commits)  (2024-04-15)


### Features

*  **navigation:** implement redirect functionality for 'Start charge' button ([71f5fba](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/71f5fba0ec91289f0f6bb2b83e26273cba87308b))    #6256940

##[2.54.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.54.0&targetVersion=GTv2.54.1&_a=commits)  (2024-04-15)


### Bug Fixes

*  resolved wrong spelling from the design ([2313f3d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/2313f3d793ac8048001c0275edfe0996207b6d64))    #6225785

#[2.54.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.53.0&targetVersion=GTv2.54.0&_a=commits)  (2024-04-12)


### Features

*  CrowdIn translation updates ([b1d9172](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b1d91726a7c7e57c38a01a9c140c5d2cb6d73a35))    #5604944

#[2.53.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.52.0&targetVersion=GTv2.53.0&_a=commits)  (2024-04-10)


### Features

*  create first UI part for cancel subscription screen ([f937796](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f937796d8d6d87aa481d45987de18b2dc04555c6))    #6225785

#[2.52.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.51.0&targetVersion=GTv2.52.0&_a=commits)  (2024-04-05)


### Features

*  navigation back from subscription failed ([0887bf2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0887bf24ae651ed55b79e77155f1d189b2d1a83a))    #6278154

#[2.51.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.50.0&targetVersion=GTv2.51.0&_a=commits)  (2024-04-05)


### Features

*  CrowdIn translation updates ([49c6207](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/49c6207d72c6f82db488639189373cf53fbd280b))    #5604944

#[2.50.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.49.0&targetVersion=GTv2.50.0&_a=commits)  (2024-04-04)


### Features

*  CrowdIn translation updates ([d9c741c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d9c741cefabb3ff3c68e8dab7ab6e38017478d52))    #5604944

#[2.49.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.48.0&targetVersion=GTv2.49.0&_a=commits)  (2024-04-04)


### Features

*  navigate PAYG-wallet user to confirm subscription screen ([e1fe81c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e1fe81c770284cfa66e7f3d6f37da0aa22f32aa2))    #6225781

#[2.48.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.47.0&targetVersion=GTv2.48.0&_a=commits)  (2024-04-03)


### Features

*  bump up the wallet mfe ([793575b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/793575b6017a88b4e1338070116c32f3212787e5))    #6218454

#[2.47.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.46.0&targetVersion=GTv2.47.0&_a=commits)  (2024-04-03)


### Features

*  payg-wallet user navigation who doesn't already have a payment method in their wallet upgrading to subscription ([d165481](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d165481ee972ff3dc10faadb0eabcc1d23854713))    #6220787

#[2.46.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.45.2&targetVersion=GTv2.46.0&_a=commits)  (2024-04-01)


### Features

*  add ana and gabriela to git secrets ([aca205d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/aca205d38f40332da4c4b37912700154006bd4bd))    #5769168

##[2.45.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.45.1&targetVersion=GTv2.45.2&_a=commits)  (2024-03-28)


### Bug Fixes

*  add Cancel screen to dashboard ([26eec9e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/26eec9e401fa99c4a58a9ce2166efe9f67869d83))    #6225800

##[2.45.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.45.0&targetVersion=GTv2.45.1&_a=commits)  (2024-03-27)


### Bug Fixes

*  remove warm welcome for resubscription ([c2712c2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c2712c27569af5ec79b1e7e1aa824a3fd9735cda))    #6451152

#[2.45.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.44.2&targetVersion=GTv2.45.0&_a=commits)  (2024-03-27)


### Features

*  add configurator screen to the MFE ([280247b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/280247bb500bfdadb1d0bc5f3c93db45db0dff5e))    #6405401

##[2.44.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.44.1&targetVersion=GTv2.44.2&_a=commits)  (2024-03-26)


### Bug Fixes

*  fixed typo ([352fd98](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/352fd98f8180cc53d1ecc967df3de8536393dfb9))    #6256940

##[2.44.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.44.0&targetVersion=GTv2.44.1&_a=commits)  (2024-03-25)


### Bug Fixes

*  update the text on ConfirmSubscription screen ([83a2d3a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/83a2d3a4d93f0dc166746d7c6605c97a55999b87))    #6238481

#[2.44.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.43.1&targetVersion=GTv2.44.0&_a=commits)  (2024-03-21)


### Features

*  **6225920:** subscription upgrade success screen ([6d9ef08](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6d9ef084e808e3b0b071e60a3a210106ac1b2ed7))    #6225920


*  CrowdIn translation updates ([ba7ee44](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ba7ee4409c3da68c4d29f257c7a68d62d6790a8f))    #5604944

##[2.43.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.43.0&targetVersion=GTv2.43.1&_a=commits)  (2024-03-21)


### Bug Fixes

*  **6226020:** add screen to stack navigator ([1558b53](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1558b530c6af058618231d998d3abd4ab39f5d2c))    #6226020

#[2.43.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.42.1&targetVersion=GTv2.43.0&_a=commits)  (2024-03-19)


### Features

*  add the confirm subscription screen ([08247ff](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/08247ff41aca225106b1f1b328c5735f45892f30))    #6238481

##[2.42.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.42.0&targetVersion=GTv2.42.1&_a=commits)  (2024-03-12)


### Bug Fixes

*  **cancelmembership:** added user type check before navigation ([5d06a6d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5d06a6d08d7ea2eae6debef3c251f711cbd2550d))    #6225800

#[2.42.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.41.0&targetVersion=GTv2.42.0&_a=commits)  (2024-03-12)


### Features

*  add dan to git secrets ([d883919](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d883919f89177dbfbc4a26cf0c189dbdaad4c0d5))    #5769168

#[2.41.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.40.3&targetVersion=GTv2.41.0&_a=commits)  (2024-03-12)


### Features

*  add the subscription cancelled screen ([612825a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/612825a669f9dab5ea7ec3ef1d736c5a940af346))    #6225800

##[2.40.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.40.2&targetVersion=GTv2.40.3&_a=commits)  (2024-03-08)


### Bug Fixes

*  add some left gap for the underline text ([f1e5c43](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f1e5c432dc521f9f18d29860f21048d040c0ee8f))    #6234529

##[2.40.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.40.1&targetVersion=GTv2.40.2&_a=commits)  (2024-03-07)


### Bug Fixes

*  send the right type to the function ([735655a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/735655a80e084f3f25f3ad7e7166bc27f164cf6b))    #6234529

##[2.40.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.40.0&targetVersion=GTv2.40.1&_a=commits)  (2024-03-06)


### Bug Fixes

*  **updating ui comp library:** updating ui comp library ([39dc707](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/39dc70744b2e5d843c12b15de1de6ec29a782fcb))    #6339791

#[2.40.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.39.1&targetVersion=GTv2.40.0&_a=commits)  (2024-03-06)


### Features

*  CrowdIn translation updates ([3403915](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/340391501fc7ee2b2a3c50fb99b92883afea1fb7))    #6166382

##[2.39.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.39.0&targetVersion=GTv2.39.1&_a=commits)  (2024-03-05)


### Bug Fixes

*  remove the internetReachable = true default value ([152674d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/152674d02cfd20bcc9c4f345306c3c8093f25eed))    #6274857

#[2.39.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.38.0&targetVersion=GTv2.39.0&_a=commits)  (2024-03-05)


### Features

*  added mihai to git secrets ([54af9bc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/54af9bc16a29d8ae851809195cdc503ab9f28f52))    #5769168

#[2.38.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.37.1&targetVersion=GTv2.38.0&_a=commits)  (2024-02-29)


### Features

*  add the new screen flow ([c051f0b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c051f0b5a722ebf12ba2dc63a2f29a72818f4128))    #6217650

##[2.37.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.37.0&targetVersion=GTv2.37.1&_a=commits)  (2024-02-23)


### Bug Fixes

*  updating subscription screen to use translations and not hardcoded English ([3e9316e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3e9316eadef142a72634e1929238643bb3db4210))    #6243010

#[2.37.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.36.1&targetVersion=GTv2.37.0&_a=commits)  (2024-02-23)


### Features

*  CrowdIn translation updates ([283fb8d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/283fb8de6106c1d3f80e642e713207f8879ac8ad))    #6196215

##[2.36.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.36.0&targetVersion=GTv2.36.1&_a=commits)  (2024-02-20)


### Bug Fixes

*  updating ui comp version ([d04f79b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d04f79bd088286779b53522944a62f0cdd2df1ed))    #6258031

#[2.36.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.35.0&targetVersion=GTv2.36.0&_a=commits)  (2024-02-20)


### Features

*  CrowdIn translation updates ([abbd1ba](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/abbd1ba42396922c98a8cb62060b0962edab62d5))    #6243010

#[2.35.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.34.0&targetVersion=GTv2.35.0&_a=commits)  (2024-02-20)


### Features

*  CrowdIn translation updates ([9ee19bb](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9ee19bb8243dac9b84dab6060417adb83fd63393))    #6243010

#[2.34.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.33.1&targetVersion=GTv2.34.0&_a=commits)  (2024-02-19)


### Features

*  CrowdIn translation updates ([e823471](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e8234710f73ea76dccb89118864a0765ac51e09e))    #6243010

##[2.33.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.33.0&targetVersion=GTv2.33.1&_a=commits)  (2024-02-05)


### Bug Fixes

*  add missing pending subscription screen to subscription landing page ([7f78870](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7f788703635abe5b17a0bc583edc26868d5c1944))    #6167500

#[2.33.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.32.1&targetVersion=GTv2.33.0&_a=commits)  (2024-01-18)


### Features

*  **screen:** add payment required screen ([f14bae6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f14bae6c4f41554d4546df4d3f7acd27ef05e700))    #6037281

##[2.32.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.32.0&targetVersion=GTv2.32.1&_a=commits)  (2023-12-14)


### Bug Fixes

*  new release ([aa287c4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/aa287c47f70423bd4529bcc97107c2ff44fa5e44))    #5848828

#[2.32.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.31.0&targetVersion=GTv2.32.0&_a=commits)  (2023-12-08)


### Features

*  CrowdIn translation updates ([53f4bc6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/53f4bc6e61936e4d277e1c2b201059df8f81651d))    #5679914

#[2.31.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.30.0&targetVersion=GTv2.31.0&_a=commits)  (2023-11-20)


### Features

*  updating version of ui-component to meet minimum requirement ([7863ffc](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7863ffc7b465dafefdc20c7e05367cb3443ec46b))    #5735949
    #5735949

#[2.30.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.29.1&targetVersion=GTv2.30.0&_a=commits)  (2023-11-14)


### Features

*  updating the type and use of locale ([e4b0804](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e4b080497d3b826e377ec7b1c99c75636da5a3ae))    #5813007
    #5813007

##[2.29.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.29.0&targetVersion=GTv2.29.1&_a=commits)  (2023-10-18)


### Bug Fixes

*  add deep merging of the messages.json so that updating locales manually... ([7f011ea](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7f011eae8f02b77bd005bd73397042bee689bde0))    #5679914

#[2.29.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.28.0&targetVersion=GTv2.29.0&_a=commits)  (2023-10-17)


### Features

*  crowdIn translation updates ([d77c9f4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d77c9f45aafd7c26803c565659c5e09c7d9ef123))    #5679914

#[2.28.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.27.1&targetVersion=GTv2.28.0&_a=commits)  (2023-10-17)


### Features

*  5679914 copy update subscribe and save screen ([c94ded6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c94ded6d13b53b96d8565ef1941da710b8414aba))    #5679914

##[2.27.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.27.0&targetVersion=GTv2.27.1&_a=commits)  (2023-10-03)


### Bug Fixes

*  5615366 User is able to scroll to far on the "Subscribe and save" page ([eed31ea](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/eed31eafce124b9fcd12fb9f40f424cf708840ee))    #5615366

#[2.27.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.26.0&targetVersion=GTv2.27.0&_a=commits)  (2023-09-22)


### Features

*  5527581 Update to use consistent pulse theming on buttons ([f07a97a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f07a97a5a883976f0d02db38e9a7f191aa99470a))    #5527581

#[2.26.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.25.0&targetVersion=GTv2.26.0&_a=commits)  (2023-09-13)


### Features

*  5415317-deactivate-the-apply-button-on-the-addoffercode-screen-when-user-has-no-signal ([f5582d3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f5582d310027acac714259b9b1b9b00cfc7ea255))    #5415317
    #5503314
    #5503326

#[2.25.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.24.0&targetVersion=GTv2.25.0&_a=commits)  (2023-09-11)


### Features

*  **updated:** Checkmarx template updated ([34a79ae](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/34a79ae328967d370d8501a62e69ec642f7e7013))    #5517032

#[2.24.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.23.0&targetVersion=GTv2.24.0&_a=commits)  (2023-09-11)


### Features

*  5480166 Retro | Refactor subs landing page ([bbd3338](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/bbd3338b8a1f7e9a8cfa61cd42acaddf1ee3d870))    #5480166
    #5480166

#[2.23.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.22.0&targetVersion=GTv2.23.0&_a=commits)  (2023-09-11)


### Features

*  added translation tooling ([7571c22](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7571c225c61691572d8185725c82d6e17a99b15b))    #5442695

#[2.22.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.21.1&targetVersion=GTv2.22.0&_a=commits)  (2023-08-30)


### Features

*  5358280 no signal or api error handling ([3207689](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/32076892d796b634a3b1dd4cca20d5c8060b9293))    #5358280

##[2.21.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.21.0&targetVersion=GTv2.21.1&_a=commits)  (2023-08-25)


### Bug Fixes

*  bug-5352683-shipping-address-page-need-to-tap-twice-on-the-continue-btn ([f426fd9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f426fd97d09626e7c629b3cac5851981bf7872d9))    #5352683

#[2.21.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.20.2&targetVersion=GTv2.21.0&_a=commits)  (2023-08-24)


### Features

*  removed the card for both sub and non sub user ([8f75d3d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8f75d3d82da724f7c5c7092cafa8187fbdf44481))    #5330811
    #5435177
    #5435209

##[2.20.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.20.1&targetVersion=GTv2.20.2&_a=commits)  (2023-08-23)


### Bug Fixes

*  5352692 Subscription MFE | Pulse point access screen should be 'bouncy' ([05f3deb](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/05f3debe8303a69ba6d1be6c18fec221b8bc9dab))    #5352692

##[2.20.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.20.0&targetVersion=GTv2.20.1&_a=commits)  (2023-08-23)


### Bug Fixes

*  5352415 Subscribe and save screen should be bouncy like all the other screens in the app ([860e49e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/860e49e8df293475d05d129531f4da40851ae7f5))    #5352415

#[2.20.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.19.0&targetVersion=GTv2.20.0&_a=commits)  (2023-08-22)


### Features

*  5352536: Subscription MFE | FE | Change name of "pulse point access" screen to "Access method" ([c7fc468](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c7fc46840ab3c8e92751038bad83f6b452e9b7c0))    #5352536

#[2.19.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.18.2&targetVersion=GTv2.19.0&_a=commits)  (2023-08-22)


### Features

*  5323981 Subscription MFE | Update "Subscribe and save" page for returning subscribers ([6bdd002](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6bdd002536dc3ba3914ea09f5ece130d601ebe8d))    #5323981

##[2.18.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.18.1&targetVersion=GTv2.18.2&_a=commits)  (2023-08-17)


### Bug Fixes

*  fix type issues and apply correct QA users ([55bb5b2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/55bb5b2c8402f9441f3e5ec6eeab1c41536fd4c6))    #5410810

##[2.18.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.18.0&targetVersion=GTv2.18.1&_a=commits)  (2023-08-10)


### Bug Fixes

*  undefined error code is causing the voucher text to not have an error display ([adaaf12](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/adaaf1280df2003c75b4cd55683db00f5278598a))    #5388127

#[2.18.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.17.4&targetVersion=GTv2.18.0&_a=commits)  (2023-08-01)


### Features

*  retrieve external urls from settings provider ([c3d3e45](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c3d3e45bc847be973ebb0dfc792e6e89eb8457f9))    #5230508

##[2.17.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.17.3&targetVersion=GTv2.17.4&_a=commits)  (2023-07-25)


### Bug Fixes

*  yarn update on all dependencies ([d6987e6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/d6987e61f358782a81a8c015b44a0325e02d6e03))    #4687918

##[2.17.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.17.2&targetVersion=GTv2.17.3&_a=commits)  (2023-07-24)


### Bug Fixes

*  apply voucher code navigation ([a51662a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/a51662a1c18e2e244488308872e91f1ecbfe9400))    #5319934

##[2.17.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.17.1&targetVersion=GTv2.17.2&_a=commits)  (2023-07-18)


### Bug Fixes

*  logic to determine if user is resubscribing ([e5de5b1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e5de5b107817afcaf60afbc97a6f817b505bb090))    #5286571

##[2.17.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.17.0&targetVersion=GTv2.17.1&_a=commits)  (2023-07-17)


### Bug Fixes

*  cancellation completed navigation screen ([3c3f836](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3c3f83677f092b3c26746e621c11d17e8308fb67))    #5286504

#[2.17.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.16.0&targetVersion=GTv2.17.0&_a=commits)  (2023-07-17)


### Features

*  update to compare rates link ([12e34ed](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/12e34ed83b623d7e208080ed22c089774edaf096))    #5104932

#[2.16.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.15.0&targetVersion=GTv2.16.0&_a=commits)  (2023-07-13)


### Features

*  new checkmarx configuration ([547d39e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/547d39e579cb80933204166f13103ab04dc0400f))    #5271026

#[2.15.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.14.2&targetVersion=GTv2.15.0&_a=commits)  (2023-07-12)


### Features

*  update toBeCancelled property type ([c6df616](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c6df616d0aff6c8f993fcd5d7e9835a9492054ad))    #5255735

##[2.14.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.14.1&targetVersion=GTv2.14.2&_a=commits)  (2023-07-10)


### Bug Fixes

*  **temporary pending mandate:** create new temporary pending status ([3e67883](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3e678835c8aac465fc109b8521b956137cf0530d))    #5138595

##[2.14.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.14.0&targetVersion=GTv2.14.1&_a=commits)  (2023-07-07)


### Bug Fixes

*  **userinfoprovider:** fix missing userId ([0b15fd7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0b15fd730f3b14f3c69eb7fa2babd0879736e232))    #5138595

#[2.14.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.13.0&targetVersion=GTv2.14.0&_a=commits)  (2023-07-06)


### Features

*  Set pending mandate temporarily ([ea74f9d](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ea74f9d3fe1fcd5e706c480cac3fa41923bf3491))    #5138595

#[2.13.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.12.3&targetVersion=GTv2.13.0&_a=commits)  (2023-06-28)


### Features

*  updates to JoinGocardless Screen ([891d4f0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/891d4f021598917dc15230406e7516ea181fdfca))    #5052196
    #5052210

##[2.12.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.12.2&targetVersion=GTv2.12.3&_a=commits)  (2023-06-27)


### Bug Fixes

*  package installation error ([9232692](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/92326923e646cf3d7833fd5a53588c2b0d5b424a))    #5191143

##[2.12.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.12.1&targetVersion=GTv2.12.2&_a=commits)  (2023-04-19)


### Bug Fixes

*  update sandbox token value ([9812bce](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9812bce0624f4b3e77b0cf44de128dabf89fc274))    #4809351

##[2.12.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.12.0&targetVersion=GTv2.12.1&_a=commits)  (2023-04-11)


### Bug Fixes

*  removing-authenticated-prop ([fd8d833](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/fd8d83352ac02b87c3889ddd230b2f55a1464a68))    #4734500

#[2.12.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.11.1&targetVersion=GTv2.12.0&_a=commits)  (2023-04-03)


### Features

*  analytic events payload updates ([b5578dd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b5578dd07efee7686bd8a29444d40dac59d922b2))    #4707412

##[2.11.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.11.0&targetVersion=GTv2.11.1&_a=commits)  (2023-03-29)


### Bug Fixes

*  **tech debt:** remove unused User attributes and utilize userinfo throughout application ([b97c0b9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/b97c0b992a99f3de4b11aafa1fe5669971354ebc))    #4512664
    #4512664

#[2.11.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.10.0&targetVersion=GTv2.11.0&_a=commits)  (2023-03-22)


### Features

*  analytics payload update ([bff2f07](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/bff2f07658596ca1d7cd527583032994884128f4))    #4502297

#[2.10.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.9.0&targetVersion=GTv2.10.0&_a=commits)  (2023-03-21)


### Features

*  update back arrow styling ([386f39f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/386f39fe6319fe0706e09ccd318e472e6c4acfda))    #4672826

#[2.9.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.8.0&targetVersion=GTv2.9.0&_a=commits)  (2023-03-16)


### Features

*  hide skip this step option for active members ([12d4467](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/12d4467ccfa2ef38d3678402c7600babc03e870e))    #4659708

#[2.8.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.7.0&targetVersion=GTv2.8.0&_a=commits)  (2023-03-13)


### Features

*  add new QA users ([8331e15](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/8331e15095c6d52251f58e5cecdcc05c390a85bc))    #4639119

#[2.7.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.6.0&targetVersion=GTv2.7.0&_a=commits)  (2023-02-17)


### Features

*  sandbox refactoring ([ff8cc2b](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ff8cc2b5bac44956df2b95081f415b9550db2e9b))    #4530664

#[2.6.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.5.5&targetVersion=GTv2.6.0&_a=commits)  (2023-02-14)


### Features

*  set initial route name ([514d49e](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/514d49ebff5af1a579214c57bb43288283f081e1))    #4488936

##[2.5.5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.5.4&targetVersion=GTv2.5.5&_a=commits)  (2023-02-13)


### Bug Fixes

*  remove duplicate font ([7a6df0a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7a6df0aa91a0d842fe703148627f06ef68086ee3))    #4449625

##[2.5.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.5.3&targetVersion=GTv2.5.4&_a=commits)  (2023-02-07)


### Bug Fixes

*  remove async await from test ([329f999](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/329f99934d5b26b1ac365a80b8188b22d9d1b65c))    #4370433

##[2.5.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.5.2&targetVersion=GTv2.5.3&_a=commits)  (2023-02-06)


### Bug Fixes

*  fix voucher code error display ([4bad0f8](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/4bad0f8dad690b42c1fa088bcaa034dbc236ec67))    #4370433

##[2.5.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.5.1&targetVersion=GTv2.5.2&_a=commits)  (2023-01-28)


### Bug Fixes

*  spinner styling ([2330fe3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/2330fe3519decbb99eae1a1b58dc2454f9f255f9))    #4410750

##[2.5.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.5.0&targetVersion=GTv2.5.1&_a=commits)  (2023-01-27)


### Bug Fixes

*  code cleanup ([bcdc12a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/bcdc12a73407cb0ee6dd35d3c824f2829a182a70))    #4398309

#[2.5.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.4.0&targetVersion=GTv2.5.0&_a=commits)  (2023-01-25)


### Features

*  **translations:** derive translation-set from locale prop ([5cdebb0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/5cdebb05aabef708b977f0ea3621697062c58568))    #4346824

#[2.4.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.3.8&targetVersion=GTv2.4.0&_a=commits)  (2023-01-23)


### Features

*  **cancel-subscription:** add cancel subscription functionality ([ebffdc6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ebffdc60e1404d2064f335ea96315f8b411f5e6e))    #3952246

##[2.3.8](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.3.7&targetVersion=GTv2.3.8&_a=commits)  (2023-01-23)


### Bug Fixes

*  test update ([7fa1470](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/7fa1470cf972b2af0cf42bab037f0116ea236099))    #4373227

##[2.3.7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.3.6&targetVersion=GTv2.3.7&_a=commits)  (2023-01-19)


### Bug Fixes

*  pending user logic ([9c72ec7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9c72ec79892c433625af3db18e2b8e35d03959e8))    #4364051

##[2.3.6](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.3.5&targetVersion=GTv2.3.6&_a=commits)  (2023-01-18)


### Bug Fixes

*  global app navigation issues ([34c5fe1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/34c5fe1e61cd20cb5295fdebf2cc3df45796b38e))    #4363840

##[2.3.5](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.3.4&targetVersion=GTv2.3.5&_a=commits)  (2023-01-18)


### Bug Fixes

*  **navigation:** handle possible undefined value on routes array ([0947170](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/0947170dd658b3ead30c45f160ba2257002f5898))    #4352220

##[2.3.4](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.3.3&targetVersion=GTv2.3.4&_a=commits)  (2023-01-16)


### Bug Fixes

*  fix navigation issues ([f62ea83](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/f62ea83e6846dde5331c5d11f5873339963a5bf7))

##[2.3.3](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.3.2&targetVersion=GTv2.3.3&_a=commits)  (2023-01-13)


### Bug Fixes

*  **screens:** remove route params, get route from nav, set initial route to landing screen ([1d93f40](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1d93f4057ce90e51dc5ef792cbbf24ee1b074dd9))    #4346805

##[2.3.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.3.1&targetVersion=GTv2.3.2&_a=commits)  (2023-01-11)


### Bug Fixes

*  update sandbox test users ([90cae10](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/90cae10db499109062f52c699f84810946d17e2c))    #4321821

##[2.3.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.3.0&targetVersion=GTv2.3.1&_a=commits)  (2023-01-10)


### Bug Fixes

*  add token api to all env secrets ([212edb9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/212edb9af2861016f86c481e9bfe0a56984a7c8e))    #4321821

#[2.3.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.2.1&targetVersion=GTv2.3.0&_a=commits)  (2023-01-04)


### Features

*  **shippingaddress:** added more test coverage ([9a1c0dd](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9a1c0ddd005edb69edd96a2d1559ff4764c5d64b))    #4204768

##[2.2.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.2.0&targetVersion=GTv2.2.1&_a=commits)  (2022-12-20)


### Bug Fixes

*  update button label and translations ([dfce928](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/dfce928fbd3b46ba5528ba8b787dc96a705fc4c0))    #4267113

#[2.2.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.1.1&targetVersion=GTv2.2.0&_a=commits)  (2022-12-13)


### Features

*  **enable resubscribe flow:** enable resubscribe flow ([c70b2f7](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/c70b2f7fd6dc9b059d11cfd0de74fe81bd1c658e))    #4181165

##[2.1.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.1.0&targetVersion=GTv2.1.1&_a=commits)  (2022-12-07)


### Bug Fixes

*  remove fonts that cause conflict with sdk ones, upgrade sdk to v1.19.1 ([47d9b87](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/47d9b8799f5c0ffa14a9c20eda376757ec17fbf4))    #4204965

#[2.1.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.0.2&targetVersion=GTv2.1.0&_a=commits)  (2022-12-05)


### Features

*  address provider test coverage ([3a9f5cf](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/3a9f5cfcbfad53b1598ac9a5470c0043f86ed63f))    #4106870

##[2.0.2](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.0.1&targetVersion=GTv2.0.2&_a=commits)  (2022-12-05)


### Bug Fixes

*  add npm command to copy assets to dist during build ([565a12a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/565a12aed8139cf38fae889b3b1e7d07de32772b))    #4106870

##[2.0.1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv2.0.0&targetVersion=GTv2.0.1&_a=commits)  (2022-12-05)


### Bug Fixes

*  **outagebanner.test:** added outage banner test ([ccd5e6a](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/ccd5e6a8f695654aa838248535aec7b8bbe6d5f7))    #4106870

#[2.0.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv1.2.0&targetVersion=GTv2.0.0&_a=commits)  (2022-12-01)


*  chore(mfe launch screen): configure MFE to consume user object from launch screen ([024c65f](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/024c65f54fb3ec096aca45b457ba59e1d874ddd4))



### BREAKING CHANGES

* MFE will need user object to be passed down

#[1.2.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv1.1.0&targetVersion=GTv1.2.0&_a=commits)  (2022-12-01)


### Features

*  **secrets:** init git secrets repository and add environment files ([18f426c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/18f426ce5cb1bbf653b16525f2727d42695d3aed))    #4185081

#[1.1.0](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/branchCompare?baseVersion=GTv1.0.0&targetVersion=GTv1.1.0&_a=commits)  (2022-11-30)


### Bug Fixes

*  Run build validation ([4492bf9](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/4492bf90977c4f07b5a930ffe64656e886a108a0))    #4127227


*  use bp ui component library header for consistency in the example app ([21d8f67](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/21d8f679006a832b0e11b3f892bd5811e1eb89ae))    #4127227




### Features

*  **package.json:** upgrade framework sdk to use latest updates to release notes ([54e0bee](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/54e0bee9b76a8b8a5ebdec01b43a1cc0023f249b))    #3875771

#1.0.0  (2022-11-23)


### Bug Fixes

*  only check the last commit message for commit lint policy ([e3735b1](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/e3735b1f229fb1341b08fe86539a89cc2c930856))    #3875771


*  **prepare-commit-msg:** removed prepare commit msg ([6b08032](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/6b080321b932bdc6f8b0b5e9f3632720f0f571c1))    #3875771


*  remove husky installation on ci ([9adbb2c](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/9adbb2c6a1acf218a04e2173d72ba9f000c2984e))    #3875771




### Features

*  add release handling process from mobile sdk with automated change log ([1581c01](https://dev.azure.com/bp-digital/DCM_Frameworks/_git/bp-mfe-feature-subscription/commit/1581c0106c12f13a3240e9ed4b5d6c09de87321c))    #3875771
