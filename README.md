# @bp/subscription

Provides the client with the capabilities to manage subscriptions based charging.

Provides an option to have separate subscription flow based on app country or business.

## Usage

Follow the guidelines in the [How to guide](https://dev.azure.com.mcas.ms/bp-digital/DST-Digital_AMU/_wiki/wikis/Bp%20Pulse%20Sdk%20release%20changelog/86719/how-to) to learn about using the MFE template with the Pulse SDK.

### Prerequisites

You must have created an ADO PAT and updated your home .npmrc to give you access to the ADO artifact feeds. Follow the instructions under NPM on https://dev.azure.com/bp-digital/PROF-SPE/_packaging?_a=connect&feed=bp-mfe-framework.

#### Installed libraries

You must already have installed:

- [Node](https://nodejs.org/en/) downloaded and installed globally
- [Watchman] (https://facebook.github.io/watchman/docs/install.html) downloaded and installed globally

Optional dependency:

- [lerna](https://www.npmjs.com/package/lerna) downloaded and installed globally. If you would rather not do this you can use `npx lerna ...` in place of `lerna ...`.
- [nvm](https://github.com/nvm-sh/nvm#installing-and-updating): Install nvm and run the `nvm use` command to switch to the required version of node. For Windows, specify a version of node >= 16 to use. For example, `nvm use 16.13.0`

### Install dependencies

Run these commands in sequence to install all project dependencies.

```shell
yarn install
```

## Development

To run the application locally you will need a minium of 2 terminal instances, 3 if you want to run both android and ios in parallel.
In 1 terminal instance run

```shell
yarn start
```

This will build the application and then watch the src file for any new changes.

In a second or third terminal instance you can run the following commands to run the application in ios or android respectively.

```shell
yarn ios

```

```shell
yarn android
```

- To skip auto pod installation

```bash
yarn pulse ios --skipPods
```

- To link assets (fonts) before building with xcode

```bash
yarn pulse setup
```

## Possible Issues

- if yarn ios fails make sure that pods were installed
  check in node_modules -> @bp -> pulse-mobile-sdk -> ios -> Pods
  you can also run pod install from ios
