{"name": "@bp/mfe-subscription", "version": "2.195.4", "description": "Subscription MFE", "license": "UNLICENSED", "author": "", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["assets/", "dist/"], "scripts": {"android": "pulse android", "prebuild": "yarn clean", "build": "tsc -p tsconfig.build.json", "ci:danger": "yarn danger && DANGER_REPORT_PATH='../../../danger-report.json' npx pulse danger", "clean": "rm -rf dist", "cm": "cz", "danger": "DANGER_REPORT_PATH='./danger-report.json' npx danger local -b remotes/origin/main", "ios": "pulse ios", "link": "pulse link", "lint:fix": "prettier --write . && yarn lint:scripts --fix", "lint:prettier": "prettier --check .", "lint:scripts": "eslint --ext .js,.jsx,.ts,.tsx .", "lint:styles": "stylelint '**/*.styles.ts'", "lint:vectors": "svgo $(find ./assets -name '*.svg')", "package:name": "echo $npm_package_name", "package:version": "echo $npm_package_version", "package:version:bump": "npm --no-git-tag-version version patch", "pods": "pulse pods", "prepare": "is-ci || husky install", "setup": "pulse setup", "start": "pulse start", "test": "jest --watchAll --coverage -c jest.config.js", "test:ci": "jest --ci --coverage -c jest.config.js", "types": "tsc --noEmit"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "resolutions": {"debug": "4.3.4", "decode-uri-component": "0.3.0", "semver": "7.5.4", "yaml": "2.2.2"}, "dependencies": {"axios": "^1.7.9", "copyfiles": "^2.4.1", "expo": "^52.0.28", "graphql-tag": "^2.12.6", "lodash.defaultsdeep": "^4.6.1", "moment": "^2.29.4", "react-native-keyboard-aware-scroll-view": "^0.9.5"}, "devDependencies": {"@babel/plugin-proposal-decorators": "^7.24.0", "@bp/bppay-wallet-feature": "6.55.0", "@bp/pulse-auth-sdk": "^0.2.32", "@bp/pulse-mobile-sdk": "3.9.0", "@bp/rfid-mfe": "1.20.0", "@bp/ui-components": "^12.18.3", "@calm/eslint-plugin-react-intl": "^1.4.1", "@react-native-community/eslint-config": "^3.2.0", "@react-native/metro-config": "^0.77.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^12.4.1", "@types/jest": "^29.5.11", "@types/lodash.defaultsdeep": "^4.6.9", "eslint": "^8.55.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-sonarjs": "^0.23.0", "eslint-plugin-you-dont-need-lodash-underscore": "^6.13.0", "is-ci": "^3.0.1", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-fetch-mock": "^3.0.3", "jest-junit": "^16.0.0", "sort-package-json": "^2.4.1", "typescript": "5.1.6", "yup": "1.2.0"}, "peerDependencies": {"@apollo/client": ">=3.7.9", "@bp/mfe-helper-apollo": ">=0.0.3", "@bp/pulse-shared-types": "^1.2.0", "@bp/ui-components": ">=12.7.0", "@react-native-async-storage/async-storage": ">=1.14.1", "@react-navigation/native": ">=6.0.8", "@react-navigation/stack": ">=6.0.8", "react": "18.2.0", "react-native": "0.72.7", "react-native-device-info": ">=9.0.2", "react-native-reanimated": ">=2.8.0", "react-native-safe-area-context": "4.7.4", "react-native-svg": ">=12.3.0", "react-native-webview": ">=13.6.3", "styled-components": ">=5.3.3"}, "packageManager": "yarn@1.22.22"}