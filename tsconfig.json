{"compilerOptions": {"target": "esnext", "module": "es6", "jsx": "react-native", "declaration": true, "outDir": "./dist", "rootDir": "./", "isolatedModules": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "moduleResolution": "node", "typeRoots": ["./types", "./node_modules/@types"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["assets", "sandbox", "dist", "component.tsx", "node_modules"]}