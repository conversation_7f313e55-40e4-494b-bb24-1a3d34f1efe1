import { PaymentCard } from '@bp/bppay-wallet-feature';
import { SupportedLocale } from '@bp/pulse-shared-types/lib/enums/SupportedLocale';
import { useNetInfo } from '@react-native-community/netinfo';
import React, {
  createContext,
  ReactElement,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

import {
  PaymentMethod,
  SubscriptionProvider,
} from '../../src/common/interfaces';
import { IQAUser } from '../config/users';
import { navigate, navigation } from '../utils/navigation';
import { SubscriptionScreenNames } from '../../src/common/enums';
import { SFConsentUpdate } from '@bp/pulse-auth-sdk';

export interface SubscriptionProviderWithEnv extends SubscriptionProvider {
  environment: string;
  micrositeUrl: string;
  tokens?: boolean;
}

interface IConfigProvider {
  tokenConfig: boolean;
  setTokenConfig: (tokenConfig: boolean) => void;
  children: ReactElement;
}

interface ContextProvider {
  config: SubscriptionProviderWithEnv;
  setConfig: (config: SubscriptionProviderWithEnv) => void;
  tokenConfig: boolean;
  setTokenConfig: (tokenConfig: boolean) => void;
  setUser: (user: IQAUser) => void;
  user: IQAUser;
}

// @ts-expect-error
export const ConfigContext = createContext<ContextProvider>({});

export const useConfig = (): ContextProvider => useContext(ConfigContext);

const defaultConfig = {
  locale: SupportedLocale.EN_GB,
  apiKey: '',
  apiURL: '',
  micrositeUrl: '',
  emspTermsAndConditions: '',
  tariffPricing: '',
  navigation: {},
  onExitMFE: () => null,
  onRequestToStartCharge: () => null,
  getToken: () =>
    new Promise<string>(() => {
      return '';
    }),
  user: {},
  featureFlags: {
    block_subscription_upgrade_during_migration: false,
    enableInvoicesList: true,
    enableOfferDetails: true,
    enableRFID: false,
    extraTariffDiscountPercentage: 10,
    introOfferCredit: 0,
    introOfferCreditDuration: 0,
    introPercentageDiscount: 20,
    subsChargingRatesPromo: 0.44,
    disableIntroOffer: false,
    savingRate: 55,
    'subsChargeRateAC<22kW': 0.42,
    'subsChargeRateDC<50kW': 0.46,
    'subsChargeRateDC>50kW': 0.54,
    subscriptionAmount: 7.49,
    pollingTimeout: 36000,
    enableThreeDsInCreateSubscription: false,
  },
  onPaymentRequired: () => null,
  onAnalyticsEvent: () => null,
  brand: '',
  consents: [],
  updateConsents: (_: SFConsentUpdate[]) => {},
  requiredConsents: [],
};

const ConfigProvider = ({
  tokenConfig,
  setTokenConfig,
  children,
}: IConfigProvider) => {
  const [user, setUser] = useState<IQAUser>({
    userId: '',
    feature: '',
    country: 'UK',
    type: '',
  });
  const netInfo = useNetInfo().isConnected;
  const [cardComponent, setCardComponent] = useState<PaymentMethod>();

  useEffect(() => {
    setCardComponent(<PaymentCard isSubsDefault />);
  }, []);

  const handleNavigateToRFID = useCallback(() => {
    navigate('RFID', {
      screen: 'RfidOrderReplacementCard',
      params: { userJourney: 'subs_upgrade' },
    });
  }, []);

  const handleNavigateToADACUnlink = useCallback(() => {
    navigate('PartnerDriver', {
      screen: 'LoadingOverlay',
      params: { isFromSubscription: true },
    });
  }, []);

  const handleNavigateToProfile = useCallback(() => {
    navigate('Dashboard', {
      screen: SubscriptionScreenNames.Dashboard,
      params: {},
    });
  }, []);

  const [config, setConfig] = useState<SubscriptionProviderWithEnv>({
    ...defaultConfig,
    onNavigateToRFID: handleNavigateToRFID,
    onNavigateToProfile: handleNavigateToProfile,
    onNavigateToADACUnlink: handleNavigateToADACUnlink,
    user,
    isInternetReachable: netInfo,
    cardComponent,
    navigation,
    environment: '',
  });

  return (
    <ConfigContext.Provider
      value={{
        tokenConfig,
        setTokenConfig,
        config,
        setConfig,
        setUser,
        user,
      }}
    >
      {children}
    </ConfigContext.Provider>
  );
};

export default ConfigProvider;
