import { WalletContextProvider } from '@bp/bppay-wallet-feature';
import React, { ReactElement, useCallback } from 'react';

import { SubscriptionScreenNames } from '../../src';
import { navigate, navigation } from '../utils/navigation';
import { useConfig } from './Config';
import { environmentConfigs } from '../config/enviromentConfig';

const Wallet = ({ children }: { children: ReactElement }) => {
  // Get MFE config from config context
  const { config } = useConfig();

  const handleAnalytics = useCallback(
    (...payload: any) => console.log('Subscription Analytics: ', payload),
    [],
  );

  const onPaymentComplete = useCallback(() => {
    navigate(SubscriptionScreenNames.WelcomeScreen, {}, false);
  }, []);

  const onMySubsPress = useCallback(() => {
    navigate('SubscriptionScreens', {
      screen: SubscriptionScreenNames.MySubscription,
      params: {},
    });
  }, []);

  const onExit = useCallback(() => {
    navigate('Dashboard', {
      screen: SubscriptionScreenNames.Dashboard,
      params: {},
    });
  }, []);

  return (
    <WalletContextProvider
      userId={config.user?.userId || ''}
      apiUrl={config.apiURL}
      apiKey={config.apiKey}
      micrositeUrl={config.micrositeUrl}
      locale={config.locale}
      country={config.user.country}
      authenticated
      overdrawnModalOnClick={() => console.log('Overdrawn modal clicked')}
      onPaymentCompleted={onPaymentComplete}
      onMySubsPress={onMySubsPress}
      onExit={onExit}
      getToken={config.getToken}
      navigation={navigation}
      onAnalyticsEvent={handleAnalytics}
      subsFlag={true}
    >
      {children}
    </WalletContextProvider>
  );
};

export default Wallet;
