import { RFIDProvider } from '@bp/rfid-mfe';
import { PaymentCard, useWallet } from '@bp/bppay-wallet-feature';
import React, { ReactElement, useCallback } from 'react';

import { useConfig } from './Config';
import { rfid_mfe } from '../config/defaults';
import { environmentConfigs } from '../config/enviromentConfig';
import { navigation } from '../utils/navigation';

const RFID = ({ children }: { children: ReactElement }) => {
  // Get MFE config from config context
  const { config, user } = useConfig();
  const { selectedCard } = useWallet();

  const handleAnalytics = useCallback(
    (...payload: any) => console.log('Subscription Analytics: ', payload),
    [],
  );

  return (
    <RFIDProvider
      onLogin={() => {}}
      paymentMethod={PaymentCard}
      onExitMFE={config.onExitMFE}
      brand="bp"
      featureFlags={rfid_mfe}
      userId={config.user?.userId || ''}
      apiURL={config.apiURL}
      apiKey={config.apiKey}
      locale={config.locale}
      country={user.country}
      geoLocationPermissionStatus={''}
      mapsApiKey={environmentConfigs[config.environment].mapsApiKey}
      hasDefaultPaymentMethod={!!selectedCard}
      userInfo={{ firstName: '', lastName: '' }}
      enableSoftOnboarding={false}
      tcsLink={'https://www.bppulse.co.uk/terms-and-conditions'}
      user={{ ...user, id: user.userId, addressLine1: '' }}
      overdrawnModalOnClick={() => console.log('Overdrawn modal clicked')}
      getToken={config.getToken}
      onAnalyticsEvent={handleAnalytics}
      navigation={navigation}
    >
      {children}
    </RFIDProvider>
  );
};

export default RFID;
