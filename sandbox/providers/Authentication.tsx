import { AuthProvider, CIPScope, SFConsent } from '@bp/pulse-auth-sdk';
import React from 'react';
import env from 'react-native-config';

interface Props {
  children: React.ReactNode;
}

const scopes = [
  CIPScope.OPEN_ID,
  CIPScope.EMAIL,
  CIPScope.PHONE,
  CIPScope.PROFILE,
  CIPScope.B2C_CONSENT,
  CIPScope.B2C_CONTACT,
  CIPScope.B2C_PROFILE,
];

const Render = ({ children }: Props) => {
  if (
    !env.CIP_BASE_URL ||
    !env.CIP_CLIENT_ID ||
    !env.CIP_REDIRECT_URL ||
    !env.MS_BASE_URL ||
    !env.MS_CLIENT_ID ||
    !env.MS_CLIENT_SECRET
  ) {
    const error = new Error('Missing secret property');
    console.error(error.message);
    throw error;
  }
  return (
    <AuthProvider
      baseUrl={env.CIP_BASE_URL}
      clientId={env.CIP_CLIENT_ID}
      callbackUrl={env.CIP_REDIRECT_URL}
      scopes={scopes}
      msBaseUrl={env.MS_BASE_URL}
      msClientId={env.MS_CLIENT_ID}
      msClientSecret={env.MS_CLIENT_SECRET}
      onLoginFailure={(e: string) => console.error('login error: ', e)}
      onRegistrationSuccess={() => {}}
      requiredConsents={[SFConsent.EV_TERMS_AND_CONDITIONS]}
    >
      {children}
    </AuthProvider>
  );
};

export default Render;
