import {
  WalletLinkingConfig,
  WalletScreenNames,
} from '@bp/bppay-wallet-feature';
import { createStackNavigator } from '@react-navigation/stack';
import React, { useCallback, useState } from 'react';

import {
  Screens as SubscriptionScreens,
  SubscriptionProvider,
  SubscriptionScreenNames,
} from '../src';
import Authentication from './providers/Authentication';
import Config, { useConfig } from './providers/Config';
import WalletProvider from './providers/Wallet';
import RFIDProvider from './providers/RFID';
import Tokens from './screens/TokenTransfer';
import Wallet from './screens/Wallet';
import RFID from './screens/RFID';
import { SafeAreaView } from 'react-native';
import Configurator from './screens/Configurator';
import { NavigationContainer } from '@react-navigation/native';
import { navigation } from './utils/navigation';
import Dashboard from './screens/Dashboard/Dashboard';
import MembershipOnboard from '../src/components/Organisms/MembershipOnboard/MembershipOnboard';

const Stack = createStackNavigator();

// Linking configuration for wallet
const linking = {
  prefixes: ['bppaywallet://'],
  config: {
    screens: {
      [WalletScreenNames.HostRoot]: WalletLinkingConfig,
    },
  },
};

const MFE = (props: any) => {
  const { config } = useConfig();

  const handleAnalytics = useCallback(
    (...payload: any) => console.log('Subscription Analytics: ', payload),
    [],
  );

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <NavigationContainer linking={linking} ref={navigation}>
        {config.environment !== '' ? (
          <SubscriptionProvider
            {...config}
            {...props}
            onAnalyticsEvent={handleAnalytics}
          >
            <WalletProvider>
              <RFIDProvider>
                <Stack.Navigator
                  initialRouteName={SubscriptionScreenNames.Dashboard}
                >
                  <Stack.Screen
                    name={SubscriptionScreenNames.Dashboard}
                    component={Dashboard}
                    options={{
                      headerShown: false,
                    }}
                  />
                  <Stack.Screen
                    name={'MembershipOnboard'}
                    component={MembershipOnboard}
                    options={{
                      headerShown: true,
                    }}
                  />
                  <Stack.Screen
                    name={'SubscriptionScreens'}
                    component={SubscriptionScreens}
                    options={{
                      headerShown: false,
                    }}
                  />

                  <Stack.Screen
                    name={WalletScreenNames.HostRoot}
                    component={Wallet}
                    options={{
                      headerShown: false,
                    }}
                  />

                  <Stack.Screen
                    name={'RFID'}
                    component={RFID}
                    options={{
                      headerShown: false,
                    }}
                  />
                </Stack.Navigator>
              </RFIDProvider>
            </WalletProvider>
          </SubscriptionProvider>
        ) : (
          <Configurator />
        )}
      </NavigationContainer>
    </SafeAreaView>
  );
};

const TokenApp = ({ setConfig }) => {
  return (
    <Authentication>
      <Tokens setConfig={setConfig} />
    </Authentication>
  );
};

export const App = () => {
  const [tokenConfig, setTokenConfig] = useState<boolean>(false);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      {tokenConfig ? (
        <TokenApp setConfig={setTokenConfig} />
      ) : (
        <Config tokenConfig={tokenConfig} setTokenConfig={setTokenConfig}>
          <MFE />
        </Config>
      )}
    </SafeAreaView>
  );
};
