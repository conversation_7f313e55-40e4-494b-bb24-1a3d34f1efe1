import env from 'react-native-config';

import { users } from './users/index';

const {
  DEV_API_GATEWAY_KEY,
  TEST_API_GATEWAY_KEY,
  PREPROD_API_GATEWAY_KEY,
  LOCAL_WALLET_MICROSITE,
  LOCAL_NEW_WALLET_MICROSITE,
  TEST_WALLET_MICROSITE,
  TEST_NEW_WALLET_MICROSITE,
  DEV_WALLET_MICROSITE,
  DEV_NEW_WALLET_MICROSITE,
  PREPROD_WALLET_MICROSITE,
  PREPROD_NEW_WALLET_MICROSITE,
  PREPROD_WALLET_API_URL,
  LOCAL_WALLET_API_URL,
  DEV_WALLET_API_URL,
  TEST_WALLET_API_URL,
  LOCAL_GATEWAY_URL_PUBLIC,
  DEV_GATEWAY_URL_PUBLIC,
  TEST_GATEWAY_URL_PUBLIC,
  PREPROD_GATEWAY_URL_PUBLIC,
  LOCAL_GATEWAY_URL_PRIVATE,
  DEV_GATEWAY_URL_PRIVATE,
  TEST_GATEWAY_URL_PRIVATE,
  PREPROD_GATEWAY_URL_PRIVATE,
  G_MAPS_KEY,
} = env;

export const environmentConfigs = {
  LOCAL: {
    mapsApiKey: G_MAPS_KEY,
    privateGateway: LOCAL_GATEWAY_URL_PRIVATE,
    publicGateway: LOCAL_GATEWAY_URL_PUBLIC,
    apiKey: {
      UK: 'xxxx',
      DE: 'xxxx',
    },
    walletMicrositeUrl: [
      { label: 'LOCAL', value: LOCAL_WALLET_MICROSITE },
      { label: 'LOCAL_NEW', value: LOCAL_NEW_WALLET_MICROSITE },
    ],
    walletApiUrl: LOCAL_WALLET_API_URL,
    accounts: {
      UK: users.UK.LOCAL,
      DE: users.DE.LOCAL,
    },
  },
  DEV: {
    mapsApiKey: G_MAPS_KEY,
    privateGateway: DEV_GATEWAY_URL_PRIVATE,
    publicGateway: DEV_GATEWAY_URL_PUBLIC,
    apiKey: {
      UK: DEV_API_GATEWAY_KEY,
      DE: DEV_API_GATEWAY_KEY,
    },
    walletApiUrl: DEV_WALLET_API_URL,
    walletMicrositeUrl: [
      { label: 'DEV', value: DEV_WALLET_MICROSITE },
      { label: 'DEV_NEW', value: DEV_NEW_WALLET_MICROSITE },
    ],
    accounts: {
      UK: users.UK.DEV,
      DE: users.DE.DEV,
    },
  },
  TEST: {
    mapsApiKey: G_MAPS_KEY,
    privateGateway: TEST_GATEWAY_URL_PRIVATE,
    publicGateway: TEST_GATEWAY_URL_PUBLIC,
    apiKey: {
      UK: TEST_API_GATEWAY_KEY,
      DE: TEST_API_GATEWAY_KEY,
    },
    walletApiUrl: TEST_WALLET_API_URL,
    walletMicrositeUrl: [
      { label: 'TEST', value: TEST_WALLET_MICROSITE },
      { label: 'TEST_NEW', value: TEST_NEW_WALLET_MICROSITE },
    ],
    accounts: {
      UK: users.UK.TEST,
      DE: users.DE.TEST,
    },
  },
  PREPROD: {
    mapsApiKey: G_MAPS_KEY,
    privateGateway: PREPROD_GATEWAY_URL_PRIVATE,
    publicGateway: PREPROD_GATEWAY_URL_PUBLIC,
    apiKey: {
      UK: PREPROD_API_GATEWAY_KEY,
      DE: PREPROD_API_GATEWAY_KEY,
    },
    walletApiUrl: PREPROD_WALLET_API_URL,
    walletMicrositeUrl: [
      { label: 'PREPROD', value: PREPROD_WALLET_MICROSITE },
      { label: 'PREPROD_NEW', value: PREPROD_NEW_WALLET_MICROSITE },
    ],
    accounts: {
      UK: users.UK.PREPROD,
      DE: users.DE.PREPROD,
    },
  },
};
