import axios from 'axios';
import querystring from 'query-string';
import env from 'react-native-config';

export const transferToken = async (
  countryCode: string,
  feature: string,
  refreshToken: string,
  userId: string,
) => {
  if (!env.TEST_TOKEN_API_KEY || !env.TEST_TOKEN_ENDPOINT) {
    throw new Error('Missing TEST_TOKEN_ENDPOINT or TEST_TOKEN_API_KEY');
  }

  if (!countryCode || !refreshToken || !userId) {
    throw new Error('Missing countryCode, refreshToken or userId');
  }

  const headers = {
    'x-api-key': env.TEST_TOKEN_API_KEY,
  };

  const queryParams = querystring.stringify({
    countryCode,
    feature,
    refreshToken,
    userId,
    forceNew: 'true',
  });

  try {
    const response = await axios.get(
      `${env.TEST_TOKEN_ENDPOINT}?${queryParams}`,
      {
        headers,
      },
    );

    return response.data;
  } catch (e) {
    console.error('Error transferring token:', JSON.stringify(e, null, 2));
    throw e;
  }
};
