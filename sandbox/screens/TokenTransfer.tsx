import { useAuth } from '@bp/pulse-auth-sdk';
import React, { useEffect } from 'react';
import { useCallback, useState } from 'react';
import SecureStorage from 'react-native-encrypted-storage';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { transferToken } from '../services/transferToken';
import env from 'react-native-config';

const REFRESH_TOKEN_SECURE_STORAGE_KEY = `${env.CIP_CLIENT_ID}_CIP_refresh_token`;

const Render = ({ setConfig }) => {
  const { loginOrRegister, logout, authenticated, user } = useAuth();
  const [refreshToken, setRefreshToken] = useState('');
  const [email, setEmail] = useState('');
  const [userId, setUserId] = useState('');
  const [feature, setFeature] = useState('');
  const [countryCode, setCountryCode] = useState('');
  const [isTransferable, setIsTransferable] = useState(false);
  const [isTestUser, setIsTestUser] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    async function fetchRefreshToken() {
      const token = await SecureStorage.getItem(
        REFRESH_TOKEN_SECURE_STORAGE_KEY,
      );
      setRefreshToken(token || '');
    }

    if (authenticated) {
      fetchRefreshToken();
    }
  }, [authenticated]);

  useEffect(() => {
    if (refreshToken) {
      console.log('refresh token, ', refreshToken);
    }
  }, [refreshToken]);

  useEffect(() => {
    setEmail(user?.email || '');
    setUserId(user?.userId || '');
  }, [user?.email, user?.userId]);

  useEffect(() => {
    if (countryCode && email && refreshToken && userId) {
      setIsTransferable(true);
    }
  }, [countryCode, email, refreshToken, userId]);

  useEffect(() => {
    if (email) {
      const regex =
        /pulseqausers\+(?<feature>[a-z]+)?_?(?<countryCode>[a-z]{2})_\d{1,3}@gmail\.com/i;
      const match = regex.exec(email);

      if (match?.groups) {
        setIsTestUser(true);
        setFeature(match.groups.feature?.toUpperCase() || '');
        setCountryCode(match.groups.countryCode?.toUpperCase() || '');
      }
    }
  }, [email]);

  const resetState = () => {
    setRefreshToken('');
    setEmail('');
    setUserId('');
    setFeature('');
    setCountryCode('');
    setIsTransferable(false);
    setIsTestUser(false);
  };

  const preventTokenInvalidation = async () => {
    await SecureStorage.setItem(REFRESH_TOKEN_SECURE_STORAGE_KEY, '');
  };

  const onLogin = useCallback(async () => {
    try {
      setIsLoading(true);
      await loginOrRegister('en-GB');
    } catch (e) {
      console.error('Error in onLogin: ', JSON.stringify(e, null, 2));
    } finally {
      setIsLoading(false);
    }
  }, [loginOrRegister]);

  const onLogout = () => {
    try {
      setIsLoading(true);
      resetState();
      preventTokenInvalidation();
      setConfig(undefined);
      logout();
    } catch (e) {
      console.error('Error in onLogout: ', JSON.stringify(e, null, 2));
    } finally {
      setIsLoading(false);
    }
  };

  const onTransferToken = useCallback(async () => {
    try {
      setIsLoading(true);
      const res = await transferToken(
        countryCode,
        feature,
        refreshToken,
        userId,
      );
      console.log('token transferred', res);
      onLogout();
    } catch (e) {
      console.error('Error in onTransferToken: ', JSON.stringify(e, null, 2));
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [countryCode, feature, refreshToken, userId]);

  if (isLoading) {
    return (
      <View style={[styles.container, styles.center]}>
        <ActivityIndicator size="large" color="black" />
      </View>
    );
  }

  if (!authenticated || !refreshToken) {
    return (
      <View style={styles.container}>
        <View style={styles.topContainer}>
          <Text style={styles.header}>
            No refresh token found. Please log in.
          </Text>
        </View>

        <View style={styles.bottomContainer}>
          <TouchableOpacity style={styles.button} onPress={onLogin}>
            <Text style={styles.buttonText}>Log In</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.backButton} onPress={onLogout}>
            <Text style={styles.buttonText}>Back</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.topContainer}>
        <TextInput editable={false} style={styles.textInput} multiline={true}>
          <Text selectable>{refreshToken}</Text>
        </TextInput>
      </View>

      <View style={styles.bottomContainer}>
        {isTestUser && (
          <View>
            <Text style={styles.infoText}>
              Transferring to QA Lambda will invalidate the current refresh
              token and log you out once this process is completed.
            </Text>
            <TouchableOpacity
              style={[styles.button, !isTransferable && styles.buttonDisabled]}
              onPress={onTransferToken}
              disabled={!isTransferable}
            >
              <Text style={styles.buttonText}>Transfer to QA Lambda</Text>
            </TouchableOpacity>
          </View>
        )}

        <TouchableOpacity style={styles.button} onPress={onLogout}>
          <Text style={styles.buttonText}>Log Out</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const buttonStyles = {
  backgroundColor: 'darkblue',
  padding: 10,
  borderRadius: 5,
  marginVertical: 12,
};
const styles = StyleSheet.create({
  container: {
    height: '100%',
  },
  topContainer: {
    flex: 2,
    justifyContent: 'center',
  },
  bottomContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    marginBottom: 24,
  },
  header: {
    fontSize: 18,
    marginBottom: 24,
    marginTop: 48,
    textAlign: 'center',
  },
  button: {
    ...buttonStyles,
    backgroundColor: 'darkblue',
  },
  backButton: {
    ...buttonStyles,
    backgroundColor: 'red',
  },
  buttonDisabled: {
    backgroundColor: 'gray',
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    textAlign: 'center',
  },
  textInput: {
    width: '100%',
    borderColor: 'gray',
    borderWidth: 1,
    padding: 10,
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 12,
    textAlign: 'center',
  },
});

export default Render;
