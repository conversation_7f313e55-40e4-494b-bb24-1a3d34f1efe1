import React, { useCallback, useEffect, useState } from 'react';
import { Button, View, Text, Pressable, ScrollView } from 'react-native';
import { useNetInfo } from '@react-native-community/netinfo';
import { SupportedLocale } from '@bp/pulse-shared-types/lib/enums/SupportedLocale';
import { SupportedBrand as brands } from '@bp/pulse-shared-types/src/enums/SupportedBrands';
import env from 'react-native-config';

import { environmentConfigs } from '../config/enviromentConfig';
import { useConfig } from '../providers/Config';
import { IQAUser, users } from '../config/users';
import { navigate, navigation } from '../utils/navigation';
import axios from 'axios';
import { PaymentMethod } from '../../src/common/interfaces';
import { PaymentCard } from '@bp/bppay-wallet-feature';
import { SubscriptionScreenNames, UserCountry } from '../../src/common/enums';
import { SFConsentUpdate } from '@bp/pulse-auth-sdk';

const mappedEnvironments = Object.keys(environmentConfigs).map((val) => val);
const availableCountries = [UserCountry.UK, UserCountry.DE];
const { TOKEN_URL, TOKEN_API_KEY } = env;

// Get generic UK user token - pass no feature in request
const getQaUserToken = async (user: IQAUser) => {
  try {
    const { data } = await axios.get(TOKEN_URL, {
      headers: { 'x-api-key': TOKEN_API_KEY },
      params: {
        userId: user.userId,
        countryCode: user.country,
        feature: user.feature,
      },
    });
    return data.token;
  } catch (err) {
    throw new Error(`Error on get user token: ${err}`);
  }
};

const Render = () => {
  const { setConfig, setTokenConfig, user, setUser } = useConfig();
  const netInfo = useNetInfo().isConnected;
  const [environment, setEnvironment] = useState('DEV');
  const [micrositeUrl, setMicrositeUrl] = useState('');
  const [brand, setBrand] = useState(brands[0]);
  const [selectedUser, setSelectedUser] = useState<IQAUser>(user);
  const [cardComponent, setCardComponent] = useState<PaymentMethod>();
  const [country, setCountry] = useState(availableCountries[1]);

  useEffect(() => {
    setCardComponent(<PaymentCard isSubsDefault />);
  }, []);

  useEffect(() => {
    if (country == UserCountry.DE) {
      setBrand(brands.ARAL);
    }
  }, [country, environment]);

  const handleAnalytics = useCallback(
    (...payload) => console.log('SubscriptionMFE Analytics: ', payload),
    [],
  );

  const handleNavigateToRFID = useCallback(() => {
    navigate('RFID', {
      screen: 'RfidOrderReplacementCard',
      params: { userJourney: 'subs_upgrade' },
    });
  }, []);

  const handleNavigateToProfile = useCallback(() => {
    navigate('Dashboard', {
      screen: SubscriptionScreenNames.Dashboard,
      params: {},
    });
  }, []);

  const handleNavigateToADACUnlink = useCallback(() => {
    navigate('PartnerDriver', {
      screen: 'LoadingOverlay',
      params: { isFromSubscription: true },
    });
  }, []);

  // Set the MFE experience config
  const onSetConfig = () => {
    setUser(selectedUser);
    setConfig({
      emspTermsAndConditions:
        'https://www.bppulse.com/en-gb/legal-terms-and-conditions/introduction',
      tariffPricing:
        country == UserCountry.DE
          ? 'https://www.aral.de/de/global/retail/pulse/tarife-bezahlmethoden.html'
          : 'https://www.bppulse.co.uk/charging-on-the-go.html#1g3yg1IElAUnZJBFA3ZW79',
      locale: SupportedLocale.EN_GB,
      navigation,
      apiKey: environmentConfigs[environment].apiKey.UK,
      apiURL: environmentConfigs[environment].privateGateway,
      micrositeUrl: micrositeUrl,
      getToken: async () => await getQaUserToken(selectedUser),
      featureFlags: {
        block_subscription_upgrade_during_migration: false,
        enableInvoicesList: true,
        enableOfferDetails: true,
        enableRFID: false,
        extraTariffDiscountPercentage: 10,
        introOfferCredit: 0,
        introOfferCreditDuration: 0,
        introPercentageDiscount: 25,
        subsChargingRatesPromo: 45,
        disableIntroOffer: false,
        savingRate: 55,
        'subsChargeRateAC<22kW': 0.42,
        'subsChargeRateDC<50kW': 0.46,
        'subsChargeRateDC>50kW': 0.54,
        subscriptionAmount: 7.49,
        subsDrivingDistancekm: '100+',
        subsChargekWh: '20',
        pollingTimeout: 36000,
        enableThreeDsInCreateSubscription: false,
      },
      onExitMFE: () => null,
      onRequestToStartCharge: () => null,
      onNavigateToRFID: handleNavigateToRFID,
      onNavigateToProfile: handleNavigateToProfile,
      onNavigateToADACUnlink: handleNavigateToADACUnlink,
      onPaymentRequired: () => null,
      user: selectedUser,
      onAnalyticsEvent: handleAnalytics,
      isInternetReachable: netInfo,
      cardComponent,
      environment,
      brand,
      consents: [],
      updateConsents: (_: SFConsentUpdate[]) => {},
      requiredConsents: [],
    });
  };

  const onTokenBtnPress = () => {
    setTokenConfig(true);
  };

  return (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ marginTop: 16, width: '100%', alignItems: 'center' }}>
        <Text style={{ fontSize: 20, fontWeight: 'bold' }}>
          Configure Subscription MFE
        </Text>
      </View>

      <View style={{ marginLeft: 15, marginRight: 15, marginTop: 20 }}>
        <Text>Select brand:</Text>
        {Object.values(brands).map((val) => (
          <Pressable key={val} onPress={() => setBrand(val)}>
            <Text
              style={{ fontWeight: val === brand ? 'bold' : 'normal' }}
              testID={`Brand-${val}`}
            >
              {val}
            </Text>
          </Pressable>
        ))}
      </View>

      <View style={{ marginLeft: 15, marginRight: 15, marginTop: 20 }}>
        <Text>Select Country:</Text>
        {availableCountries.map((val) => (
          <Pressable
            key={val}
            onPress={() => {
              setCountry(val);
            }}
            accessibilityLabel={val}
          >
            <Text
              style={{ fontWeight: val === country ? 'bold' : 'normal' }}
              testID={`Country-${val}`}
            >
              {val}
            </Text>
          </Pressable>
        ))}
      </View>
      <View style={{ marginLeft: 15, marginRight: 15, marginTop: 20 }}>
        <Text>Select Environment:</Text>
        {mappedEnvironments.map((val) => (
          <Pressable
            key={val}
            onPress={() => {
              setEnvironment(val);
            }}
            accessibilityLabel={val}
          >
            <Text
              style={{ fontWeight: val === environment ? 'bold' : 'normal' }}
              testID={`Environment-${val}`}
            >
              {val}
            </Text>
          </Pressable>
        ))}
      </View>

      <View style={{ marginLeft: 15, marginRight: 15, marginTop: 20 }}>
        <Text>Select Microsite Url:</Text>
        {environmentConfigs[environment].walletMicrositeUrl.map((val) => (
          <Pressable
            key={val.value}
            onPress={() => {
              setMicrositeUrl(val.value);
            }}
          >
            <Text
              style={{
                fontWeight: val.value === micrositeUrl ? 'bold' : 'normal',
              }}
              testID={`MicrositeUrl-${val.label}`}
            >
              {val.label}
            </Text>
          </Pressable>
        ))}
      </View>

      <View style={{ marginLeft: 15, marginRight: 15, marginTop: 20 }}>
        <Text>Select User:</Text>
        {users[country][environment].map(
          (val) =>
            val.country === country && (
              <Pressable
                key={val.userId}
                onPress={() => setSelectedUser(val)}
                accessibilityLabel={val.userId}
              >
                <Text
                  style={{
                    fontWeight: val === selectedUser ? 'bold' : 'normal',
                  }}
                  testID={`User-${val.userId}`}
                >
                  {val.userId} - {val.type}
                </Text>
              </Pressable>
            ),
        )}
      </View>
      <View
        style={{
          alignSelf: 'center',
          marginLeft: 15,
          marginRight: 15,
          marginTop: 20,
        }}
      >
        <Text>Selected Environment: {environment || 'none'}</Text>
        <Text>Selected User: {selectedUser.userId || 'none'}</Text>
      </View>
      <View style={{ marginTop: 20 }}>
        <Button
          accessibilityLabel="Confirm"
          disabled={selectedUser.userId === '' || !environment || !country}
          onPress={onSetConfig}
          title="Confirm Config"
          testID="ConfirmConfig"
        />
        <Button
          testID="transferTokens"
          onPress={onTokenBtnPress}
          title="Transfer QA tokens"
        />
      </View>
    </ScrollView>
  );
};

export default Render;
