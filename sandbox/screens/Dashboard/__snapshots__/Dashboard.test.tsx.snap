// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Example component should render correctly 1`] = `
<View
  style={
    Object {
      "alignItems": "center",
      "flex": 1,
    }
  }
>
  <View
    style={
      Object {
        "marginTop": 100,
      }
    }
  >
    <Text
      style={
        Object {
          "fontSize": 20,
          "fontWeight": "bold",
        }
      }
    >
      Select user:
    </Text>
  </View>
  <View
    style={
      Array [
        Object {
          "marginTop": 20,
        },
      ]
    }
  >
    <View
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      testID="pickGoCardless"
    >
      <Text>
        0058E00000Ad8b6QAB
      </Text>
    </View>
  </View>
  <View
    style={
      Array [
        Object {
          "marginTop": 20,
        },
      ]
    }
  >
    <View
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      testID="pickGoCardless"
    >
      <Text>
        0058E00000AEUTIQA5
      </Text>
    </View>
  </View>
  <View
    style={
      Object {
        "marginTop": 50,
      }
    }
  >
    <Text
      style={
        Object {
          "fontSize": 20,
          "fontWeight": "bold",
        }
      }
    >
      Select journey:
    </Text>
  </View>
  <View
    style={
      Array [
        Object {
          "marginTop": 20,
        },
      ]
    }
  >
    <View
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      testID="pickGoCardless"
    >
      <Text>
        Landing screen (spinner)
      </Text>
    </View>
  </View>
  <View
    style={
      Array [
        Object {
          "marginTop": 20,
        },
      ]
    }
  >
    <View
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      testID="pickGoCardless"
    >
      <Text>
        Your Membership (new)
      </Text>
    </View>
  </View>
  <View
    style={
      Array [
        Object {
          "marginTop": 20,
        },
      ]
    }
  >
    <View
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      testID="pickGoCardless"
    >
      <Text>
        Your Membership (returning)
      </Text>
    </View>
  </View>
  <View
    style={
      Array [
        Object {
          "marginTop": 20,
        },
      ]
    }
  >
    <View
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      testID="pickWelcome"
    >
      <Text>
        Welcome to Membership
      </Text>
    </View>
  </View>
  <View
    style={
      Array [
        Object {
          "marginTop": 20,
        },
      ]
    }
  >
    <View
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      testID="pickYourMembership"
    >
      <Text>
        Your Membership (active)
      </Text>
    </View>
  </View>
  <View
    style={
      Array [
        Object {
          "marginTop": 20,
        },
      ]
    }
  >
    <View
      accessible={true}
      collapsable={false}
      focusable={true}
      onBlur={[Function]}
      onClick={[Function]}
      onFocus={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      testID="pickCancelMembership"
    >
      <Text>
        Cancel Membership
      </Text>
    </View>
  </View>
</View>
`;
