/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import * as S from './Dashboard.styles';
import { useConfig } from '../../providers/Config';
import {
  SubscriptionScreenNames,
  UserTypeEnum,
} from '../../../src/common/enums';
import { SupportedCountries } from '@bp/rfid-mfe';
import { WalletScreenNames } from '@bp/bppay-wallet-feature';

const Render = (props: any) => {
  const { navigation } = props;
  const { user, config, setConfig, setUser } = useConfig();

  const onExit = () => {
    const nullUser = {
      userId: '',
      feature: '',
      country: 'UK' as SupportedCountries,
      type: '',
    };
    setUser(nullUser);
    setConfig({ ...config, user: nullUser, environment: '' });
  };

  return (
    <View style={{ flex: 1, alignItems: 'center' }}>
      <View style={{ marginTop: 16 }}>
        <Text style={{ fontSize: 20, fontWeight: 'bold' }}>
          Flows in SubscriptionMFE
        </Text>
      </View>
      <S.Screen>
        <Pressable
          style={{ marginTop: 20 }}
          testID="Membership Onboard"
          onPress={() =>
            navigation.navigate('MembershipOnboard', {
              screen: SubscriptionScreenNames.MembershipOnboard,
              params: {
                title: SubscriptionScreenNames.MembershipOnboard,
                headerStyle: {
                  backgroundColor: '#10109a',
                  shadowColor: 'transparent',
                },
                headerTitleAlign: 'center',
                headerTintColor: '#fff',
              },
            })
          }
        >
          <S.BlueText>Membership Onboard</S.BlueText>
        </Pressable>
        <Pressable
          style={{ marginTop: 20 }}
          testID="My Comfort Membership"
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.MyComfortMembership,
              params: {},
            })
          }
        >
          <S.BlueText>My Comfort Membership</S.BlueText>
        </Pressable>
        <Pressable
          style={{ marginTop: 20 }}
          testID="Subscription Ending"
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.SubscriptionEnding,
              params: {},
            })
          }
        >
          <S.BlueText>Subscription Ending</S.BlueText>
        </Pressable>
        <Pressable
          style={{ marginTop: 20 }}
          testID="normalFlow"
          onPress={() => navigation.navigate('SubscriptionScreens')}
        >
          <S.BlueText>Normal flow</S.BlueText>
        </Pressable>

        <Pressable
          style={{ marginTop: 20 }}
          testID="SubscriptionFailed"
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.SubscriptionFailed,
              params: {},
            })
          }
        >
          <S.BlueText>SubscriptionFailed</S.BlueText>
        </Pressable>

        {(user.type === UserTypeEnum.PAYG_WALLET ||
          user.type === UserTypeEnum.SUBS_WALLET) && (
          <Pressable
            style={{ marginTop: 20 }}
            testID="confirmSubscription"
            onPress={() =>
              navigation.navigate('SubscriptionScreens', {
                screen: SubscriptionScreenNames.ConfirmSubscription,
                params: {},
              })
            }
          >
            <S.BlueText>ConfirmSubscription</S.BlueText>
          </Pressable>
        )}

        {user.type === UserTypeEnum.SUBS_WALLET && (
          <Pressable
            style={{ marginTop: 20 }}
            testID="cancelMembership"
            onPress={() =>
              navigation.navigate('SubscriptionScreens', {
                screen: SubscriptionScreenNames.CancelMembership,
                params: {},
              })
            }
          >
            <S.BlueText>CancelMembership</S.BlueText>
          </Pressable>
        )}

        {user.type === UserTypeEnum.SUBS_WALLET && (
          <Pressable
            style={{ marginTop: 20 }}
            testID="mySubscription"
            onPress={() =>
              navigation.navigate('SubscriptionScreens', {
                screen: SubscriptionScreenNames.MySubscription,
                params: {},
              })
            }
          >
            <S.BlueText>MySubscription</S.BlueText>
          </Pressable>
        )}

        {user.type === UserTypeEnum.SUBS_WALLET && (
          <Pressable
            style={{ marginTop: 20 }}
            testID="subscriptionSuccess"
            onPress={() =>
              navigation.navigate('SubscriptionScreens', {
                screen: SubscriptionScreenNames.SubscriptionSuccess,
                params: {},
              })
            }
          >
            <S.BlueText>SubscriptionSuccess</S.BlueText>
          </Pressable>
        )}

        <Pressable
          style={{ marginTop: 20 }}
          testID="CancelSubscriptionFailed"
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.CancelSubscriptionFailed,
              params: {},
            })
          }
        >
          <S.BlueText>CancelSubscriptionFailed</S.BlueText>
        </Pressable>

        <Pressable
          style={{ marginTop: 20 }}
          testID="CancelSubscriptionConfirmation"
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.CancelSubscriptionConfirmation,
              params: {},
            })
          }
        >
          <S.BlueText>CancelSubscriptionConfirmation</S.BlueText>
        </Pressable>

        {user.type === UserTypeEnum.SUBS_WALLET && (
          <Pressable
            style={{ marginTop: 20 }}
            testID="walletPaymentDetails"
            onPress={() =>
              navigation.navigate('Wallet', {
                screen: WalletScreenNames.PaymentDetails,
                params: {},
              })
            }
          >
            <S.BlueText>WalletPaymentDetails</S.BlueText>
          </Pressable>
        )}
        <Pressable
          style={{ marginTop: 20 }}
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.AdacCancelSubsConfirmation,
              params: {},
            })
          }
        >
          <S.BlueText>ADACCancelSubsConfirmation</S.BlueText>
        </Pressable>
        <Pressable
          style={{ marginTop: 20 }}
          testID="paymentSuccess"
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.PaymentSuccess,
              params: {},
            })
          }
        >
          <S.BlueText>PaymentSuccess</S.BlueText>
        </Pressable>
        <Pressable
          style={{ marginTop: 20 }}
          testID="paymentFailed"
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.PaymentFailed,
              params: {},
            })
          }
        >
          <S.BlueText>PaymentFailed</S.BlueText>
        </Pressable>
        <Pressable
          style={{ marginTop: 20 }}
          testID="shippingAddress"
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.ShippingAddress,
              params: {},
            })
          }
        >
          <S.BlueText>ShippingAddress</S.BlueText>
        </Pressable>
        <Pressable
          style={{ marginTop: 20 }}
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.ConfirmCancelComfortMembership,
              params: {},
            })
          }
        >
          <S.BlueText>ConfirmCancelComfortMembership</S.BlueText>
        </Pressable>

        <Pressable
          style={{ marginTop: 20 }}
          testID="myComfortMembershipActivation"
          onPress={() =>
            navigation.navigate('SubscriptionScreens', {
              screen: SubscriptionScreenNames.MyComfortMembershipActivation,
              params: {},
            })
          }
        >
          <S.BlueText>MyComfortMembershipActivation</S.BlueText>
        </Pressable>
        <Pressable
          style={{ marginTop: 20 }}
          testID="exit"
          onPress={() => onExit()}
        >
          <S.RedText>Exit</S.RedText>
        </Pressable>
      </S.Screen>
    </View>
  );
};

export default Render;
