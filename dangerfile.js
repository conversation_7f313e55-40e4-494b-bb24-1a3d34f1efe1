import dangerFileBase from '@bp/pulse-mobile-sdk/dangerfile-base';
import {
  danger,
  fail,
  markdown,
  message,
  results,
  schedule,
  warn,
} from 'danger';

const fs = require('fs');
const path = require('path');

// Report written to file to allow publishing to azure PRs
const writeReportToJSON = () => {
  fs.writeFileSync(
    path.resolve(process.env.DANGER_REPORT_PATH),
    JSON.stringify(results, null, 2),
  );
};

dangerFileBase({ danger, warn, fail, schedule, results, message, markdown });

writeReportToJSON();
